#!/bin/bash
set -e

# Cores para saída
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Carregar variáveis do arquivo .env se existir
if [ -f ".env" ]; then
  echo -e "${BLUE}🔄 Carregando variáveis de ambiente do arquivo .env...${NC}"
  export $(grep -v '^#' .env | xargs)
  echo -e "${GREEN}✅ Variáveis de ambiente carregadas com sucesso!${NC}"
else
  echo -e "${YELLOW}⚠️ Arquivo .env não encontrado. Usando variáveis de ambiente do sistema.${NC}"
fi

# Verificar se o Docker está rodando
echo -e "${BLUE}🔍 Verificando o Docker...${NC}"
if ! docker info >/dev/null 2>&1; then
  echo -e "${RED}❌ Docker não está rodando ou você não tem permissões suficientes${NC}"
  exit 1
fi

# Verificar se o Encore CLI está instalado
echo -e "${BLUE}🔍 Verificando o Encore CLI...${NC}"
if ! command -v encore &> /dev/null; then
  echo -e "${RED}❌ Encore CLI não está instalado. Por favor, instale-o com:${NC}"
  echo "curl -L https://encore.dev/install.sh | bash"
  exit 1
fi

# Verificar se o arquivo infra.config.json existe
INFRA_CONFIG_FILE="./infra.config.json"
if [ ! -f "${INFRA_CONFIG_FILE}" ]; then
    echo -e "${RED}❌ Arquivo de configuração ${INFRA_CONFIG_FILE} não encontrado.${NC}"
    echo -e "${YELLOW}Este script espera que você tenha um arquivo infra.config.json na raiz do projeto.${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Usando arquivo de configuração existente: ${INFRA_CONFIG_FILE}${NC}"


# Configurações
DOCKER_USERNAME="${DOCKER_USERNAME:-pluggou}" # Será substituído pelo usuário atual do Docker se já estiver logado
APP_NAME="pixapi"
TAG="v0.1.16"

# Solicitar ou usar variáveis de ambiente padrão para o build
echo -e "${BLUE}🔧 Configurando variáveis de ambiente para o build (usadas em .secrets.local.cue)...${NC}"

# Variáveis de banco de dados (usadas para .secrets.local.cue e como defaults)
# Usando o banco de dados Neon
BUILD_DB_HOST="${DB_HOST:-ep-muddy-darkness-acfy40ru-pooler.sa-east-1.aws.neon.tech}"
BUILD_DB_USER="${DB_USER:-pixdb_owner}"
BUILD_DB_PASSWORD="${DB_PASSWORD:-npg_Ta9xPfFXGCv6}" # Shell var para senha no build
BUILD_DB_NAME="${DB_NAME:-pixdb}"
BUILD_DB_CONNECTION_STRING="${DB_CONNECTION_STRING:-postgresql://pixdb_owner:<EMAIL>/pixdb?sslmode=require}"

# Variáveis legadas do RDS (mantidas para compatibilidade)
BUILD_RDS_ENDPOINT="${RDS_ENDPOINT:-your-rds-endpoint.region.rds.amazonaws.com}"
BUILD_RDS_USERNAME="${RDS_USERNAME:-admin}"
BUILD_RDS_PASSWORD="${RDS_PASSWORD:-your-secure-password}" # Shell var para senha no build

# Variáveis da Flow2Pay API (usadas para .secrets.local.cue)
BUILD_FLOW2PAY_CLIENT_ID="${FLOW2PAY_CLIENT_ID:-cliente_temporario}"
BUILD_FLOW2PAY_CLIENT_SECRET="${FLOW2PAY_CLIENT_SECRET:-segredo_temporario}"
BUILD_FLOW2PAY_EVENT_TOKEN="${FLOW2PAY_EVENT_TOKEN:-token_temporario}"
BUILD_ADMIN_API_KEY="${ADMIN_API_KEY:-admin_key_segura_temporaria}"

# Variáveis para NSQ (PubSub) (usadas para .secrets.local.cue)
BUILD_NSQ_HOST="${NSQ_HOST:-nsqd}"
BUILD_NSQ_PORT="${NSQ_PORT:-4150}"

# Base URL da API (usada para .secrets.local.cue)
BUILD_API_BASE_URL="${API_BASE_URL:-https://apipix.cloud.pluggou.io}" # Default para produção

echo -e "${GREEN}✅ Configurações de build carregadas com sucesso${NC}"

# Criar arquivo .secrets.local.cue temporário para o build local
# Este arquivo fornece valores para {$env: VAR_NAME} no SEU infra.config.json DURANTE o `encore build docker`
echo -e "${BLUE}📝 Criando arquivo de secrets temporário para build local (.secrets.local.cue)...${NC}"
cat > .secrets.local.cue << EOF
API_BASE_URL: "${BUILD_API_BASE_URL}"
# Neon Database
NEON_DB_PASSWORD: "${BUILD_DB_PASSWORD}"
NEON_DB_CONNECTION_STRING: "${BUILD_DB_CONNECTION_STRING}"
POSTGRES_PASSWORD: "${BUILD_DB_PASSWORD}"
# Legacy RDS (mantido para compatibilidade)
RDS_ENDPOINT: "${BUILD_RDS_ENDPOINT}"
RDS_USERNAME: "${BUILD_RDS_USERNAME}"
RDS_PASSWORD: "${BUILD_RDS_PASSWORD}"
# NSQ
NSQD_TCP_ADDR: "${BUILD_NSQ_HOST}:${BUILD_NSQ_PORT}"
# Flow2Pay
FLOW2PAY_CLIENT_ID: "${BUILD_FLOW2PAY_CLIENT_ID}"
FLOW2PAY_CLIENT_SECRET: "${BUILD_FLOW2PAY_CLIENT_SECRET}"
FLOW2PAY_EVENT_TOKEN: "${BUILD_FLOW2PAY_EVENT_TOKEN}"
ADMIN_API_KEY: "${BUILD_ADMIN_API_KEY}"
EOF

# Definir variável para controlar se devemos inicializar o banco de dados
SKIP_DB_INIT=${SKIP_DB_INIT:-false}

# Verificar se devemos pular a inicialização do banco de dados
if [ "$SKIP_DB_INIT" = "true" ]; then
  echo -e "${BLUE}🔄 Pulando a inicialização do banco de dados e migrações conforme solicitado (SKIP_DB_INIT=true)${NC}"
  echo -e "${YELLOW}⚠️ Certifique-se de que o banco de dados será inicializado no ambiente de destino (Coolify)${NC}"
else
  # Testar conexão com o banco de dados Neon
  echo -e "${BLUE}🔍 Testando conexão com o banco de dados Neon em ${BUILD_DB_HOST}...${NC}"

  # Exportar variáveis para o script de migração
  export DB_HOST="${BUILD_DB_HOST}"
  export DB_USER="${BUILD_DB_USER}"
  export DB_PASSWORD="${BUILD_DB_PASSWORD}"
  export DB_NAME="${BUILD_DB_NAME}"
  export DB_CONNECTION_STRING="${BUILD_DB_CONNECTION_STRING}"

  # Verificar se o script de migração para Neon existe
  if [ -f "./scripts/apply-neon-migrations.sh" ]; then
    echo -e "${BLUE}🔄 Aplicando migrações ao banco de dados Neon...${NC}"
    chmod +x ./scripts/apply-neon-migrations.sh
    ./scripts/apply-neon-migrations.sh

    if [ $? -ne 0 ]; then
      echo -e "${YELLOW}⚠️ Aviso: Falha ao aplicar migrações ao banco de dados Neon.${NC}"
      echo -e "${YELLOW}⚠️ Verifique as credenciais e a conectividade com o banco de dados.${NC}"
      echo -e "${YELLOW}⚠️ Continuando o build mesmo assim...${NC}"
    else
      echo -e "${GREEN}✅ Migrações aplicadas ao banco de dados Neon com sucesso!${NC}"
    fi
  else
    echo -e "${RED}❌ Script apply-neon-migrations.sh não encontrado no diretório scripts/.${NC}"
    echo -e "${YELLOW}⚠️ Verifique se o script existe e tem permissões de execução.${NC}"
    echo -e "${YELLOW}⚠️ Continuando o build sem aplicar migrações...${NC}"
  fi
fi

# Compilar a imagem Docker usando o comando encore build docker e o infra.config.json existente
echo -e "${BLUE}🔨 Compilando a imagem Docker usando Encore CLI com ${INFRA_CONFIG_FILE}...${NC}"
encore build docker --config=${INFRA_CONFIG_FILE} ${APP_NAME}:${TAG}

if [ $? -ne 0 ]; then
  echo -e "${RED}❌ Falha ao compilar a imagem Docker${NC}"
  echo -e "${YELLOW}Possível causa: Verifique se o arquivo ${INFRA_CONFIG_FILE} está correto e se todas as variáveis referenciadas com '\$env' nele estão definidas no arquivo .secrets.local.cue gerado.${NC}"
  echo -e "${YELLOW}O erro anterior sobre 'InfraConfig.pubsub.hosts' pode indicar um problema nesse campo no seu ${INFRA_CONFIG_FILE}.${NC}"
  rm -f .secrets.local.cue
  exit 1
fi

# Taguear a imagem para o Docker Hub
echo -e "${BLUE}🏷️  Marcando a imagem para o Docker Hub...${NC}"
docker tag "${APP_NAME}:${TAG}" "${DOCKER_USERNAME}/${APP_NAME}:${TAG}"
docker tag "${APP_NAME}:${TAG}" "${DOCKER_USERNAME}/${APP_NAME}:latest"

# Autenticação no Docker Hub
echo -e "${BLUE}🔑 Verificando autenticação no Docker Hub...${NC}"

# Verificar se o arquivo de configuração do Docker existe e tem credenciais
if [ -f "$HOME/.docker/config.json" ]; then
  # Verificar se há credenciais no arquivo
  if grep -q "auth" "$HOME/.docker/config.json"; then
    echo -e "${GREEN}✅ Credenciais do Docker encontradas${NC}"

    # Tentar obter o usuário atual
    if command -v jq &> /dev/null; then
      # Se jq estiver instalado, podemos tentar extrair o usuário
      DOCKER_SERVERS=$(jq -r '.auths | keys[]' "$HOME/.docker/config.json" 2>/dev/null || echo "")
      if [[ "$DOCKER_SERVERS" == *"docker.io"* ]] || [[ "$DOCKER_SERVERS" == *"index.docker.io"* ]]; then
        echo -e "${GREEN}✅ Autenticação para Docker Hub encontrada${NC}"
      fi
    fi

    # Verificar se o Docker está logado usando docker info
    if docker info 2>/dev/null | grep -q "Username"; then
      CURRENT_USER=$(docker info 2>/dev/null | grep "Username:" | cut -d' ' -f2)
      echo -e "${GREEN}✅ Usando autenticação existente no Docker Hub como ${CURRENT_USER}${NC}"

      # Atualizar a variável DOCKER_USERNAME para usar o usuário atual
      if [ "$CURRENT_USER" != "$DOCKER_USERNAME" ]; then
        echo -e "${YELLOW}⚠️ Ajustando configuração: alterando usuário de ${DOCKER_USERNAME} para ${CURRENT_USER}${NC}"
        DOCKER_USERNAME="$CURRENT_USER"

        # Atualizar as tags para usar o usuário correto
        docker tag "${APP_NAME}:${TAG}" "${DOCKER_USERNAME}/${APP_NAME}:${TAG}"
        docker tag "${APP_NAME}:${TAG}" "${DOCKER_USERNAME}/${APP_NAME}:latest"
      fi
    else
      echo -e "${BLUE}Usando credenciais salvas para ${DOCKER_USERNAME}${NC}"
    fi
  else
    echo -e "${YELLOW}⚠️ Nenhuma credencial encontrada no arquivo de configuração do Docker${NC}"
    echo -e "${BLUE}Por favor, faça login manualmente antes de executar este script:${NC}"
    echo -e "   docker login"
    echo -e "${BLUE}Depois execute este script novamente.${NC}"
    rm -f .secrets.local.cue
    exit 1
  fi
else
  echo -e "${YELLOW}⚠️ Arquivo de configuração do Docker não encontrado${NC}"
  echo -e "${BLUE}Por favor, faça login manualmente antes de executar este script:${NC}"
  echo -e "   docker login"
  echo -e "${BLUE}Depois execute este script novamente.${NC}"
  rm -f .secrets.local.cue
  exit 1
fi

# Fazer push da imagem para o Docker Hub
echo -e "${BLUE}📤 Enviando a imagem para o Docker Hub...${NC}"

echo -e "${BLUE}Enviando tag ${TAG}...${NC}"
docker push "${DOCKER_USERNAME}/${APP_NAME}:${TAG}"

if [ $? -ne 0 ]; then
  echo -e "${RED}❌ Falha ao enviar a imagem com tag ${TAG} para o Docker Hub${NC}"
  echo -e "${YELLOW}Verifique se você está autenticado corretamente no Docker Hub${NC}"
  rm -f .secrets.local.cue
  exit 1
fi

echo -e "${BLUE}Enviando tag latest...${NC}"
docker push "${DOCKER_USERNAME}/${APP_NAME}:latest"

if [ $? -ne 0 ]; then
  echo -e "${RED}❌ Falha ao enviar a imagem com tag latest para o Docker Hub${NC}"
  echo -e "${YELLOW}⚠️ A tag ${TAG} foi enviada com sucesso, mas a tag latest falhou.${NC}"
  rm -f .secrets.local.cue
  exit 1
fi

echo -e "${GREEN}✅ Imagem Docker buildada com sucesso e enviada para o Docker Hub:${NC}"
echo -e "${GREEN}  - ${DOCKER_USERNAME}/${APP_NAME}:${TAG}${NC}"
echo -e "${GREEN}  - ${DOCKER_USERNAME}/${APP_NAME}:latest${NC}"
echo ""
echo -e "${YELLOW}Instruções para rodar o container Docker da aplicação:${NC}"
echo "A imagem '${DOCKER_USERNAME}/${APP_NAME}:${TAG}' foi construída usando SEU arquivo ${INFRA_CONFIG_FILE}."
echo "As seguintes variáveis de ambiente são esperadas pelo SEU ${INFRA_CONFIG_FILE} em tempo de execução:"
echo ""
echo -e "${BLUE}Variáveis de Ambiente (baseadas no seu infra.config.json padrão):${NC}"
echo "  - ${YELLOW}PORT${NC}=<porta_que_a_app_encore_escuta_no_container> (ex: 4000)"
echo "  - ${YELLOW}API_BASE_URL${NC}=<url_base_publica_da_sua_api> (ex: https://apipix.cloud.pluggou.io)"
echo "  - ${YELLOW}NEON_DB_PASSWORD${NC}=<senha_do_neon> (sua senha segura do Neon)"
echo "  - ${YELLOW}NEON_DB_CONNECTION_STRING${NC}=<string_de_conexao_completa> (ex: postgresql://pixdb_owner:<EMAIL>/pixdb?sslmode=require)"
echo "  - ${YELLOW}DATABASE_URL${NC}=<string_de_conexao_completa> (mesma string de conexão do Neon)"
echo "  - ${YELLOW}NSQD_TCP_ADDR${NC}=<host_do_nsqd>:<porta_tcp_do_nsqd> (ex: nsqd-servico:4150)"
echo "  - ${YELLOW}FLOW2PAY_CLIENT_ID${NC}=<seu_client_id_flow2pay>"
echo "  - ${YELLOW}FLOW2PAY_CLIENT_SECRET${NC}=<seu_client_secret_flow2pay>"
echo "  - ${YELLOW}FLOW2PAY_EVENT_TOKEN${NC}=<seu_event_token_flow2pay>"
echo "  - ${YELLOW}ADMIN_API_KEY${NC}=<sua_chave_admin_segura>"
echo ""
echo "Lembre-se de que o banco de dados Neon e os serviços NSQ devem estar rodando e acessíveis pela rede."
echo "Ao usar com docker-compose, defina estas variáveis de ambiente na seção 'environment' do serviço da sua aplicação Encore."
echo ""
echo -e "${GREEN}Arquivo .secrets.local.cue foi usado para o build e pode ser removido se desejar.${NC}"
# rm -f .secrets.local.cue # Descomente para remover automaticamente
