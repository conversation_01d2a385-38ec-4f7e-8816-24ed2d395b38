import { Pool } from 'pg';
import * as fs from 'fs';
import * as path from 'path';

// Get connection string from environment
const connectionString = process.env.DB_CONNECTION_STRING;

async function main() {
  console.log('Connecting to database...');
  const pool = new Pool({ connectionString });
  
  try {
    // Read the migration file
    const migrationPath = path.join(process.cwd(), 'pixapi/migrations/drizzle/0001_id_type_fix.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Apply the migration
    console.log('Applying ID type fix migration...');
    await pool.query(migrationSQL);
    
    console.log('Migration applied successfully!');
  } catch (err) {
    console.error('Error applying migration:', err);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

main().catch(err => {
  console.error('Error:', err);
  process.exit(1);
});
