# Adding a New Webhook Client

This guide provides step-by-step instructions on how to add a new client that will receive webhook events from our PIX API system.

## Overview

Our webhook system allows clients to receive real-time notifications about PIX events such as payments, transfers, and reversals. When an event occurs, our system sends a webhook to the client's specified endpoint with details about the event.

## Prerequisites

- The client must have a publicly accessible HTTPS endpoint to receive webhooks
- The client must be able to verify webhook signatures for security
- The client must have a user account in our system

## Step 1: Create a User Account (if not already exists)

If the client doesn't already have a user account in our system, create one:

```sql
INSERT INTO users (email, name, partner_id)
VALUES ('<EMAIL>', 'Client Name', 'flow2pay')
RETURNING id;
```

Note the returned `id` as you'll need it in the next step.

## Step 2: Create a Webhook Configuration

Add a webhook configuration for the client:

```sql
INSERT INTO webhook_configs (
  user_id,
  url,
  secret_key,
  events,
  description,
  is_active
)
VALUES (
  123, -- Replace with the user_id from Step 1
  'https://client-domain.com/webhooks', -- <PERSON><PERSON>'s webhook endpoint
  'random-secret-key-for-signature-verification', -- Generate a secure random key
  '["PixIn", "PixOut", "PixInReversal", "PixOutReversalExternal"]', -- Events to receive
  'Client webhook configuration',
  true
)
RETURNING id, secret_key;
```

Make sure to:
- Replace `123` with the actual user ID
- Replace `https://client-domain.com/webhooks` with the client's actual webhook endpoint
- Generate a secure random key for `secret_key` (at least 32 characters)
- Customize the events array based on which events the client wants to receive

## Step 3: Share the Secret Key with the Client

The `secret_key` generated in Step 2 must be shared securely with the client. They will use this key to verify the authenticity of webhooks received from our system.

## Step 4: Implement Webhook Verification (Client Side)

Provide the client with the following code example to verify webhook signatures:

```javascript
// Example code for verifying webhook signatures (Node.js)
const crypto = require('crypto');

function verifyWebhookSignature(payload, signature, secretKey) {
  const expectedSignature = crypto
    .createHmac('sha256', secretKey)
    .update(JSON.stringify(payload))
    .digest('hex');
  
  return crypto.timingSafeEqual(
    Buffer.from(signature),
    Buffer.from(expectedSignature)
  );
}

// In your webhook handler:
app.post('/webhooks', (req, res) => {
  const signature = req.headers['x-webhook-signature'];
  const payload = req.body;
  
  if (!verifyWebhookSignature(payload, signature, 'your-secret-key')) {
    return res.status(401).send('Invalid signature');
  }
  
  // Process the webhook
  console.log('Received valid webhook:', payload);
  res.status(200).send('OK');
});
```

## Step 5: Test the Webhook Configuration

Use our test endpoint to send a test webhook to the client:

```bash
curl -X POST https://apipix.cloud.pluggou.io/test-webhook \
  -H "Content-Type: application/json" \
  -d '{
    "event_type": "PixIn",
    "status": "Sucesso",
    "valor": 10000,
    "user_id": 123
  }'
```

Replace `123` with the client's user ID.

## Step 6: Monitor Webhook Deliveries

Monitor webhook deliveries using the following SQL query:

```sql
SELECT 
  wdl.id,
  wdl.event_type,
  wdl.status,
  wdl.status_code,
  wdl.response_body,
  wdl.created_at,
  wc.url
FROM 
  webhook_delivery_logs wdl
JOIN 
  webhook_configs wc ON wdl.webhook_config_id = wc.id
WHERE 
  wc.user_id = 123 -- Replace with the client's user ID
ORDER BY 
  wdl.created_at DESC
LIMIT 10;
```

## Webhook Payload Format

Clients will receive webhooks with the following format:

```json
{
  "event": "PixIn",
  "timestamp": "2023-06-01T12:00:00Z",
  "data": {
    "transaction_id": 12345,
    "user_id": 123,
    "amount": 10000,
    "flow2pay_id": "abcdef1234567890",
    "end_to_end_id": "E123456789012345",
    "status": "completed",
    "created_at": "2023-06-01T12:00:00Z"
  }
}
```

## Webhook Retry Mechanism

If a webhook delivery fails (non-2xx response), our system will automatically retry with the following schedule:
- 1st retry: 5 minutes after failure
- 2nd retry: 30 minutes after 1st retry
- 3rd retry: 2 hours after 2nd retry
- 4th retry: 6 hours after 3rd retry
- 5th retry: 24 hours after 4th retry

After 5 failed attempts, the webhook will be marked as permanently failed.

## Troubleshooting

If webhooks are not being delivered, check:

1. The `webhook_delivery_logs` table for delivery status and error messages
2. Ensure the client's endpoint is publicly accessible and returns a 2xx status code
3. Verify that the client is correctly validating the webhook signature
4. Check that the client's endpoint can handle the webhook payload format

## API Endpoints for Webhook Management

The following API endpoints are available for webhook management:

- `POST /webhook-config` - Create a new webhook configuration
- `GET /webhook-configs` - List all webhook configurations for a user
- `PUT /webhook-config/:id` - Update a webhook configuration
- `DELETE /webhook-config/:id` - Delete a webhook configuration
- `POST /test-webhook` - Send a test webhook

Refer to the API documentation for more details on these endpoints.
