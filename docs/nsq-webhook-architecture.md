# Leveraging NSQ for Webhook Processing

This document outlines the architecture and implementation steps needed to leverage our existing NSQ queue infrastructure for processing webhooks from our admin application.

## Current Architecture

Our current architecture for webhook processing is as follows:

1. Flow2Pay sends webhooks to our `/baas` endpoint
2. We validate and process these webhooks
3. We publish events to NSQ using the "pix-events" topic
4. Subscribers process these events and forward them to client endpoints

## Enhanced Architecture for Admin Application Webhooks

We can extend this architecture to process webhooks from our admin application:

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  Flow2Pay       │     │  Admin App      │     │  Other Sources  │
│  Webhooks       │     │  Webhooks       │     │  (Future)       │
└────────┬────────┘     └────────┬────────┘     └────────┬────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                          PIX API                                │
│                                                                 │
│  ┌─────────────────┐     ┌─────────────────┐                    │
│  │  /baas Endpoint │     │  /admin-webhook │                    │
│  └────────┬────────┘     └────────┬────────┘                    │
│           │                       │                             │
│           ▼                       ▼                             │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                 Webhook Processor                        │   │
│  └────────────────────────────┬──────────────────────────┬─┘   │
│                               │                          │      │
│                               ▼                          ▼      │
│  ┌─────────────────┐     ┌────────────────┐    ┌────────────────┐
│  │  Database       │     │  NSQ           │    │  Direct Client │
│  │  Storage        │     │  pix-events    │    │  Webhooks      │
│  └─────────────────┘     └────────┬───────┘    └────────────────┘
│                                   │                             │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                      NSQ Subscribers                            │
│                                                                 │
│  ┌─────────────────┐     ┌─────────────────┐     ┌─────────────┐│
│  │  Event Logger   │     │  Webhook        │     │  Business   ││
│  │                 │     │  Forwarder      │     │  Logic      ││
│  └─────────────────┘     └─────────────────┘     └─────────────┘│
└─────────────────────────────────────────────────────────────────┘
```

## Implementation Steps

### 1. Create a New Endpoint for Admin Webhooks

Create a new endpoint in the PIX API to receive webhooks from the admin application:

```typescript
// In pixapi/admin-webhooks.ts
import { api } from 'encore.dev/api';
import { db } from './db';
import { pixEvents } from './events';
import log from 'encore.dev/log';
import { secret } from 'encore.dev/config';
import crypto from 'crypto';

// Admin webhook token from environment variables
const ADMIN_WEBHOOK_TOKEN = secret('ADMIN_WEBHOOK_TOKEN');

/**
 * Admin webhook payload interface
 */
export interface AdminWebhookPayload {
  event_type: string;
  token?: string;
  user_id: number;
  data: any;
  timestamp: string;
}

/**
 * Admin webhook handler
 * This endpoint receives webhooks from our admin application
 */
export const handleAdminWebhook = api.raw(
  { method: 'POST', path: '/admin-webhook', expose: true },
  async (req, resp) => {
    const logger = log.with({ service: 'admin-webhook' });
    logger.info('Received admin webhook');

    try {
      // Parse the request body
      let body = '';
      for await (const chunk of req) {
        body += chunk.toString();
      }

      if (!body) {
        logger.warn('Empty webhook payload received');
        resp.writeHead(400, { 'Content-Type': 'application/json' });
        resp.end(JSON.stringify({ success: false, error: 'Empty payload' }));
        return;
      }

      // Log the raw webhook payload for debugging
      console.log('Raw admin webhook payload:', body);

      // Parse the JSON payload
      let payload: AdminWebhookPayload;
      try {
        payload = JSON.parse(body);
      } catch (error) {
        logger.error('Invalid JSON in webhook payload', { body });
        resp.writeHead(400, { 'Content-Type': 'application/json' });
        resp.end(
          JSON.stringify({ success: false, error: 'Invalid JSON payload' })
        );
        return;
      }

      // Validate the token if present
      if (payload.token && payload.token !== ADMIN_WEBHOOK_TOKEN()) {
        logger.error('Invalid token in webhook payload');
        resp.writeHead(401, { 'Content-Type': 'application/json' });
        resp.end(
          JSON.stringify({
            success: false,
            error: 'Invalid authentication token',
          })
        );
        return;
      }

      // Log the received webhook
      logger.info('Received admin webhook', {
        event_type: payload.event_type,
        user_id: payload.user_id,
      });

      // Store the webhook in the database
      await db.exec`
        INSERT INTO admin_webhooks (
          event_type, user_id, payload, created_at
        ) VALUES (
          ${payload.event_type},
          ${payload.user_id},
          ${JSON.stringify(payload.data)},
          ${new Date(payload.timestamp)}
        )
      `;

      // Publish the webhook to NSQ for distribution
      await pixEvents.publish({
        event_type: payload.event_type,
        user_id: payload.user_id,
        amount: payload.data.amount || 0,
        webhook_payload: payload.data,
        timestamp: payload.timestamp,
      });

      // Return success immediately
      resp.writeHead(200, { 'Content-Type': 'application/json' });
      resp.end(JSON.stringify({ success: true }));
    } catch (error) {
      logger.error('Error handling webhook', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      resp.writeHead(500, { 'Content-Type': 'application/json' });
      resp.end(
        JSON.stringify({
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        })
      );
    }
  }
);
```

### 2. Create a Database Table for Admin Webhooks

Create a new migration file to add a table for storing admin webhooks:

```sql
-- In pixapi/migrations/7_admin_webhooks.up.sql
CREATE TABLE admin_webhooks (
  id BIGSERIAL PRIMARY KEY,
  event_type TEXT NOT NULL,
  user_id BIGINT NOT NULL,
  payload JSONB NOT NULL,
  processed BOOLEAN NOT NULL DEFAULT FALSE,
  processed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL
);

CREATE INDEX idx_admin_webhooks_event_type ON admin_webhooks(event_type);
CREATE INDEX idx_admin_webhooks_user_id ON admin_webhooks(user_id);
CREATE INDEX idx_admin_webhooks_created_at ON admin_webhooks(created_at);
CREATE INDEX idx_admin_webhooks_processed ON admin_webhooks(processed) WHERE processed = FALSE;
```

### 3. Update the Webhook Forwarding Subscriber

Modify the webhook forwarding subscriber to handle admin webhooks:

```typescript
// In pixapi/subscribers.ts
// Update the webhook forwarding subscriber to handle all event types
const webhookForwardingSubscription = new Subscription(
  pixEvents,
  'webhook-forwarding-handler',
  {
    handler: async (event: PixEvent) => {
      const logger = log.with({
        action: 'webhook_forwarding',
        event_type: event.event_type,
      });

      logger.info('Processing webhook for forwarding', {
        event_type: event.event_type,
        user_id: event.user_id,
        transaction_id: event.transaction_id,
      });

      try {
        // Forward to app.pluggou.io
        await forwardToPluggou(event);

        // Forward to any other configured webhook endpoints
        await forwardToConfiguredEndpoints(event);

        logger.info('Webhook forwarding completed successfully');
      } catch (error) {
        logger.error('Error forwarding webhook', {
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    },
  }
);
```

### 4. Add Environment Variable for Admin Webhook Token

Add the `ADMIN_WEBHOOK_TOKEN` environment variable to your configuration:

```bash
# In .env file or environment configuration
ADMIN_WEBHOOK_TOKEN=your-secure-random-token
```

### 5. Update the Admin Application to Send Webhooks

Modify the admin application to send webhooks to the PIX API:

```typescript
// Example code for sending webhooks from the admin application
async function sendWebhookToPIXAPI(eventType, userId, data) {
  const payload = {
    event_type: eventType,
    token: process.env.ADMIN_WEBHOOK_TOKEN,
    user_id: userId,
    data,
    timestamp: new Date().toISOString(),
  };

  try {
    const response = await fetch('https://apipix.cloud.pluggou.io/admin-webhook', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(`Failed to send webhook: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error sending webhook:', error);
    throw error;
  }
}
```

## Benefits of This Architecture

1. **Reliability**: NSQ provides at-least-once delivery guarantees, ensuring webhooks are processed even if there are temporary failures.

2. **Scalability**: NSQ can handle high throughput and scales horizontally, allowing us to process large volumes of webhooks.

3. **Decoupling**: The webhook sender (admin application) is decoupled from the webhook processors, allowing each to scale independently.

4. **Unified Processing**: All webhooks (Flow2Pay and admin) are processed through the same pipeline, ensuring consistent handling.

5. **Monitoring**: NSQ provides built-in monitoring and metrics, making it easy to track webhook processing.

## Monitoring and Troubleshooting

### NSQ Admin Interface

Access the NSQ admin interface at `http://nsqadmin.cloud.pluggou.io` to monitor topics, channels, and messages.

### Database Queries

Use the following queries to monitor webhook processing:

```sql
-- Check admin webhooks
SELECT * FROM admin_webhooks ORDER BY created_at DESC LIMIT 10;

-- Check webhook delivery logs
SELECT * FROM webhook_delivery_logs ORDER BY created_at DESC LIMIT 10;

-- Check unprocessed webhooks
SELECT * FROM admin_webhooks WHERE processed = FALSE ORDER BY created_at DESC;
```

## Conclusion

By leveraging our existing NSQ infrastructure, we can create a robust and scalable system for processing webhooks from our admin application. This architecture ensures reliable delivery of webhooks to clients while maintaining a clean separation of concerns between webhook reception and processing.
