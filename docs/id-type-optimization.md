# Database ID Type Optimization Analysis

## Overview

This document analyzes the performance implications of different ID types in PostgreSQL and provides recommendations for optimizing our database schema for the PIX API application.

## Current Approach

We currently use text-based CUID strings for all primary and foreign keys in our database schema. This approach has several benefits:

- **Globally unique**: CUIDs are globally unique, reducing the risk of ID collisions
- **URL-safe**: CUIDs use URL-safe characters, making them suitable for use in URLs
- **Non-sequential**: Unlike auto-incrementing IDs, CUIDs don't reveal information about the number of records
- **Distributed-friendly**: CUIDs can be generated across multiple servers without coordination

However, there are potential performance implications to consider, especially for indexing and joins.

## Performance Analysis: Text vs. Numeric IDs in PostgreSQL

### Storage Size

- **Text-based CUIDs**: Typically 24-36 characters, requiring 24-36 bytes of storage plus overhead
- **Numeric IDs (bigint)**: 8 bytes of storage
- **UUID**: 16 bytes of storage

The larger storage size of text-based IDs impacts:
- Disk space usage
- Memory usage for index caching
- I/O performance for reading and writing

### Index Performance

PostgreSQL uses B-tree indexes for both text and numeric columns, but:

- **Text-based IDs**: Slower comparison operations, larger index size, more memory usage
- **Numeric IDs**: Faster comparison operations, smaller index size, less memory usage

For a database with millions of records, the performance difference can be significant, especially for:
- JOIN operations
- WHERE clauses on ID columns
- ORDER BY operations on ID columns

### Join Performance

Joins are particularly affected by ID type:

- **Text-based IDs**: Slower string comparisons, more memory usage
- **Numeric IDs**: Faster numeric comparisons, less memory usage

In a financial transaction system like PIX API, where joins are common (e.g., joining transactions with users), this performance difference can be noticeable.

## The drizzle-cuid2 Package

The [drizzle-cuid2](https://github.com/tqman/drizzle-cuid2) package provides specialized CUID2 column types for Drizzle ORM. Key features:

- **Specialized column types**: `cuid2()` and `cuid2String()`
- **Type safety**: Proper TypeScript types for CUID2 values
- **Integration with Drizzle ORM**: Seamless integration with Drizzle's query builder

However, it doesn't fundamentally change the storage type in PostgreSQL - IDs are still stored as text.

## Recommendation

Based on the analysis, we recommend the following approach:

### 1. Keep Using Text-Based CUID IDs

Despite the performance implications, we recommend continuing to use text-based CUID IDs for the following reasons:

- **Distributed generation**: Essential for a scalable financial system
- **Non-sequential IDs**: Better for security in a financial application
- **URL-safe**: Useful for API endpoints and client-side usage
- **Existing schema**: Significant effort to change the current approach

### 2. Adopt the drizzle-cuid2 Package

We recommend adopting the drizzle-cuid2 package to improve type safety and developer experience:

```typescript
import { cuid2 } from "drizzle-cuid2";

export const users = pgTable('users', {
  id: cuid2("id").primaryKey(),
  // other columns...
});
```

### 3. Implement Optimized Indexing Strategies

To mitigate the performance impact of text-based IDs:

1. **Ensure all ID columns are properly indexed**:
   - Primary keys are automatically indexed
   - Foreign key columns should be explicitly indexed

2. **Consider partial indexes for frequently queried subsets**:
   - For example, index only active transactions

3. **Use covering indexes for common queries**:
   - Include frequently accessed columns in the index

4. **Consider functional indexes for specialized queries**:
   - For example, index on LOWER(email) for case-insensitive searches

### 4. Implement Database Partitioning for Large Tables

For tables expected to grow very large (e.g., transactions, webhooks):

1. **Consider table partitioning by date**:
   - Partition by month or quarter
   - Improves query performance for date-range queries
   - Easier maintenance (archiving old partitions)

2. **Implement a data retention policy**:
   - Archive or delete old data
   - Keep the active dataset smaller

## Migration Plan

To implement these recommendations with minimal disruption:

### Phase 1: Adopt drizzle-cuid2 Package

1. Update the schema.ts file to use the drizzle-cuid2 package:

```typescript
import { pgTable, text, timestamp, boolean, jsonb, integer } from 'drizzle-orm/pg-core';
import { cuid2 } from 'drizzle-cuid2';

export const users = pgTable('users', {
  id: cuid2('id').primaryKey(),
  // other columns...
});
```

2. Generate a new migration:

```bash
npm run db:generate
```

3. Apply the migration:

```bash
npm run db:migrate
```

### Phase 2: Optimize Indexes

1. Create a migration to add optimized indexes:

```sql
-- Add indexes on foreign key columns
CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON pixapi.transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_transactions_qr_code_id ON pixapi.transactions(qr_code_id);
CREATE INDEX IF NOT EXISTS idx_qr_codes_user_id ON pixapi.qr_codes(user_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_user_id ON pixapi.api_keys(user_id);
CREATE INDEX IF NOT EXISTS idx_webhook_configs_user_id ON pixapi.webhook_configs(user_id);

-- Add composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_transactions_user_id_status ON pixapi.transactions(user_id, status);
CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON pixapi.transactions(created_at DESC);

-- Add partial indexes for active records
CREATE INDEX IF NOT EXISTS idx_api_keys_active ON pixapi.api_keys(id) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_webhook_configs_active ON pixapi.webhook_configs(id) WHERE is_active = true;
```

2. Apply the migration:

```bash
npm run db:migrate
```

### Phase 3: Monitor and Optimize

1. Implement query monitoring to identify slow queries
2. Analyze query plans using `EXPLAIN ANALYZE`
3. Add additional indexes or optimize queries as needed

## Conclusion

While text-based CUID IDs have some performance implications compared to numeric IDs, the benefits for a distributed financial system outweigh the costs. By adopting the drizzle-cuid2 package and implementing optimized indexing strategies, we can mitigate the performance impact while maintaining the benefits of CUIDs.

For future consideration, if the application scales to millions of transactions, we may want to revisit this decision and consider more advanced optimization strategies, such as table partitioning or a hybrid approach with both CUID and numeric IDs.
