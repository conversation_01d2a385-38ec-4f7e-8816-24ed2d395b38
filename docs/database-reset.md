# Database Reset and Fresh Schema

This document explains how to reset the database and apply a fresh schema with consistent ID types for the PIX API application.

## Overview

The PIX API application uses a PostgreSQL database with the Drizzle ORM. All tables are created in the `pixapi` schema and use text-based CUID (Collision-resistant Unique Identifiers) for primary and foreign keys.

If you encounter foreign key constraint errors or other issues related to ID type mismatches, you can reset the database and apply a fresh schema with consistent ID types.

## When to Use These Scripts

Use these scripts in the following scenarios:

1. **Foreign Key Constraint Errors**: If you encounter errors like "foreign key constraint violation" or "invalid input syntax for type bigint" when inserting data
2. **Development Environment Setup**: When setting up a new development environment
3. **Schema Changes**: When making significant changes to the database schema
4. **Data Reset**: When you want to start with a clean database

**WARNING**: These scripts will delete all data in the database. Do not use them in production environments unless you know what you're doing.

## Available Scripts

### Reset Database

The `db:reset` script drops all tables and recreates the `pixapi` schema:

```bash
npm run db:reset
```

This script:
1. Drops all foreign key constraints
2. Drops all tables in the `pixapi` schema
3. Drops and recreates the `pixapi` schema
4. Drops the Drizzle migration tracking tables

### Apply Fresh Schema

The `db:fresh` script applies a fresh schema with consistent ID types:

```bash
npm run db:fresh
```

This script:
1. Creates all tables in the `pixapi` schema with text-based CUID IDs
2. Sets up all foreign key relationships correctly
3. Creates the Drizzle migration tracking table

## Complete Reset Process

To completely reset the database and apply a fresh schema:

```bash
# 1. Reset the database (drops all tables)
npm run db:reset

# 2. Apply the fresh schema
npm run db:fresh

# 3. Create an admin user
npm run admin:create -- --email <EMAIL> --name "Admin User"
```

## Troubleshooting

### Foreign Key Constraint Errors

If you encounter foreign key constraint errors, it's likely because the ID types in your database don't match the ID types in your Drizzle schema. The most common issue is having `bigint` IDs in the database but trying to insert text-based CUID strings.

The solution is to reset the database and apply a fresh schema with consistent ID types:

```bash
npm run db:reset
npm run db:fresh
```

### Migration Errors

If you encounter errors when running migrations, it might be because the migration files are trying to create tables that already exist or modify columns that don't exist. In this case, it's best to reset the database and apply a fresh schema:

```bash
npm run db:reset
npm run db:fresh
```

### Data Loss

Remember that resetting the database will delete all data. If you need to preserve data, you should export it before resetting the database and import it afterward.

## Schema Details

The fresh schema creates the following tables with text-based CUID IDs:

- `users`: API users
- `qr_codes`: PIX QR codes
- `transactions`: Payment transactions
- `api_keys`: API keys for authentication
- `api_key_usage`: API key usage tracking
- `webhook_configs`: Webhook configuration
- `webhook_delivery_logs`: Webhook delivery tracking
- `auth_tests`: Authentication tests
- `webhooks`: Generic webhook events
- `flow2pay_webhooks`: Flow2Pay-specific webhook events
- `audit_logs`: Audit logging
- `drizzle_migrations`: Drizzle migration tracking

All foreign key relationships are set up correctly to ensure referential integrity.
