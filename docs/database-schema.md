# PIX API Database Schema

This document describes the database schema for the PIX API application, which integrates with Flow2Pay for PIX payments in Brazil.

## Overview

The database uses PostgreSQL with the Drizzle ORM for type-safe database operations. All tables are created in the `pixapi` schema to keep them organized and separate from other applications that might share the same database.

## ID System

The application uses CUID (Collision-resistant Unique Identifiers) for all primary keys and foreign keys. This provides several benefits:

1. **Globally unique**: CUIDs are globally unique, reducing the risk of ID collisions
2. **URL-safe**: CUIDs use URL-safe characters, making them suitable for use in URLs
3. **Non-sequential**: Unlike auto-incrementing IDs, CUIDs don't reveal information about the number of records
4. **Distributed-friendly**: CUIDs can be generated across multiple servers without coordination

### ID Type Fix

If you're migrating from a previous version that used `BIGSERIAL` (auto-incrementing bigint) IDs, you need to run the ID type fix migration:

```bash
npm run db:fix-ids
```

This will convert all ID columns from `BIGINT` to `TEXT` to match the Drizzle schema definition.

## Schema Structure

### Users

The `users` table stores information about API users:

- `id`: Primary key (CUID)
- `email`: User's email address (unique)
- `name`: User's name
- `partnerId`: Payment partner ID (e.g., 'flow2pay')
- `createdAt`: Timestamp when the user was created
- `updatedAt`: Timestamp when the user was last updated

### QR Codes

The `qr_codes` table stores PIX QR codes generated for payments:

- `id`: Primary key (CUID)
- `userId`: Foreign key to the users table
- `txid`: Transaction ID for the QR code (unique)
- `amount`: Payment amount in cents
- `description`: Optional payment description
- `expirationTime`: Expiration time in seconds
- `qrCodeImage`: Base64 encoded QR code image
- `qrCodeText`: PIX code string (can be copied by user)
- `status`: Payment status ('pending', 'completed', 'expired', 'canceled')
- `partnerId`: Payment partner used
- `externalId`: Additional ID for reference
- `createdAt`: Timestamp when the QR code was created
- `updatedAt`: Timestamp when the QR code was last updated

### Transactions

The `transactions` table stores payment transactions:

- `id`: Primary key (CUID)
- `userId`: Foreign key to the users table
- `qrCodeId`: Optional foreign key to the qr_codes table
- `type`: Transaction type ('pix_in', 'pix_out', 'pix_in_reversal', 'pix_out_reversal')
- `amount`: Transaction amount in cents
- `currency`: Currency code (default: 'BRL')
- `status`: Transaction status ('pending', 'completed', 'failed', 'reversed')
- `description`: Optional transaction description
- `flow2payTxid`: Flow2Pay transaction ID for QR code payments
- `flow2payEndToEndId`: Unique PIX identifier
- `flow2payIdEnvio`: Flow2Pay ID for direct transfers
- `webhookPayload`: Raw webhook data for auditing
- `createdAt`: Timestamp when the transaction was created
- `updatedAt`: Timestamp when the transaction was last updated

### API Keys

The `api_keys` table stores API keys for authentication:

- `id`: Primary key (CUID)
- `userId`: Foreign key to the users table
- `apiKey`: API key string (unique)
- `name`: Name to identify the key
- `permissions`: JSON array of permissions
- `description`: Optional description
- `isActive`: Whether the key is active
- `lastUsedAt`: Timestamp when the key was last used
- `createdAt`: Timestamp when the key was created
- `updatedAt`: Timestamp when the key was last updated
- `expiresAt`: Optional expiration timestamp
- `rateLimit`: Optional rate limit
- `rateLimitWindow`: Optional rate limit window
- `usageCount`: Number of times the key has been used

### Webhooks

The `webhooks` table stores generic webhook events:

- `id`: Primary key (CUID)
- `source`: Webhook source
- `eventType`: Event type
- `payload`: JSON payload
- `processed`: Whether the webhook has been processed
- `errorMessage`: Optional error message
- `receivedAt`: Timestamp when the webhook was received
- `processedAt`: Timestamp when the webhook was processed

### Flow2Pay Webhooks

The `flow2pay_webhooks` table stores Flow2Pay-specific webhook events:

- `id`: Primary key (CUID)
- `eventType`: Event type ('PixIn', 'PixOut', 'PixInReversal', 'PixOutReversalExternal')
- `txid`: Transaction ID for PixIn events
- `idEnvio`: ID for PixOut events
- `endToEndId`: End-to-end ID for all events
- `valor`: Amount in cents
- `status`: Status ('Sucesso', 'Falha', 'Em processamento', etc.)
- `payload`: Full webhook payload
- `processed`: Whether the webhook has been processed
- `processedAt`: Timestamp when the webhook was processed
- `createdAt`: Timestamp when the webhook was created

## Best Practices

1. **Always use the Drizzle ORM**: Don't write raw SQL queries unless absolutely necessary
2. **Use transactions for related operations**: Wrap multiple database operations in transactions
3. **Handle errors properly**: Catch and log database errors
4. **Use the `pixapi` schema**: All tables should be in the `pixapi` schema
5. **Use CUIDs for IDs**: Don't use auto-incrementing IDs or UUIDs

## Migrations

Database migrations are managed using Drizzle Kit. Use the following commands:

- `npm run db:init`: Initialize the Drizzle migration system
- `npm run db:generate`: Generate new migrations based on schema changes
- `npm run db:migrate`: Apply pending migrations
- `npm run db:fix-ids`: Fix ID type mismatch (if needed)

## Troubleshooting

### ID Type Mismatch

If you encounter errors like "invalid input syntax for type bigint", run the ID type fix migration:

```bash
npm run db:fix-ids
```

This will convert all ID columns from `BIGINT` to `TEXT` to match the Drizzle schema definition.
