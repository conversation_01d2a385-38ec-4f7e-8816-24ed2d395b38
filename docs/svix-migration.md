# Migração de NSQ para Svix

Este documento descreve a migração do sistema de webhooks da PIX API do NSQ para o Svix.

## Visão Geral

A PIX API foi atualizada para usar o Svix como sistema de gerenciamento de webhooks, substituindo a implementação anterior baseada em NSQ. Esta mudança traz os seguintes benefícios:

- **Entrega confiável**: O Svix oferece um sistema robusto de entrega de webhooks com retentativas automáticas
- **Validação de assinaturas**: Segurança aprimorada com verificação de assinaturas HMAC
- **Dashboard para monitoramento**: Interface visual para acompanhar o status das entregas
- **Filtragem de eventos**: Capacidade de filtrar eventos por tipo e destinatário

## Arquitetura Anterior (NSQ)

Na arquitetura anterior, o fluxo de eventos era:

1. Webhook recebido da Flow2Pay no endpoint `/baas`
2. Evento publicado no tópico NSQ `pix-events`
3. Diferentes consumidores NSQ processavam os eventos:
   - `pix-in-handler`: Processava eventos PixIn
   - `pix-out-handler`: Processava eventos PixOut
   - `pix-reversal-handler`: Processava eventos PixInReversal e PixOutReversalExternal
   - `webhook-forwarding-handler`: Encaminhava todos os eventos para app.pluggou.io

## Nova Arquitetura (Svix)

Na nova arquitetura, o fluxo de eventos é:

1. Webhook recebido da Flow2Pay no endpoint `/baas`
2. Evento enviado para o Svix com o tipo de evento mapeado
3. Svix entrega o evento para os endpoints configurados:
   - app.pluggou.io recebe todos os eventos
   - Endpoints específicos por usuário podem ser configurados

Além disso, um novo endpoint `/svix-webhook` foi adicionado para receber webhooks do Svix, permitindo que outros serviços enviem eventos para a PIX API através do Svix.

## Mudanças Realizadas

### 1. Remoção de Componentes NSQ

Os seguintes componentes relacionados ao NSQ foram removidos:

- Serviços NSQ no docker-compose.yaml e docker-compose.prod.yaml
- Configuração NSQ no infra.config.json
- Arquivos de configuração específicos do NSQ (docker-compose.nsq.yaml, reset-nsq.sh, etc.)
- Código de subscriptions no arquivo subscribers.ts

### 2. Implementação do Cliente Svix

Foi criado um novo módulo `pixapi/svix.ts` com as seguintes funcionalidades:

- Obtenção de um cliente Svix
- Envio de mensagens para o Svix
- Mapeamento de tipos de eventos Flow2Pay para tipos de eventos Svix
- Verificação de assinaturas de webhooks Svix

### 3. Atualização do Banco de Dados

O esquema do banco de dados foi atualizado para incluir campos relacionados ao Svix:

- `published_to_svix`: Flag indicando se o webhook foi publicado no Svix
- `svix_message_id`: ID da mensagem no Svix para rastreamento
- `svix_event_type`: Tipo de evento Svix mapeado
- `published_at`: Timestamp de quando o webhook foi publicado no Svix

### 4. Atualização do Manipulador de Webhooks

O manipulador de webhooks em `pixapi/webhooks.ts` foi atualizado para:

- Enviar webhooks para o Svix em vez do NSQ
- Mapear tipos de eventos Flow2Pay para tipos de eventos Svix
- Rastrear IDs de mensagens Svix no banco de dados

### 5. Endpoint de Webhook Svix

Foi criado um novo endpoint em `/svix-webhook` para receber webhooks do Svix, que:

- Verifica a assinatura para segurança
- Extrai o payload Flow2Pay da mensagem Svix
- Processa o webhook usando a função `processWebhook` existente
- Encaminha o evento para app.pluggou.io

### 6. Encaminhamento de Webhooks

A funcionalidade de encaminhamento de webhooks foi movida do subscriber NSQ para o endpoint Svix, mantendo a compatibilidade com o sistema existente.

## Configuração

### Variáveis de Ambiente

As seguintes variáveis de ambiente foram adicionadas:

- `SVIX_TOKEN`: Token de autenticação para a API Svix
- `SVIX_WEBHOOK_SECRET`: Segredo para verificação de assinaturas de webhooks Svix

### Secrets do Encore

Os seguintes secrets foram adicionados ao Encore:

```bash
encore secret set --type production SVIX_TOKEN
encore secret set --type production SVIX_WEBHOOK_SECRET
```

## Configuração do Svix

Para configurar o Svix para receber webhooks da PIX API:

1. Acesse o dashboard do Svix em https://webhooks.cloud.pluggou.io/
2. Crie endpoints para seus serviços que precisam receber webhooks
3. Inscreva-se no canal `flow2pay-events` para receber todos os eventos
4. Opcionalmente, inscreva-se em canais específicos por usuário (`user-{id}`)

## Tipos de Eventos

Os seguintes tipos de eventos são mapeados no Svix:

- `PixIn.Processing`: Pagamento PIX recebido em processamento
- `PixIn.Confirmation`: Pagamento PIX recebido confirmado
- `PixOut.Processing`: Transferência PIX em processamento
- `PixOut.Confirmation`: Transferência PIX confirmada
- `PixOut.Failure`: Transferência PIX falhou
- `PixInReversal.Processing`: Estorno de PIX recebido em processamento
- `PixInReversal.Confirmation`: Estorno de PIX recebido confirmado
- `PixOutReversalExternal`: Estorno de PIX enviado

## Monitoramento

Para monitorar o sistema de webhooks:

1. Verifique os logs da API para erros ao enviar mensagens para o Svix
2. Consulte o dashboard do Svix para verificar o status de entrega dos webhooks
3. Verifique a tabela `flow2pay_webhooks` no banco de dados para o status de processamento

## Referências

- [Documentação oficial do Svix](https://docs.svix.com/)
- [Dashboard do Svix da Pluggou](https://webhooks.cloud.pluggou.io/)
- [Guia de integração do Svix](./svix-webhooks/integration-guide.md)
