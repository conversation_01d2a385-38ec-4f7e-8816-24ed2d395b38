

### Payloads

#### 1. **PixIn.Processing** (Pagamento em Processamento)
```json
{
  "description": "Evento disparado quando um pagamento Pix está em processamento",
  "name": "PixIn.Processing",
  "archived": false,
  "deprecated": false,
  "schemas": {
    "1": {
      "type": "object",
      "description": "Schema para evento de pagamento Pix em processamento",
      "properties": {
        "evento": { "type": "string", "example": "PixIn" },
        "token": { "type": "string", "example": "QFjjgr0mzu1t82f5czsdb6lx0iHAy1dp6z1un3veywoz8f6n" },
        "idEnvio": { "type": "string", "example": "E4341334842960862057537973844942" },
        "endToEndId": { "type": "string", "example": "D3851456662913328993041863948377" },
        "codigoTransacao": { "type": "string", "example": "nbyayb64-pws9-4jd6-v4yg-5v04ljwqlpto" },
        "status": { "type": "string", "example": "Em processamento" },
        "chavePix": { "type": ["string", "null"], "example": null },
        "valor": { "type": "number", "example": 100 },
        "horario": { "type": "string", "example": "2025-02-10T23:07:06.725Z" },
        "recebedor": {
          "type": "object",
          "properties": {
            "nome": { "type": "string", "example": "Teste da silva" },
            "codigoBanco": { "type": "string", "example": "17846627" },
            "cpf_cnpj": { "type": "string", "example": "***.123.456-**" }
          }
        },
        "erro": { "type": ["object", "null"], "example": null }
      },
      "required": ["evento", "token", "idEnvio", "endToEndId", "codigoTransacao", "status", "valor", "horario"]
    }
  },
  "featureFlag": null,
  "groupName": "pix"
}
```

#### 2. **PixIn.Confirmation** (Pagamento Confirmado)
```json
{
  "description": "Evento disparado quando um pagamento Pix é confirmado",
  "name": "PixIn.Confirmation",
  "archived": false,
  "deprecated": false,
  "schemas": {
    "1": {
      "type": "object",
      "description": "Schema para evento de pagamento Pix confirmado",
      "properties": {
        "evento": { "type": "string", "example": "PixIn" },
        "token": { "type": "string", "example": "QFjjgr0mzu1t82f5czsdb6lx0iHAy1dp6z1un3veywoz8f6n" },
        "idEnvio": { "type": "string", "example": "E4341334842960862057537973844942" },
        "endToEndId": { "type": "string", "example": "D3851456662913328993041863948377" },
        "codigoTransacao": { "type": "string", "example": "nbyayb64-pws9-4jd6-v4yg-5v04ljwqlpto" },
        "status": { "type": "string", "example": "Sucesso" },
        "chavePix": { "type": ["string", "null"], "example": null },
        "valor": { "type": "number", "example": 100 },
        "horario": { "type": "string", "example": "2025-02-10T23:07:06.725Z" },
        "recebedor": {
          "type": "object",
          "properties": {
            "nome": { "type": "string", "example": "Teste da silva" },
            "codigoBanco": { "type": "string", "example": "17846627" },
            "cpf_cnpj": { "type": "string", "example": "***.123.456-**" }
          }
        },
        "erro": { "type": ["object", "null"], "example": null }
      },
      "required": ["evento", "token", "idEnvio", "endToEndId", "codigoTransacao", "status", "valor", "horario"]
    }
  },
  "featureFlag": null,
  "groupName": "pix"
}
```

#### 3. **PixOut.Processing** (Transferência em Processamento)
```json
{
  "description": "Evento disparado quando uma transferência Pix está em processamento",
  "name": "PixOut.Processing",
  "archived": false,
  "deprecated": false,
  "schemas": {
    "1": {
      "type": "object",
      "description": "Schema para evento de transferência Pix em processamento",
      "properties": {
        "evento": { "type": "string", "example": "PixOut" },
        "token": { "type": "string", "example": "lerkh7r79nkdjda1yg9zxvuhvc64kbfethixv9k6vt7fxmx8" },
        "idEnvio": { "type": "string", "example": "hayjqnv754f6f" },
        "endToEndId": { "type": "string", "example": "E5151512675793904351659710459689" },
        "codigoTransacao": { "type": "string", "example": "nbyayb64-pws9-4jd6-v4yg-5v04ljwqlpto" },
        "status": { "type": "string", "example": "Em processamento" },
        "chavePix": { "type": "string", "example": "<EMAIL>" },
        "valor": { "type": "number", "example": -1 },
        "horario": { "type": "string", "example": "2024-11-04T14:47:12.221Z" },
        "recebedor": {
          "type": "object",
          "properties": {
            "nome": { "type": "string", "example": "Teste da Silva LTDA" },
            "codigoBanco": { "type": "string", "example": "13140088" },
            "cpf_cnpj": { "type": "string", "example": "12345678912345" }
          }
        },
        "erro": { "type": ["object", "null"], "example": null }
      },
      "required": ["evento", "token", "idEnvio", "endToEndId", "codigoTransacao", "status", "valor", "horario"]
    }
  },
  "featureFlag": null,
  "groupName": "pix"
}
```

#### 4. **PixOut.Confirmation** (Transferência Confirmada)
```json
{
  "description": "Evento disparado quando uma transferência Pix é confirmada",
  "name": "PixOut.Confirmation",
  "archived": false,
  "deprecated": false,
  "schemas": {
    "1": {
      "type": "object",
      "description": "Schema para evento de transferência Pix confirmada",
      "properties": {
        "evento": { "type": "string", "example": "PixOut" },
        "token": { "type": "string", "example": "lerkh7r79nkdjda1yg9zxvuhvc64kbfethixv9k6vt7fxmx8" },
        "idEnvio": { "type": "string", "example": "hayjqnv754f6f" },
        "endToEndId": { "type": "string", "example": "E5151512675793904351659710459689" },
        "codigoTransacao": { "type": "string", "example": "nbyayb64-pws9-4jd6-v4yg-5v04ljwqlpto" },
        "status": { "type": "string", "example": "Sucesso" },
        "chavePix": { "type": "string", "example": "<EMAIL>" },
        "valor": { "type": "number", "example": -1 },
        "horario": { "type": "string", "example": "2024-11-04T14:47:12.221Z" },
        "recebedor": {
          "type": "object",
          "properties": {
            "nome": { "type": "string", "example": "Teste da Silva LTDA" },
            "codigoBanco": { "type": "string", "example": "13140088" },
            "cpf_cnpj": { "type": "string", "example": "12345678912" }
          }
        },
        "erro": { "type": ["object", "null"], "example": null }
      },
      "required": ["evento", "token", "idEnvio", "endToEndId", "codigoTransacao", "status", "valor", "horario"]
    }
  },
  "featureFlag": null,
  "groupName": "pix"
}
```

#### 5. **PixOut.Failure** (Transferência com Falha)
```json
{
  "description": "Evento disparado quando uma transferência Pix falha",
  "name": "PixOut.Failure",
  "archived": false,
  "deprecated": false,
  "schemas": {
    "1": {
      "type": "object",
      "description": "Schema para evento de transferência Pix com falha",
      "properties": {
        "evento": { "type": "string", "example": "PixOut" },
        "token": { "type": "string", "example": "lerkh7r79nkdjda1yg9zxvuhvc64kbfethixv9k6vt7fxmx8" },
        "idEnvio": { "type": "string", "example": "hayjqnv754f6f" },
        "endToEndId": { "type": ["string", "null"], "example": null },
        "codigoTransacao": { "type": "string", "example": "nbyayb64-pws9-4jd6-v4yg-5v04ljwqlpto" },
        "status": { "type": "string", "example": "Falha" },
        "chavePix": { "type": "string", "example": "<EMAIL>" },
        "valor": { "type": "number", "example": -1 },
        "horario": { "type": "string", "example": "2024-11-22T11:16:54.787Z" },
        "recebedor": {
          "type": ["object", "null"],
          "properties": {
            "nome": { "type": "string", "example": "Teste da Silva LTDA" },
            "codigoBanco": { "type": "string", "example": "13140088" },
            "cpf_cnpj": { "type": "string", "example": "12345678912345" }
          }
        },
        "erro": {
          "type": "object",
          "properties": {
            "origem": { "type": "string", "example": "Origem interna" },
            "motivo": { "type": "string", "example": "Erro de processamento, transação não executada. Reenvie a transação" }
          }
        }
      },
      "required": ["evento", "token", "idEnvio", "codigoTransacao", "status", "valor", "horario"]
    }
  },
  "featureFlag": null,
  "groupName": "pix"
}
```

#### 6. **PixInReversal.Processing** (Estorno de Pagamento em Processamento)
```json
{
  "description": "Evento disparado quando um estorno de pagamento Pix recebido está em processamento",
  "name": "PixInReversal.Processing",
  "archived": false,
  "deprecated": false,
  "schemas": {
    "1": {
      "type": "object",
      "description": "Schema para evento de estorno de pagamento Pix em processamento",
      "properties": {
        "evento": { "type": "string", "example": "PixInReversal" },
        "token": { "type": "string", "example": "QFjjgr0mzu1t82f5czsdb6lx0iHAy1dp6z1un3veywoz8f6n" },
        "idEnvio": { "type": "string", "example": "E4341334842960862057537973844942" },
        "endToEndId": { "type": "string", "example": "D3851456662913328993041863948377" },
        "codigoTransacao": { "type": "string", "example": "nbyayb64-pws9-4jd6-v4yg-5v04ljwqlpto" },
        "status": { "type": "string", "example": "Em processamento" },
        "chavePix": { "type": ["string", "null"], "example": null },
        "valor": { "type": "number", "example": -2 },
        "horario": { "type": "string", "example": "2025-02-10T23:07:06.725Z" },
        "recebedor": {
          "type": "object",
          "properties": {
            "nome": { "type": "string", "example": "Teste da silva" },
            "codigoBanco": { "type": "string", "example": "17846627" },
            "cpf_cnpj": { "type": "string", "example": "***.123.456-**" }
          }
        },
        "erro": { "type": ["object", "null"], "example": null }
      },
      "required": ["evento", "token", "idEnvio", "endToEndId", "codigoTransacao", "status", "valor", "horario"]
    }
  },
  "featureFlag": null,
  "groupName": "pix"
}
```

#### 7. **PixInReversal.Confirmation** (Estorno de Pagamento Confirmado)
```json
{
  "description": "Evento disparado quando um estorno de pagamento Pix recebido é confirmado",
  "name": "PixInReversal.Confirmation",
  "archived": false,
  "deprecated": false,
  "schemas": {
    "1": {
      "type": "object",
      "description": "Schema para evento de estorno de pagamento Pix confirmado",
      "properties": {
        "evento": { "type": "string", "example": "PixInReversal" },
        "token": { "type": "string", "example": "QFjjgr0mzu1t82f5czsdb6lx0iHAy1dp6z1un3veywoz8f6n" },
        "idEnvio": { "type": "string", "example": "E4341334842960862057537973844942" },
        "endToEndId": { "type": "string", "example": "D3851456662913328993041863948377" },
        "codigoTransacao": { "type": "string", "example": "nbyayb64-pws9-4jd6-v4yg-5v04ljwqlpto" },
        "status": { "type": "string", "example": "Sucesso" },
        "chavePix": { "type": ["string", "null"], "example": null },
        "valor": { "type": "number", "example": -2 },
        "horario": { "type": "string", "example": "2025-02-10T23:07:06.725Z" },
        "recebedor": {
          "type": "object",
          "properties": {
            "nome": { "type": "string", "example": "Teste da silva" },
            "codigoBanco": { "type": "string", "example": "17846627" },
            "cpf_cnpj": { "type": "string", "example": "***.123.456-**" }
          }
        },
        "erro": { "type": ["object", "null"], "example": null }
      },
      "required": ["evento", "token", "idEnvio", "endToEndId", "codigoTransacao", "status", "valor", "horario"]
    }
  },
  "featureFlag": null,
  "groupName": "pix"
}
```

#### 8. **PixOutReversalExternal** (Estorno de Transferência Externa)
```json
{
  "description": "Evento disparado quando ocorre um estorno de transferência Pix por origem externa",
  "name": "PixOutReversalExternal",
  "archived": false,
  "deprecated": false,
  "schemas": {
    "1": {
      "type": "object",
      "description": "Schema para evento de estorno de transferência Pix por origem externa",
      "properties": {
        "evento": { "type": "string", "example": "PixOutReversalExternal" },
        "token": { "type": "string", "example": "lerkh7r79nkdjda1yg9zxvuhvc64kbfethixv9k6vt7fxmx8" },
        "idEnvio": { "type": "string", "example": "hayjqnv754f6f" },
        "endToEndId": { "type": "string", "example": "E5151512675793904351659710459689" },
        "codigoTransacao": { "type": "string", "example": "nbyayb64-pws9-4jd6-v4yg-5v04ljwqlpto" },
        "status": { "type": "string", "example": "Sucesso" },
        "chavePix": { "type": ["string", "null"], "example": null },
        "valor": { "type": "number", "example": 1 },
        "horario": { "type": "string", "example": "2024-11-04T15:51:13.431Z" },
        "recebedor": {
          "type": "object",
          "properties": {
            "nome": { "type": ["string", "null"], "example": null },
            "codigoBanco": { "type": "string", "example": "13140088" },
            "cpf_cnpj": { "type": "string", "example": "***.456.789-**" }
          }
        },
        "erro": {
          "type": "object",
          "properties": {
            "origem": { "type": "string", "example": "Origem externa" },
            "motivo": { "type": "string", "example": "Pedido de reembolso" }
          }
        }
      },
      "required": ["evento", "token", "idEnvio", "endToEndId", "codigoTransacao", "status", "valor", "horario"]
    }
  },
  "featureFlag": null,
  "groupName": "pix"
}
```
