
# Introduction

Svix makes sending webhooks easy and reliable by offering [webhook sending as a service](https://www.svix.com/). With Svix you can start sending webhooks in minutes, while ensuring robust deliverability, and a great developer experience for your users.

## Looking for the receiving webhooks docs?[​](https://docs.svix.com/#looking-for-the-receiving-webhooks-docs "Direct link to Looking for the receiving webhooks docs?")

Are you looking for docs about how to receive webhooks sent by Svix? Please refer to the [receiving webhooks](https://docs.svix.com/receiving/introduction) section of the docs.

## Webhooks are harder than they seem.[​](https://docs.svix.com/#webhooks-are-harder-than-they-seem "Direct link to Webhooks are harder than they seem.")

Webhooks require a lot more engineering time, resources, and ongoing maintenance than you would first expect.

When building your own webhooks you have to deal with a lot of challenges, such as: unreliable user endpoints, which fail or hang more often than you think; monitoring and reliability of your webhook system; security implications which are unique to webhooks and much more.

This is where we come in. With Svix you can start sending webhooks in under five minutes, and we take care of all of the above and more.



# Setup

Svix offers a REST API to interact with the service, and easy to use libraries to interact with the API.

Svix also has an API reference and OpenAPI (formerly Swagger) specifications at the [API documentation page](https://api.svix.com/docs) which you can always refer to the exact schemas and endpoints available in our API.

When working with Svix you can either use our [REST API](https://api.svix.com/docs) directly, or using any of our libraries. In this document we'll cover how to install them for each platform.

## Install the libraries[​](https://docs.svix.com/setup#install-the-libraries "Direct link to Install the libraries")

- JavaScript
- Python
- Rust
- Go
- Java
- Kotlin
- Ruby
- C#
- CLI
- cURL

```
npm install svix// Oryarn add svix
```

Then you can just use them as follows

- JavaScript
- Python
- Rust
- Go
- Java
- Kotlin
- Ruby
- C#
- CLI
- cURL

```
import { Svix } from "svix";const svix = new Svix("AUTH_TOKEN");const app = await svix.application.create({ name: "Application name" });
```




# Quickstart

This page has everything you need to start sending webhooks with Svix.

### Main concepts[​](https://docs.svix.com/quickstart#main-concepts "Direct link to Main concepts")

In Svix you have three important entities you will be interacting with:

- [`messages`](https://docs.svix.com/overview#messages): these are the webhooks being sent. They can have contents and a few other properties.
- [`application`](https://docs.svix.com/overview#consumer-applications) (consumer application): this is where `messages` are sent to. Usually you want to create one application for each user on your platform.
- [`endpoint`](https://docs.svix.com/overview#endpoints): endpoints are the URLs messages will be sent to. Each application can have multiple `endpoints` and each message sent to that application will be sent to all of them (unless they are not subscribed to the sent [event type](https://docs.svix.com/event-types)).

For more information, please refer to the [Overview section](https://docs.svix.com/overview).

## Getting started[​](https://docs.svix.com/quickstart#getting-started "Direct link to Getting started")

Get your authentication token (`AuthToken`) from the [Svix dashboard](https://dashboard.svix.com/).

Automatic environment detection

The Svix libraries automatically infer the correct server URL from the authentication token when using the hosted Svix server. No action needed.

If you are self-hosting the server, you will need to explicitly set the server URL yourself. Please refer to the [custom server URL](https://docs.svix.com/quickstart#custom-server-url) section below.

## Sending messages[​](https://docs.svix.com/quickstart#sending-messages "Direct link to Sending messages")

### Creating a consumer application[​](https://docs.svix.com/quickstart#creating-a-consumer-application "Direct link to Creating a consumer application")

Each of your users needs an associated consumer application. The easiest way is to create a new application whenever a user signs up. In this section we will use the [create application API endpoint](https://api.svix.com/docs#operation/create_application_api_v1_app__post) to create an application.

You would need the application's ID when sending messages. You can either save the ID returned when creating the application, or set your own unique id (e.g. your user's username or internal database id) in the optional `uid` field and use that instead.

Code example:

- JavaScript
- Python
- Rust
- Go
- Java
- Kotlin
- Ruby
- C#
- CLI
- cURL

```
import { Svix } from "svix";const svix = new Svix("AUTH_TOKEN");const app = await svix.application.create({ name: "Application name" });
```

### Send a message[​](https://docs.svix.com/quickstart#send-a-message "Direct link to Send a message")

We will now send a new message using the [create message API endpoint](https://api.svix.com/docs#operation/create_message_api_v1_app__app_id__msg__post). It accepts an `app_id`, which is the application's ID (or custom `uid`) from the previous section. In addition, it accepts the following fields (as part the json body):

- [`eventType`](https://docs.svix.com/event-types): an identifier denoting the type of the event. E.g. `invoice.paid`.
- `eventId`: an optional unique ID for the event (unique per app). This is useful if you want to map each message to unique events on your system.
- `payload`: a JSON dictionary that can hold anything. Its content will be sent as the webhook content.

For example, the following code sends a webhook of type `eventType`, with the contents of `payload` as the body:

- JavaScript
- Python
- Rust
- Go
- Java
- Kotlin
- Ruby
- C#
- CLI
- cURL

```
const svix = new Svix("AUTH_TOKEN");await svix.message.create("app_Xzx8bQeOB1D1XEYmAJaRGoj0", {  eventType: "invoice.paid",  eventId: "evt_Wqb1k73rXprtTm7Qdlr38G",  payload: {    type: "invoice.paid",    id: "invoice_WF7WtCLFFtd8ubcTgboSFNql",    status: "paid",    attempt: 2,  },});
```

#### Including the event type in the payload[​](https://docs.svix.com/quickstart#including-the-event-type-in-the-payload "Direct link to Including the event type in the payload")

Webhook consumers often consume multiple event types using the same endpoint. To enable them to be able to differentiate different events, it's common practice to include the event type in the webhook's payload. Svix, however, doesn't automatically inject the event type into the payload, in order to give our customers full control over the content and structure of the payloads they send.

Most commonly people include the event type in the payload as a top-level key called `type`, `event_type`, or `eventType`, but it's up to you how you want to call it.

#### Idempotency[​](https://docs.svix.com/quickstart#idempotency "Direct link to Idempotency")

Svix supports [idempotency](https://en.wikipedia.org/wiki/Idempotence) for safely retrying requests without accidentally performing the same operation twice. This is useful when an API call is disrupted in transit and you do not receive a response.

For more information, please refer to the [idempotency section](https://docs.svix.com/idempotency) of the docs.

Note: while the `eventId` can potentially be used to enforce short-term uniqueness (similar to idempotency), it's recommended to use the idempotency mechanism when needed rather than relying on `eventId` checks.

## Add webhook endpoints[​](https://docs.svix.com/quickstart#add-webhook-endpoints "Direct link to Add webhook endpoints")

In the example above we showed how to send messages, though these messages were not sent to any specific URLs. In order for them to be sent, we need to add endpoints. This is what this section is about.

tip

You can use the [Svix Play webhook debugger](https://www.svix.com/play/) and the [Svix CLI](https://github.com/svix/svix-cli) to inspect, test and debug your webhooks during development.

### Using the Application Portal[​](https://docs.svix.com/quickstart#using-the-application-portal "Direct link to Using the Application Portal")

Svix offers a pre-build application portal. With [one API call](https://api.svix.com/docs#operation/get_dashboard_access_api_v1_auth_dashboard_access__app_id___post), you can give your users access to this UI and they can then add their own endpoints themselves. App portal access is based on short-lived sessions using special magic links. Your customers don't need a Svix account, and they don't even need to know that Svix exists.

More on that in the [application portal docs](https://docs.svix.com/app-portal).

### Using the API[​](https://docs.svix.com/quickstart#using-the-api "Direct link to Using the API")

In addition to the App Portal, you can also use our API to add endpoints to your applications. To do so, you will use the [create endpoint API](https://api.svix.com/docs#operation/create_endpoint_api_v1_app__app_id__endpoint__post).

For example:

- JavaScript
- Python
- Rust
- Go
- Java
- Kotlin
- Ruby
- C#
- CLI
- cURL

```
const svix = new Svix("AUTH_TOKEN");await svix.endpoint.create("app_Xzx8bQeOB1D1XEYmAJaRGoj0", {  url: "https://api.example.com/svix-webhooks/",  version: 1,  description: "My main endpoint",});
```

The version number is an integer signifying the current version of the API. You can set it to `1` if you haven't started versioning your API yet.

## Using Svix in a stateless manner[​](https://docs.svix.com/quickstart#using-svix-in-a-stateless-manner "Direct link to Using Svix in a stateless manner")

You can use Svix in a completely stateless manner, without having to store any Svix identifiers (or anything else) in your own database; you can do it by utilizing `UID`s. If you set a `UID` on an application, endpoint, or any other entity, you can use this `UID` interchangeably with its `ID` anywhere in the API.

For more information, please refer to the [section about `UID`s in the overview](https://docs.svix.com/overview#ids-and-uids).

## Consuming webhooks documentation[​](https://docs.svix.com/quickstart#consuming-webhooks-documentation "Direct link to Consuming webhooks documentation")

Please refer to the [consuming webhooks](https://docs.svix.com/consuming-webhooks) section for information you can share with your customers on how to easily consume webhooks, and how to use the application portal.

## Common Usage Examples[​](https://docs.svix.com/quickstart#common-usage-examples "Direct link to Common Usage Examples")

The above should give you everything you need to know in order to get started with Svix. However, we've gathered examples of some of the more common ways people use the Svix API in order to make it even easier for you to get started and follow the best practices.

For more information please refer to the [common usage examples section](https://docs.svix.com/common-usage-examples).

## Custom server URL[​](https://docs.svix.com/quickstart#custom-server-url "Direct link to Custom server URL")

Starting from version 0.64.0, the libs automatically infer the correct server URL from the authentication token (for tokens created from August 2022 onwards).

If you are using old tokens, or a non-standard server URL, you may need to set the URL explicitly in the library based on your chosen environment and as shown in the "API Access" page in the dashboard.

- JavaScript
- Python
- Rust
- Go
- Java
- Kotlin
- Ruby
- C#
- CLI
- cURL

```
import { Svix } from "svix";const svix = new Svix("AUTH_TOKEN", { serverUrl: "THE_SERVER_URL" });
```

## Closing words[​](https://docs.svix.com/quickstart#closing-words "Direct link to Closing words")

That's it! There are only three API calls you should really care about. Creating applications (i.e users), sending messages, and giving your users access to the App Portal. All of them are covered here.

[Edit this page](https://github.com/svix/svix-docs/edit/main/docs/quickstart.mdx)






# Overview

This section provides an overview of the main concepts of Svix and how to use them.

## Information flow[​](https://docs.svix.com/overview#information-flow "Direct link to Information flow")

In Svix you can create multiple `environments`. Each `environment` is completely isolated from others. It has different data, different settings, and different API keys, among others.

Webhooks are called `messages`. You can use your API keys to send `messages` to `applications`. Each `application` can have multiple `endpoints`. A `message` sent to a specific application is sent to all of the relevant `endpoints` (filtered by `event type` and `channel`).

![Information flow graph](https://docs.svix.com/assets/images/msg-flow-07d064185a79f14ac746450097dbb5c7.svg)

## IDs and UIDs[​](https://docs.svix.com/overview#ids-and-uids "Direct link to IDs and UIDs")

All Svix entities have IDs as their unique identifiers. Some entities, such as applications and endpoints, support an additional unique identifier called `UID`.

These `UID`s can be used interchangeably with `ID`s all throughout the API. So for example, you can send a message to a specific `ID`, or a specific `UID`, both will work.

This enables you to use Svix in a completely stateless manner, without having to store the Svix identifiers (or anything) in your own database.

## Entities[​](https://docs.svix.com/overview#entities "Direct link to Entities")

There are a variety of entities in Svix. The Svix entities are mostly hierarchical with the environment being the main isolation unit, below it there are applications which represent a target for messages, and even below there are endpoints which represent a destination to send webhooks to.

![Entities diagram](https://docs.svix.com/assets/images/entity-graph-7b44ff6199b69c496e2ac50b61992fb8.svg)

### Consumer Applications[​](https://docs.svix.com/overview#consumer-applications "Direct link to Consumer Applications")

When sending messages using the Svix API you will be sending them to a specific application (consumer), which will then distribute them to the associated endpoints. In most cases you would want to create one application for each of our customers, though in some cases you may want to create multiple applications per customer.

Each application lies within its own security context. Each application is completely isolated from another, but also there is no isolation within an application. This means that you should assume that every message sent to an application can be viewed by all of the endpoints subscribed to that application. What does this mean in practice? Create different applications for different security contexts.

You can define a `uid` for an application which can then be used interchangeably in the API with the application's `id`. Most people set the `uid` to their own internal customer id, so for example if they have a user called `some-user` they would set the `uid` to `some-user` and then use it with the Svix API as follows:

```
svix.application.update('some-user', ApplicationIn(/* ... */))
```

It's recommended to create an application (using the API) for each of their customers when they sign up to your service. You can however also create it "lazily", and only do it when they enable webhooks.

Alternatively, you can create an application the first time you send a message to it. When sending a message you can, optionally, send an `application` property along with it. If the application does not exist yet it will be created; if it does then the `application` property will be ignored.

```
svix.message.create('some-user', {    application: ApplicationIn(/* ... */),    payload: {/* ... */},})
```

### Endpoints[​](https://docs.svix.com/overview#endpoints "Direct link to Endpoints")

Endpoints represent a target for messages sent to a specific application. You can have multiple endpoints per application, and every message sent to the application will be sent to all of them. Endpoints can have filters applied to them which will prevent them from receiving specific messages. The most common such filter is [event type](https://docs.svix.com/event-types), where an endpoint can choose to only subscribe to a limited set of events.

Endpoints can be created by you using [the create endpoint API](https://api.svix.com/docs#operation/create_endpoint_api_v1_app__app_id__endpoint__post), though they are most commonly created by your customers using the [application portal](https://docs.svix.com/app-portal) or a similar UI.

Like with applications, you can define a `uid` for an endpoint which can then be used interchangeably in the API with the endpoint's `id`.

### Messages[​](https://docs.svix.com/overview#messages "Direct link to Messages")

Messages are the webhooks being sent. They can have a content, event type, and a few other properties.

A message sent to an application will be sent to all of its endpoints (based on aforementioned filtering rules). When an application has no endpoints, or when no endpoints match a message, the sent message is just saved to the database but is not actually sent to a customer.

If a message delivery fails, it will be attempted multiple times until either it succeeds, or its attempts have been exhausted. Please refer to the [retry schedule](https://docs.svix.com/retries) for more information.

Messages can have an associated `eventId`. The `eventId` is used to map a message from Svix to one in your system so you can easily map a message to the reason why you sent it.

### Attempts[​](https://docs.svix.com/overview#attempts "Direct link to Attempts")

Attempts represent an attempt that has been made to send a message to an endpoint. Attempts also record the response content of the attempt, the response HTTP status code, as well as other properties. Each attempt to send a message is recorded in an attempt entity which can then be queried (there can be multiple when there are failures).

### Event Types[​](https://docs.svix.com/overview#event-types "Direct link to Event Types")

Event types are identifiers on messages that describe the message being sent and implies its associated schema. Each message has exactly one event type, and endpoints can listen to all, or a subset of the created event types.

Event types schemas are used for things like "send example webhook" in the application portal, the event catalog, and the [automatic Zapier integration](https://docs.svix.com/integrations/zapier).

You will need to create event types for them to be shown in the application portal and used by endpoints.

### Environments[​](https://docs.svix.com/overview#environments "Direct link to Environments")

Environments are completely isolated Svix environments that have their own API keys, data, and settings. You can think of Svix environments are completely separate accounts.

You can create as many environments as you want in the dashboard UI depending on your needs. Most people create one environment for `Production`, one for `Staging` and one for `Development`. Some people create multiple `Production` environments based on geographical regions e.g. `Production EU` and `Production US`.





# Common Usage Examples

This document includes examples and useful information for how to model your use-case with Svix.

This list is not exhaustive and only covers some of the more common examples. Svix is used by a variety of different customers for a variety of different use-cases. If your use-case doesn't fit any of the below, please [contact us](https://docs.svix.com/get-help/) and we would be happy to chat on how to best use Svix in your particular circumstances.

## Preamble[​](https://docs.svix.com/common-usage-examples#preamble "Direct link to Preamble")

While different use-cases often require different solutions, there are a few Svix features that are useful in most use-cases and we will highlight them in this section.

### Consumer Application and Endpoint UIDs, and message event IDs[​](https://docs.svix.com/common-usage-examples#consumer-application-and-endpoint-uids-and-message-event-ids "Direct link to Consumer Application and Endpoint UIDs, and message event IDs")

Svix enables you to use your internal `ID`s as ids for Svix entities by using the `UID` property of consumer applications and endpoints, and `eventId` of messages.

This lets you use Svix in a completely stateless manner, without having to store the Svix identifiers (or anything) in your own database.

For more information, please refer to the [section about `UID`s in the overview](https://docs.svix.com/overview#ids-and-uids).

### Event types[​](https://docs.svix.com/common-usage-examples#event-types "Direct link to Event types")

Each message sent through Svix has an associated [event type](https://docs.svix.com/event-types). Event types are identifiers denoting the type of message being sent and are the primary way for webhook consumers to configure what events they are interested in receiving.

You can even automatically generate public documentation for your webhooks [by making your event catalog public](https://docs.svix.com/event-types#publishing-your-event-catalog).

### Consumer Application portal[​](https://docs.svix.com/common-usage-examples#consumer-application-portal "Direct link to Consumer Application portal")

Svix comes with an application portal for your users that you can use out of the box. Your users can then use it to add endpoints, debug delivery, as well as inspect and replay past webhooks. The application portal can be used as a standalone page, or embedded in your own dashboard using an iframe.

Most Svix customers use it in an iframe, or implement it themselves (either fully or partially) using the Svix API.

For more information, please refer to the [app portal section of the docs](https://docs.svix.com/app-portal).

### Idempotency[​](https://docs.svix.com/common-usage-examples#idempotency "Direct link to Idempotency")

Svix supports idempotency for safely retrying requests without accidentally performing the same operation twice. This is useful when an API call is disrupted in transit and you do not receive a response.

For more information, please refer to the [idempotency section of the docs](https://docs.svix.com/idempotency).

### Webhooks sent by Svix[​](https://docs.svix.com/common-usage-examples#webhooks-sent-by-svix "Direct link to Webhooks sent by Svix")

As you may expect from a webhooks service, Svix also uses webhooks to notify you of events. For example, Svix will send you a webhook when a message delivery has failed, or an endpoint has been failing for too long of a period.

For more information, please refer to the [operational webhooks section of the docs](https://docs.svix.com/incoming-webhooks).

### Environments (sub-accounts)[​](https://docs.svix.com/common-usage-examples#environments-sub-accounts "Direct link to Environments (sub-accounts)")

Svix supports having multiple environments (sub-accounts) within the same Svix account. One common use-case is to separate production, staging and development environments. Another one, is ensuring data stays in a specific geography (e.g. the EU) for compliance reasons.

For more information, please refer to the [managing environments section of the docs](https://docs.svix.com/account/environments).

## Example use-cases[​](https://docs.svix.com/common-usage-examples#example-use-cases "Direct link to Example use-cases")

### The common use-case (probably you)[​](https://docs.svix.com/common-usage-examples#the-common-use-case-probably-you "Direct link to The common use-case (probably you)")

#### Who is it for[​](https://docs.svix.com/common-usage-examples#who-is-it-for "Direct link to Who is it for")

This use-case is the right one if you have different customers on your service, and each of them gets sent webhooks independently based on activities on their own separate accounts. This is the most common use-case and probably matches most people.

One company that matches this description is Stripe. Every Stripe customer has their own account, and webhooks are sent by Stripe to a specific account. The webhooks may cover the activities of many of a particular Stripe customer's users (e.g. whether they paid), but webhooks sent by Stripe only ever affect one customer.

Another example is [Clerk](https://clerk.dev/). Each of Clerk's customers may subscribe to webhooks related to the activity of its own account only.

#### Usage example[​](https://docs.svix.com/common-usage-examples#usage-example "Direct link to Usage example")

A service matching this description should create one application for each one of its customers. The service will then send events related to this specific customer to the application, and offer the [application portal](https://docs.svix.com/app-portal) to each of its customers so that customers can manage their own webhooks.

As mentioned above, the service can utilize event types to let its customers filter which event they would like to listen to, and it can also utilize application `UID`s to [use Svix in a fully stateless manner](https://docs.svix.com/overview#ids-and-uids).

### Multiple channels use-case[​](https://docs.svix.com/common-usage-examples#multiple-channels-use-case "Direct link to Multiple channels use-case")

#### Who is it for[​](https://docs.svix.com/common-usage-examples#who-is-it-for-1 "Direct link to Who is it for")

This use-case is similar to the common one, but with one small difference: your customers may take part in completely separate activities or projects.

One common example for this is project management software such as Github, Jira, and Linear. Github users may be watching multiple repositories and be a member of multiple organizations. Their customers want to be able to choose which webhooks should get to which endpoints based on the project or organization.

#### Usage example[​](https://docs.svix.com/common-usage-examples#usage-example-1 "Direct link to Usage example")

One easy solution is to use a different application for each project. While it's possible to do it this way, it does pose a few limitations. First of all, the app portal only shows one application at a time, so managing endpoints across different applications is difficult. But also, oftentimes you'd like to listen for events affecting multiple projects from the same endpoint, which is very cumbersome if you've created multiple applications.

Enter [channels](https://docs.svix.com/channels). Channels are an extra dimension of filtering messages that is similar, but orthogonal to event types. Channels let you include in the message which channels have been affected by a particular change. For example, you can send a message and mark it as affecting `proj_12` and `proj_35`. Endpoints can then be marked as listening to only changes affecting particular projects (channels) and the messages will be filtered accordingly.

Services matching this use-case often implement their own UI for adding and editing endpoints instead of using the application portal, in order to build a nice UI for auto completing, and choosing relevant channels. They would still use the application portal for offering their customers a way to gain visibility into the webhooks being sent, just in read-only mode.

### Partners use-case[​](https://docs.svix.com/common-usage-examples#partners-use-case "Direct link to Partners use-case")

#### Who is it for[​](https://docs.svix.com/common-usage-examples#who-is-it-for-2 "Direct link to Who is it for")

This use-case is for services where the webhooks are not being ingested by the service's customers, but rather by partners that, for example, build extensions for the service.

Let's take Zoom for example. They have an application marketplace, and you can install different applications on your Zoom account. As a Zoom customer, you are not actually listening to webhooks yourself, but Zoom needs to send them to the applications you've installed.

#### Usage example[​](https://docs.svix.com/common-usage-examples#usage-example-2 "Direct link to Usage example")

There are a few ways to achieve this, and the exact method chosen will depend on your additional requirements and your own personal preferences.

The first method is to have one application for each of your customers (even though they are unaware of webhooks), and for every partner that would like to listen to webhooks add a new endpoint with this partner's URL and event type requirements (with a unique endpoint `UID` to [keep things stateless](https://docs.svix.com/overview#ids-and-uids)). The nice thing about this method is that it keeps things simple, and all of the webhooks for one application are sent to one place. The bad thing, however, is that if this partner ever wants to change the URL or the event type, this needs to be updated for each and every integration that they use.

The second method is to have one application per partner. When an event is triggered for a customer that a partner is interested in, a message is sent to this partner's app (as well as those for any other partners listening). This is currently done with separate API calls, but we plan on adding a way to mass send a message to multiple applications, so please [let us know](https://docs.svix.com/get-help) if this is something you would be interested in.

### The mix and match use-case[​](https://docs.svix.com/common-usage-examples#the-mix-and-match-use-case "Direct link to The mix and match use-case")

#### Who is it for[​](https://docs.svix.com/common-usage-examples#who-is-it-for-3 "Direct link to Who is it for")

Some services have more complex use-cases that match more than one of the above examples. For example, your service may have both partners and end-users, and both may need to be able to listen to webhooks. Or maybe even two types of customers listening to the same events (so essentially "using" the common scenario twice).

One such example is Gmail, where the user may want to connect third party add-ons (e.g. a CRM) and listen to a webhook themselves.

#### Usage example[​](https://docs.svix.com/common-usage-examples#usage-example-3 "Direct link to Usage example")

One easy solution is to just have the equivalent of multiple use-cases layered on top of one another, i.e., send messages to multiple applications that are exposed differently in your service's dashboard to different partners and customers based on their roles.

### Other use-cases[​](https://docs.svix.com/common-usage-examples#other-use-cases "Direct link to Other use-cases")

As mentioned above, this list is not exhaustive and only covers some of the more common examples. Svix is used by a variety of customers for a variety of use-cases. If your use-case doesn't fit any of the above, please [contact us](https://docs.svix.com/get-help/) and we would be happy to chat on how to best use Svix in your particular circumstances.




# Consuming Webhooks

In addition to helping you send webhooks to your users, we also help your users to easily verify the authenticity and security of the webhooks they receive from you.

info

For information on how to verify webhooks, please head to the [Consuming Webhooks documentation](https://docs.svix.com/receiving/introduction).

## Documentation for your users[​](https://docs.svix.com/consuming-webhooks#documentation-for-your-users "Direct link to Documentation for your users")

We offer easy to use docs for how to safely consume webhooks which you can share with your users directly: [Consuming Webhooks documentation](https://docs.svix.com/receiving/introduction).

## Building your own libraries[​](https://docs.svix.com/consuming-webhooks#building-your-own-libraries "Direct link to Building your own libraries")

Depending on your product, you may want to offer additional processing before passing the verified payload for your customers. For example, you may want to create API objects from the payload that your users can use to interact with your API.

In this scenario you would want to create your own `Webhook` class equivalent that uses the `Svix` class internally. This way you can get all of the verification that Svix offer, while still being able to post-process the payload before passing it to your users.




# Event Types

Each message sent through Svix has an associated event type. Event types are identifiers denoting the type of message being sent. Because they are the primary way for webhook consumers to configure what events they are interested in receiving, we highly recommend including an event catalog that describes each event type and provides sample payloads in your documentation. To make it easy for webhook consumers to find and subscribe to the right events, Svix automatically generates an event catalog. See more about [publishing your event catalog here](https://docs.svix.com/event-types#publishing-your-event-catalog).

Event types are just a string, for example: `user.signup`, `invoice.paid` and `workflow.completed`.

Webhook consumers can choose which events are sent to which endpoint. By default, all messages are sent to all endpoints. Though when adding or editing endpoints, users can choose to only subscribe to some of the event types for this particular endpoint.

Bulk Create

It's recommended to add the event types you are going to use ahead of time. You can do it by [importing them from an OpenAPI spec](https://docs.svix.com/event-types#import-event-types-from-an-openapi-specification) or [bulk creating them from code](https://docs.svix.com/event-types#bulk-creating-event-types-from-code).

## What your users will see[​](https://docs.svix.com/event-types#what-your-users-will-see "Direct link to What your users will see")

This is how choosing event types look like in the [pre-built application portal](https://docs.svix.com/app-portal):

![Management UI screenshot](https://docs.svix.com/assets/images/event-type-selection-a1721ce6b84bd24d6228c146701ff77a.png)

## Event Type Format[​](https://docs.svix.com/event-types#event-type-format "Direct link to Event Type Format")

Event types have a pattern defined a `^[a-zA-Z0-9\\-_.]+$`, meaning it can contain any of the following characters:

- A through Z (uppercase or lowercase)
- 0 through 9
- Special characters: `-`, `_`, `.`

Style guide

We recommend you use period-delimited event type names (e.g. `<group>.<event>`). If you do, the App Portal UI will logically group them for your users and make it easier for them to subscribe an endpoint to all events in a particular group of event types.

## Using event types[​](https://docs.svix.com/event-types#using-event-types "Direct link to Using event types")

You can add, edit, and delete event types in [the dashboard](https://dashboard.svix.com/) or through the API below.

- JavaScript
- Python
- Rust
- Go
- Java
- Kotlin
- Ruby
- C#
- CLI
- cURL

```
import { Svix } from "svix";const svix = new Svix("AUTH_TOKEN");const eventType = await svix.eventType.create({  name: "user.signup",  description: "A user has signed up",});
```

## Event Type Schema[​](https://docs.svix.com/event-types#event-type-schema "Direct link to Event Type Schema")

One of the best ways to help your users integrate with you is by defining schemas for your event types. Event type schemas allow your users to anticipate the shape of the message body they will receive as well as introduce guardrails for each data type.

Schemas can be created using our visual schema editor, or by providing your own JSONSchema (Draft 7) spec either in the UI or [the API](https://api.svix.com/docs#tag/Event-Type/operation/v1.event-type.create).

![schema-editor-basic](https://docs.svix.com/assets/images/schema-editor-basic-57bcf14b05162c19ba48b578d23d8bdc.png)

Once you have a schema defined, your users will be able to view the schema definition as well as an example event from the Event Catalog in the App Portal.

![schema-preview](https://docs.svix.com/assets/images/schema-preview-4552aac4d0e309946d52735a3441bf9a.png)

To learn more about creating schemas, check out our guide on [adding your first event type schema](https://docs.svix.com/tutorials/event-type-schema).

### Schema validation[​](https://docs.svix.com/event-types#schema-validation "Direct link to Schema validation")

Svix doesn't enforce the event type schema when creating a message. There are a few reasons to why we do it, though the main one is that we would much rather send a message with a bad schema, than block messages because the schema was slightly wrong. This is especially true for more strict schemas like ones that enforce some fields follow a specific regex (a potentially minor violation).

Additionally, since the payload is fully controlled by our customers (you), it's very easy to verify the schema on the sender side, before sending to Svix, which ensures people get exactly the level of validation they expect.

We do however love schemas and think they are super important, so we plan on adding an automatic SDK generator that will have type checked and validated schemas for you client side, so that bad schemas will fail at compile time, not run time!

## Import event types from an OpenAPI specification[​](https://docs.svix.com/event-types#import-event-types-from-an-openapi-specification "Direct link to Import event types from an OpenAPI specification")

The [OpenAPI specification](https://www.openapis.org/) is a formal standard for describing HTTP APIs. Some people write these by hand, and some automatically generate these from the web framework of their choice.

If you already have an OpenAPI specification, you can upload it to Svix which will automatically create event types for you based on the [`webhooks`](https://github.com/OAI/OpenAPI-Specification/blob/main/versions/3.1.0.md#fixed-fields) section (or `x-webhooks` for OpenAPI 3.0).

In addition to the standard features supported by the OpenAPI spec, Svix also supports a couple of extensions: `x-svix-feature-flag` and `x-svix-group-name` which let you set the [feature flag](https://docs.svix.com/event-types#event-type-feature-flags) and group name on the event type respectively as follows:

```
"webhooks": {  "pet.new": {    "post": {      "operationId": "pet.new",      "x-svix-feature-flag": "beta-feature-a",      "x-svix-group-name": "group-name-1",      "requestBody": {        ...      }    }  }}
```

If your API spec has a `webhooks` section (or `x-webhooks`) you can continue to the next sections to learn how to upload it. If you need help with adding the `webhooks` section to your OpenAPI spec, please refer to the [Generating an OpenAPI spec section](https://docs.svix.com/event-types#generating-an-openapi-spec).

### From the dashboard[​](https://docs.svix.com/event-types#from-the-dashboard "Direct link to From the dashboard")

Your first option for uploading your OpenAPI spec is by uploading it from the Svix dashboard. Once uploaded you'll be faced with this preview which will give you the opportunity to review the added event types before saving them.

![event-type-openapi](https://docs.svix.com/assets/images/event-type-openapi-8ce5b4f6585eef3919e1423f1c161682.png)

The event description, schema and examples will be used to create the Svix event type, using the `path id` as the event type name. If you use choose the name of an already existing event type, it will be updated with the new values.

### Using the API[​](https://docs.svix.com/event-types#using-the-api "Direct link to Using the API")

The API includes an endpoint which helps keep your [event-types in sync with a JSON OpenAPI Spec](https://api.svix.com/docs#tag/Event-Type/operation/v1.event-type.import-openapi). Event types and their schemas are generated from `webhooks` or `x-webhooks` top-level key of the spec, just like with the UI importer.

When the event type already exists, it'll be updated with the latest description and schema, otherwise a new event type will be created.

- JavaScript
- Python
- Rust
- Go
- Java
- Kotlin
- Ruby
- C#
- CLI
- cURL

```
const spec = loadOpenapiFromFile("openapi.json");await svix.eventType.importOpenApi({ spec });
```

### Using the Github Action[​](https://docs.svix.com/event-types#using-the-github-action "Direct link to Using the Github Action")

Svix also provides a [Github Action](https://github.com/svix/svix-event-type-import-action) to upload your OpenAPI spec and automatically create or update your event types as part of your CI/CD pipeline.

```
- name: Upload Event Types to Svix  uses: svix/svix-event-type-import-action@v1.0.0  with:    openapi-file: 'path/to/your/openapi-spec.yml' # can be a .json too    svix-api-key: ${{ secrets.SVIX_API_KEY }}
```

## Bulk creating event types from code[​](https://docs.svix.com/event-types#bulk-creating-event-types-from-code "Direct link to Bulk creating event types from code")

The easiest way to bulk-create event types is by just writing a simple script to load a [pipe delimited `CSV`](https://en.wikipedia.org/wiki/Comma-separated_values) file or a `JSON` file with the event types and make the API requests.

Here is an example `CSV` file (without headers) of events:

```
user.created|A user has been createduser.removed|A user has been removeduser.changed|A user has changed
```

Here are some example scripts for processing the above file:

- JavaScript
- Python
- Rust
- Go
- Java
- Kotlin
- Ruby
- C#
- CLI
- cURL

```
import { Svix } from "svix";import fs from "fs";import readline from "readline";const svix = new Svix("AUTH_TOKEN");async function execute() {  const fileStream = fs.createReadStream("./data.csv");  const data = readline.createInterface({    input: fileStream,    crlfDelay: Infinity,  });  for await (const lineItr of data) {    const line = lineItr.split("|");    const eventType = await svix.eventType.create({      name: line[0],      description: line[1],    });  }}execute();
```

## Publishing your Event Catalog[​](https://docs.svix.com/event-types#publishing-your-event-catalog "Direct link to Publishing your Event Catalog")

By default, your event types are only accessible to users from within an authenticated session on the Application Portal.

If you would like to have them publicly accessible, you can enable the "Make Public" setting from the Event Catalog configuration screen in the Dashboard settings.

Enabling this setting will cause your event types to be statically served on **svix.com**. You can link or embed that site within your own documentation.

![event-catalog-config](https://docs.svix.com/assets/images/event-catalog-config-dbaa927b912771236e370df60d0fbb0f.png)

### Configuration Options[​](https://docs.svix.com/event-types#configuration-options "Direct link to Configuration Options")

- **Make Public:** Whether or not the Event Catalog will be publicly accessible from **svix.com**.
- **Display Name:** _Required to make your Event Catalog public._ The display name will be shown in the heading of the published page. It should be the name of your company or product.

### Using a custom domain[​](https://docs.svix.com/event-types#using-a-custom-domain "Direct link to Using a custom domain")

It is also possible to have the Event Catalog served from your own domain (e.g. `webhooks.example.com`) instead of the default `www.svix.com`.

To set this up, please set the following record on your DNS provider:

- Type: `CNAME`
- Name: `webhooks` (or whatever subdomain you would like to use)
- Value: `event-types.svix.com`

And [let us know](https://www.svix.com/contact/) once you do so that we can activate it on our end.

### What it looks like[​](https://docs.svix.com/event-types#what-it-looks-like "Direct link to What it looks like")

![event-catalog-published](https://docs.svix.com/assets/images/event-catalog-published-ec269ec7bc9569e55fd7bfa28022f5cd.png)

## Event type feature flags[​](https://docs.svix.com/event-types#event-type-feature-flags "Direct link to Event type feature flags")

When introducing new features in your application it can be useful to only expose them to a select set of users.

Event types can be hidden from users by setting a feature flag on them. If a feature flag is set on an event type users won't see the event type in the Event Catalog and the API, unless they have an authorization token that explicitly allows them.

A feature flag is an arbitrary string that you can set at event type creation time. You can also remove or add back the feature flag by updating an existing event type. Feature flags follow the same naming rules as the event type name itself: `^[a-zA-Z0-9\\-_.]+$`.

Here's how you can create an event type with a feature flag:

- JavaScript
- Python
- Rust
- Go
- Java
- Kotlin
- Ruby
- C#
- CLI
- cURL

```
import { Svix } from "svix";const svix = new Svix("AUTH_TOKEN");const eventType = await svix.eventType.create({  name: "user.signup",  description: "A user has signed up",  featureFlag: "beta-feature-a",});
```

If a user tries to retrieve this newly created event type they will get a not-found error. To give them access you need to give them an access token that explicitly allows them to see event types with the feature flag set during creation.

- JavaScript
- Python
- Rust
- Go
- Java
- Kotlin
- Ruby
- C#
- CLI
- cURL

```
const svix = new Svix("AUTH_TOKEN");const dashboard = await svix.authentication.appPortalAccess("app_Xzx8bQeOB1D1XEYmAJaRGoj0", {    featureFlags: ["beta-feature-a"]});// A URL that automatically logs user into the dashboardconsole.log(dashboard.url);
```

A user with this newly minted dashboard access token will be able to see this new event type.

Once you're ready to release this new event type to all of your users simply remove the feature flag from it.

- JavaScript
- Python
- Rust
- Go
- Java
- Kotlin
- Ruby
- C#
- CLI
- cURL

```
const svix = new Svix("AUTH_TOKEN");await svix.eventType.update("user.signup", { featureFlag: null });
```

Important

Keep in mind that endpoints with no event type filtering will receive all messages regardless of feature flags. Also, users with knowledge of a feature flag-gated event type can subscribe to that event type regardless of feature flags. Feature flags only impact event types' visibility in the catalog and the API, not their deliverability.

## Appendix[​](https://docs.svix.com/event-types#appendix "Direct link to Appendix")

### Generating an OpenAPI spec[​](https://docs.svix.com/event-types#generating-an-openapi-spec "Direct link to Generating an OpenAPI spec")

While most web frameworks support generating OpenAPI specs, not all of them support generating the new `webhooks` section (added in OpenAPI 3.1).

If your framework of choice already support webhooks you are probably good to go, just make sure that it generates it correctly. Here's an example of how a `pet.new` event type should look like in your OpenAPI file:

```
"webhooks": {  "pet.new": {    "post": {      "operationId": "pet.new",      "description": "A new pet has been added",      "x-svix-feature-flag": "beta-feature-a",  // Optional field      "x-svix-group-name": "group-name-1",  // Optional field      "requestBody": {        "content": {          "application/json": {            "schema": {              "type": "object",              "properties": {                "name": {                  "type": "string"                },                "tag": {                  "type": "integer"                }              }            },            "example": {              "name": "Buddy",              "tag": 1234            }          }        }      }    }  }}
```

If your framework doesn't support generating a `webhooks` section, or if your webhooks section is not generated correctly, you can resort to generating the section yourself using a simple post-processing script that adds the `webhooks` section to your spec.

One alternative is to write the section manually and then have code to inject it into the OpenAPI spec. A better alternative is to use your web framework's auto-generation to do some of the heavy lifting for you.

You can make your web framework generate the types for you by creating a dummy model that includes all of your submodels. Here is a Python example:

```
class PetNewEvent(pydantic.BaseModel):    """A new pet has been added"""    event_type = "pet.new"    foo: str    bar: str...class WebhookTypes(BaseModel):    """All of the webhook types that we support"""    a1: PetNewEvent    a2: PetChangedEvent    a3: PetDeletedEvent
```

This will generate the schemas for all of your event types. Once you have those, you can write a script to generate the `webhooks` for you, or you can generate it manually. The result should look something like this:

```
"webhooks": {  "pet.new": {    "post": {      "operationId": "pet.new",      "description": "A new pet has been added",      "requestBody": {        "content": {          "application/json": {            "schema": {              "$ref": "#/components/schemas/PetNewEvent"            },            "example": {              "event_type": "pet.new",              "foo": "Example 1",              "bar": "Example 2",            }          }        }      }    }  }  // ...}
```










# Channels

Channels are an extra dimension of filtering messages that is orthogonal to [event types](https://docs.svix.com/event-types). You can listen to multiple channels from each endpoint, and you can send each message to multiple channels.

Event types imply a specific consistent schema, and mean a specific type of message. Channels are filters based on the expected recipient or group of recipients.

**Note:** like event types, channels are meant to filter messages to a particular application, and not across applications.

## How to use it[​](https://docs.svix.com/channels#how-to-use-it "Direct link to How to use it")

You first need to enable support to it for each of your environments from [the dashboard](https://dashboard.svix.com/settings/organization/general-settings).

Once enabled, your customers can choose their wanted channels from the Application Portal, or alternatively you can set it per endpoint in the API.

![Add endpoint with channels](https://docs.svix.com/assets/images/channels-791e56e5259d3aa06ae4d4865b477fc1.png)

You then need to send messages with the corresponding channels in order for these to reach the endpoints that filter by them. See below for the channels filtering rules.

```
await svix.message.create('app_id', {    eventType: "user.signup",    channels: [        "project_123",        "project_group_11"    ],    payload: {        "username": "test_user",        "email": "<EMAIL>"    },});
```

## When not to use it[​](https://docs.svix.com/channels#when-not-to-use-it "Direct link to When not to use it")

The channels are not meant as a way to filter messages across different customers, that's what applications are for. Applications provide proper isolation between messages of different customers, while channels do not. Additionally, while you can have an infinite number of applications, there's a limit to the number of endpoints per application (more on that [on the Endpoint API docs](https://api.svix.com/docs#tag/Endpoint)).

Additionally, there are limits to the number of channels messages and endpoints can be associated with. And having applications with a large number of endpoints is very inefficient and can hurt performance.

## Example use-cases[​](https://docs.svix.com/channels#example-use-cases "Direct link to Example use-cases")

Channels are useful for when you have a variety of sub-categories or recipients that expect the same types of messages but just need additional filtering.

For example, consider Github. You may want to define webhooks for the whole organization, but only send certain events to certain endpoints based on the repository. You could just create a Svix App per repository and then manually add the endpoints to each, but it makes for a much better experience to have the webhook handling defined in one place with the same endpoints listening to multiple projects.

So for example, you can have `svix`, `svix/svix-webhooks` and `svix/svix-docs` as channels, and then have Github send messages for both `svix` and for each repo whenever an event occurs on a specific repository. Github's customers can then create endpoints that listen to events either for the whole group, or for each repository in particular.

## Channels filtering rules[​](https://docs.svix.com/channels#channels-filtering-rules "Direct link to Channels filtering rules")

Channels are case-sensitive, and endpoints that are filtering for specific channels will only get messages sent to that specific channel.

Svix will send (or not send) to endpoints based on the following conditions:

1. Endpoint has no channels set: this is a catch-all, all messages are sent to to it, regardless of whether the message had channels set.
2. Both endpoint and message have channels set: if there's a shared channel between them, the message will be sent to the endpoint.
3. Endpoint has channels set and message has _no_ channels set: the message will not be sent to the endpoint.



# Receiving Webhooks with Ingest

Receiving webhooks with [Svix Ingest](https://svix.com/ingest) starts by creating a `Source`.

A `Source` generates an endpoint you can share with a webhook provider as a destination for their webhooks.

Ingest supports signature verification schemes and flows used by a variety of webhook providers. Supported values for a `Source`'s `type` include:

- `beehiiv`
- `brex`
- `clerk`
- `github`
- `guesty`
- `hubspot`
- `incidentIo`
- `lithic`
- `nash`
- `pleo`
- `replicate`
- `resend`
- `safebase`
- `sardine`
- `segment`
- `shopify`
- `slack`
- `stripe`
- `stych`
- `svix` (that's us!)
- `zoom`
- `adobeSign`
- `docusign`

Additionally there's the option to configure a `Source` as `genericWebhook` to skip performing signature verification. This is useful for providers that have no verification scheme and for providers whose verification scheme is not yet supported by Ingest.

tip

Let us know if your webhook provider isn't yet supported so we can add it!

## Create a Source[​](https://docs.svix.com/receiving/receiving-with-ingest#create-a-source "Direct link to Create a Source")

Creating a `Source` from the [Ingest Dashboard](https://dashboard.svix.com/ingest):

![screenshot of the Ingest Dashboard showing the overview tab for a Source named &quot;demo&quot;](https://docs.svix.com/assets/images/source-edit-54fbfbfa8fa821b7e1132a3423ba0ed6.png)

- JavaScript
- Python
- Rust
- Go
- Java
- Kotlin
- Ruby
- C#
- CLI
- cURL

```
const svix = new Svix("AUTH_TOKEN");const ingestSourceOut = await svix.ingest.source.create({  name: "myGithubWebhook",  uid: "unique-identifier",  type: "github",  config: {      secret: "SECRET"  },});
```

## Tell your provider where to send webhooks[​](https://docs.svix.com/receiving/receiving-with-ingest#tell-your-provider-where-to-send-webhooks "Direct link to Tell your provider where to send webhooks")

The Ingest URL is also listed on the [Ingest Dashboard](https://dashboard.svix.com/ingest) for each `Source`.

The `IngestSourceOut` response from the API will include an `ingestUrl` which is the endpoint you give to your provider, telling them where to send their webhooks.

For GitHub, as is used in this example, this is referred to as the _"Payload URL"_.

## Managing incoming messages[​](https://docs.svix.com/receiving/receiving-with-ingest#managing-incoming-messages "Direct link to Managing incoming messages")

In the [Ingest Dashboard](https://dashboard.svix.com/ingest), the Destinations tab for your `Source` is where you can configure endpoints, view logs, inspect message payloads, etc.

![screenshot of the Ingest Dashboard showing the Destination/Endpoints tab for a Source named &quot;demo&quot;](https://docs.svix.com/assets/images/destination-endpoints-e333c0a0b44c86adee25c00bfb14eca3.png)

Configuring endpoints allows you to forward messages received by Ingest over to the endpoints you choose using Svix Core.

![screenshot of the Ingest Dashboard showing the Destination/Logs tab for a Source named &quot;demo&quot;](https://docs.svix.com/assets/images/destination-logs-13de0bb30e9f0688a2dd0e131bb94dfd.png)

Logs and statistics are available to help monitor for problems, replay or recover messages.

## Managing Source Tokens[​](https://docs.svix.com/receiving/receiving-with-ingest#managing-source-tokens "Direct link to Managing Source Tokens")

The last portion of the Ingest URL is a `Token` which can be invalidated and rotated:

![screenshot of the Ingest Dashboard showing the overview tab for a Source named &quot;demo&quot;](https://docs.svix.com/assets/images/url-token-rotate-d7ab0cfc12d26113dc6a1969c9e0b783.png)

`Token`s that are rotated stay usable for 24 hours. During this time both the old and new `Token`s are honored by Ingest. During this period it's important that you reconfigure your provider with the new Ingest URL in order to have a seamless transition.





