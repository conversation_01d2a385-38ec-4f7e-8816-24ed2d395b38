# Svix Webhook Integration Guide

Este guia explica como configurar e usar a integração com Svix para gerenciamento de webhooks na Pluggou PIX API.

## Visão Geral

A Pluggou PIX API agora utiliza o Svix para gerenciamento de webhooks, substituindo a implementação anterior baseada em NSQ. O Svix oferece:

- Entrega confiável de webhooks com retentativas automáticas
- Verificação de assinaturas para segurança
- Dashboard para monitoramento e depuração
- Filtragem de eventos por tipo e destinatário

## Configuração

### 1. Configurar Credenciais Svix

Você precisa configurar as seguintes variáveis de ambiente:

```bash
# Para desenvolvimento local
encore secret set --type local SVIX_TOKEN
encore secret set --type local SVIX_WEBHOOK_SECRET

# Para produção
encore secret set --type production SVIX_TOKEN
encore secret set --type production SVIX_WEBHOOK_SECRET
```

### 2. Configurar Endpoints no Svix Dashboard

1. Acesse o dashboard do Svix em https://webhooks.cloud.pluggou.io/
2. Crie uma aplicação para seu serviço (se ainda não existir)
3. Adicione endpoints para receber webhooks:
   - URL: URL do seu serviço que receberá os webhooks
   - Versão: Versão da API (geralmente `1`)
   - Descrição: Descrição do endpoint
   - Canais: Inscreva-se em `flow2pay-events` para receber todos os eventos
   - Canais de evento: Opcionalmente, inscreva-se em canais específicos por usuário (`user-{id}`)

## Tipos de Eventos

A API mapeia os eventos da Flow2Pay para os seguintes tipos de eventos no Svix:

- `PixIn.Processing`: Pagamento PIX recebido em processamento
- `PixIn.Confirmation`: Pagamento PIX recebido confirmado
- `PixOut.Processing`: Transferência PIX em processamento
- `PixOut.Confirmation`: Transferência PIX confirmada
- `PixOut.Failure`: Transferência PIX falhou
- `PixInReversal.Processing`: Estorno de PIX recebido em processamento
- `PixInReversal.Confirmation`: Estorno de PIX recebido confirmado
- `PixOutReversalExternal`: Estorno de PIX enviado

## Recebendo Webhooks

### Endpoint Svix

A API expõe um endpoint para receber webhooks do Svix:

```
POST /svix-webhook
```

Este endpoint:
1. Verifica a assinatura do webhook usando o cabeçalho `svix-signature`
2. Extrai o payload da Flow2Pay do webhook
3. Processa o webhook da mesma forma que o endpoint `/baas` direto da Flow2Pay

### Formato do Payload

O payload do webhook do Svix contém:

```json
{
  "event_type": "PixIn.Confirmation",
  "payload": {
    "event_type": "PixIn",
    "flow2pay_event_type": "PixIn",
    "svix_event_type": "PixIn.Confirmation",
    "user_id": "clrz1234abcd",
    "amount": 1000,
    "flow2pay_id": "txid123456",
    "end_to_end_id": "E12345678901234567890123456",
    "webhook_payload": {
      // Payload original da Flow2Pay
      "evento": "PixIn",
      "txid": "txid123456",
      "valor": 1000,
      "horario": "2023-01-01T12:00:00Z",
      "status": "Sucesso",
      // ... outros campos
    },
    "timestamp": "2023-01-01T12:00:00Z",
    "status": "Sucesso",
    "event_id": "PixIn-txid123456"
  }
}
```

## Verificação de Assinaturas

O Svix assina todos os webhooks com uma assinatura HMAC. A API verifica automaticamente esta assinatura usando os cabeçalhos:

- `svix-id`: ID único do webhook
- `svix-timestamp`: Timestamp do webhook
- `svix-signature`: Assinatura HMAC do webhook

## Migração do NSQ para Svix

A migração do NSQ para Svix foi implementada de forma transparente:

1. O endpoint `/baas` agora envia eventos para o Svix em vez do NSQ
2. Os consumidores NSQ existentes foram substituídos por endpoints Svix
3. O formato do payload foi mantido compatível para facilitar a migração

## Depuração

Para depurar problemas com webhooks:

1. Verifique os logs da API para erros ao enviar mensagens para o Svix
2. Consulte o dashboard do Svix para verificar o status de entrega dos webhooks
3. Verifique a tabela `flow2pay_webhooks` no banco de dados para o status de processamento

## Referências

- [Documentação oficial do Svix](https://docs.svix.com/)
- [Dashboard do Svix da Pluggou](https://webhooks.cloud.pluggou.io/)
