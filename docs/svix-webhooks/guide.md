
### Compreensão do Cenário
- **Sua Pix API**: Uma API que se comunica com a Flow2Pay, seu gateway de Pix parceiro, para gerenciar recebimentos e transferências de Pix.
- **Aplicação Full Stack (`app.pluggou.io`)**: Um sistema multiempresa baseado em Next.js (frontend) e Hono (API backend) com PostgreSQL, onde cada empresa pode:
  - Receber pagamentos Pix (via QR Code ou outros meios).
  - Enviar transferências Pix.
  - Usar sua plataforma como sistema de gestão de pagamentos.
  - Oferecer acesso via API para que os clientes dessas empresas também utilizem os serviços.
- **Webhooks**: Você precisa que:
  - A Flow2Pay envie webhooks com eventos de Pix (ex.: `PixIn`, `PixOut`, `PixInReversal`, `PixOutReversalExternal`) para sua aplicação.
  - Os clientes das empresas (via API) possam escutar webhooks para eventos específicos (pagamento confirmado, transferência e estornos).
- **Svix**: Você quer usar o Svix para:
  - Centralizar o recebimento de webhooks da Flow2Pay.
  - Padronizar e gerenciar o envio de webhooks para os clientes das empresas.
- **Eventos Criados**: Você já criou os tipos de eventos no Svix (conforme a lista fornecida), incluindo:
  - `PixIn.Processing`, `PixIn.Confirmation`
  - `PixOut.Processing`, `PixOut.Confirmation`, `PixOut.Failure`
  - `PixInReversal.Processing`, `PixInReversal.Confirmation`
  - `PixOutReversalExternal`

### Próximos Passos
Agora que os eventos estão criados, vamos continuar configurando o fluxo. O próximo passo é:

1. **Configurar um Endpoint no Svix para Receber Webhooks da Flow2Pay**:
   - Este endpoint receberá os webhooks enviados pela Flow2Pay e os encaminhará para sua aplicação (`app.pluggou.io/api/pluggou-pix`).
2. **Integrar sua Aplicação (`app.pluggou.io`) com o Svix**:
   - Criar um endpoint na sua API Hono para processar os webhooks recebidos do Svix.
   - Atualizar o banco de dados PostgreSQL com base nos eventos (ex.: registrar pagamentos, transferências, estornos por empresa).
3. **Configurar Endpoints no Svix para Clientes**:
   - Permitir que os clientes das empresas se inscrevam para receber webhooks específicos (ex.: apenas `PixIn.Confirmation`).
4. **Cadastrar o Webhook na Flow2Pay**:
   - Informar à Flow2Pay a URL do endpoint Svix para receber os webhooks.

### Passo a Passo com Payloads

#### **Passo 1: Configurar Endpoint no Svix para Receber Webhooks da Flow2Pay**
- **Endpoint**: `POST {{baseUrl}}/api/v1/app/app_2xJGtEh9B3sOptpW4thRObvtlh9/endpoint`
- **Payload**:
  ```json
  {
    "url": "https://app.pluggou.io/api/pluggou-pix",
    "description": "Recebe webhooks da Flow2Pay para processamento de eventos Pix",
    "rateLimit": 1000,
    "uid": "flow2pay-webhook-endpoint",
    "disabled": false,
    "filterTypes": [
      "PixIn.Processing",
      "PixIn.Confirmation",
      "PixOut.Processing",
      "PixOut.Confirmation",
      "PixOut.Failure",
      "PixInReversal.Processing",
      "PixInReversal.Confirmation",
      "PixOutReversalExternal"
    ],
    "channels": [
      "flow2pay-events"
    ],
    "secret": "whsec_4kT9pL2mWqR8vX0nJ5uY3tE7hD1cG6bF",
    "metadata": {
      "source": "flow2pay",
      "app": "pluggou"
    },
    "headers": {
      "adipisicing52": "flow2pay-header-value"
    }
  }
  ```
- **Cabeçalhos**:
  - `Authorization: Bearer your-svix-jwt-token`
  - `Content-Type: application/json`
- **Notas**: Substitua `your-svix-jwt-token` pelo token gerado para sua aplicação no Svix. O `secret` é um exemplo; gere um novo ou deixe como `null` para que o Svix crie automaticamente.

#### **Passo 2: Integrar sua Aplicação com o Svix**
- **Endpoint na API Hono**: Crie um endpoint em `app.pluggou.io/api/pluggou-pix` para processar os webhooks.
- **Exemplo de Código (Hono)**:
  ```javascript
  import { Hono } from 'hono';

  const app = new Hono();

  app.post('/api/pluggou-pix', async (c) => {
    const body = await c.req.json();
    const signature = c.req.header('svix-signature');
    const timestamp = c.req.header('svix-timestamp');

    // Valide a assinatura do webhook (usando a biblioteca Svix)
    // const svix = new Svix('YOUR_SVIX_API_KEY');
    // const payload = JSON.stringify(body);
    // const valid = svix.verify(payload, signature, timestamp, 'whsec_4kT9pL2mWqR8vX0nJ5uY3tE7hD1cG6bF');

    // if (!valid) return c.text('Invalid signature', 400);

    // Processar o evento
    const { evento, status, valor, endToEndId, empresaId } = body; // Ajuste conforme o payload
    if (empresaId) {
      // Atualizar banco de dados (exemplo com PostgreSQL via Prisma)
      await prisma.transacao.create({
        data: {
          tipo: evento,
          status,
          valor,
          endToEndId,
          empresaId,
          createdAt: new Date(),
        },
      });
    }

    return c.json({ success: true, received: evento });
  });

  export default app;
  ```
- **Notas**:
  - Instale dependências como `hono` e `prisma`.
  - Adicione validação de assinatura usando a biblioteca `@svix/webhooks`.
  - Ajuste `empresaId` para identificar a empresa no payload (se a Flow2Pay enviar isso).
  - Configure o Prisma para mapear as transações no PostgreSQL.

#### **Passo 3: Configurar Endpoints no Svix para Clientes**
- **Endpoint**: `POST {{baseUrl}}/api/v1/app/app_2xJGtEh9B3sOptpW4thRObvtlh9/endpoint`
- **Payload para Cliente Exemplo (Empresa 1)**:
  ```json
  {
    "url": "https://empresa1.com/webhook/pix",
    "description": "Recebe notificações de eventos Pix para a Empresa 1",
    "rateLimit": 500,
    "uid": "empresa1-endpoint",
    "disabled": false,
    "filterTypes": [
      "PixIn.Confirmation",
      "PixOut.Confirmation",
      "PixInReversal.Confirmation"
    ],
    "channels": [
      "empresa1-events"
    ],
    "secret": "whsec_8mP3vL9qT2wY5nR0kX7uJ4tH6eD2cB1a",
    "metadata": {
      "cliente": "empresa1"
    },
    "headers": {
      "adipisicing52": "empresa1-header-value"
    }
  }
  ```
- **Payload para Cliente Exemplo (Empresa 2 - Apenas Pagamentos)**:
  ```json
  {
    "url": "https://empresa2.com/webhook/pix",
    "description": "Recebe notificações de pagamentos Pix para a Empresa 2",
    "rateLimit": 500,
    "uid": "empresa2-endpoint",
    "disabled": false,
    "filterTypes": [
      "PixIn.Confirmation"
    ],
    "channels": [
      "empresa2-events"
    ],
    "secret": "whsec_5nQ8rK1pV3xT6mW9jY2uH7eL4dC0bA1f",
    "metadata": {
      "cliente": "empresa2"
    },
    "headers": {
      "adipisicing52": "empresa2-header-value"
    }
  }
  ```
- **Cabeçalhos**:
  - `Authorization: Bearer your-svix-jwt-token`
  - `Content-Type: application/json`

#### **Passo 4: Cadastrar o Webhook na Flow2Pay**
- **Ação**: Envie um e-mail ao suporte da Flow2Pay.
- **Exemplo de E-mail**:
  ```
  Assunto: Cadastro de URL para Webhooks

  Prezados,

  Gostaria de cadastrar a seguinte URL para receber os webhooks de eventos Pix (PixIn, PixOut, PixInReversal, PixOutReversalExternal):

  URL: https://<seu-svix-dominio>/api/v1/app/app_2xJGtEh9B3sOptpW4thRObvtlh9/endpoint/<endpoint-id>

  Atenciosamente,
  [Seu Nome]
  ```
- **Notas**: Substitua `<seu-svix-dominio>` e `<endpoint-id>` pela URL gerada após criar o endpoint no Passo 1. Envie para `<EMAIL>`.

### Considerações
- **Horário Atual**: É 07:54 AM -03, 19 de maio de 2025. Certifique-se de que os horários nos logs e eventos estejam sincronizados.
- **Segurança**: Valide as assinaturas dos webhooks no backend usando o `secret` do Svix.
- **Escalabilidade**: Ajuste `rateLimit` conforme o volume de eventos esperado por empresa.
- **Multiempresa**: Adicione lógica no backend para associar eventos a empresas específicas no PostgreSQL (ex.: via `empresaId`).

