# Using drizzle-cuid2 in Your Schema

This document provides examples of how to update your schema.ts file to use the drizzle-cuid2 package for better type safety and developer experience.

## Installation

First, install the drizzle-cuid2 package:

```bash
npm install drizzle-cuid2
```

## Schema Update Example

Here's an example of how to update your schema.ts file to use the drizzle-cuid2 package:

```typescript
import {
  pgTable,
  timestamp,
  boolean,
  jsonb,
  integer,
  pgSchema,
  text,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { cuid2 } from 'drizzle-cuid2';

// Create pixapi schema
export const pixapiSchema = pgSchema('pixapi');

// Users table
export const users = pgTable('users', {
  id: cuid2('id').primaryKey(),
  email: text('email').notNull().unique(),
  name: text('name').notNull(),
  partnerId: text('partner_id').notNull(), // 'flow2pay' or other future payment partners
  createdAt: timestamp('created_at', { withTimezone: true })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp('updated_at', { withTimezone: true })
    .notNull()
    .defaultNow(),
});

// QR codes table
export const qrCodes = pgTable('qr_codes', {
  id: cuid2('id').primaryKey(),
  userId: cuid2('user_id').references(() => users.id),
  txid: text('txid').notNull().unique(),
  amount: integer('amount').notNull(), // Amount in cents
  description: text('description'),
  expirationTime: integer('expiration_time').notNull(), // Expiration time in seconds
  qrCodeImage: text('qr_code_image'), // Base64 encoded QR code image
  qrCodeText: text('qr_code_text').notNull(), // PIX code string (can be copied by user)
  status: text('status').notNull(), // 'pending', 'completed', 'expired', 'canceled'
  partnerId: text('partner_id'), // Which payment partner was used
  externalId: text('external_id'), // Additional ID for reference if needed
  createdAt: timestamp('created_at', { withTimezone: true })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp('updated_at', { withTimezone: true })
    .notNull()
    .defaultNow(),
});

// Transactions table
export const transactions = pgTable('transactions', {
  id: cuid2('id').primaryKey(),
  userId: cuid2('user_id')
    .notNull()
    .references(() => users.id),
  qrCodeId: cuid2('qr_code_id').references(() => qrCodes.id), // Optional, only for PIX received via QR code
  type: text('type').notNull(), // 'pix_in', 'pix_out', 'pix_in_reversal', 'pix_out_reversal'
  amount: integer('amount').notNull(), // Amount in cents
  currency: text('currency').notNull().default('BRL'),
  status: text('status').notNull(), // 'pending', 'completed', 'failed', 'reversed'
  description: text('description'),
  flow2payTxid: text('flow2pay_txid'), // For QR code payments (PIX in)
  flow2payEndToEndId: text('flow2pay_end_to_end_id'), // Unique PIX identifier
  flow2payIdEnvio: text('flow2pay_id_envio'), // For direct transfers (PIX out)
  webhookPayload: jsonb('webhook_payload'), // Store the raw webhook data for auditing
  createdAt: timestamp('created_at', { withTimezone: true })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp('updated_at', { withTimezone: true })
    .notNull()
    .defaultNow(),
});

// Continue updating the rest of your schema...
```

## Benefits of Using drizzle-cuid2

1. **Type Safety**: The `cuid2` function returns a properly typed column, ensuring type safety throughout your application.

2. **Developer Experience**: Better autocompletion and type checking in your IDE.

3. **Consistency**: Ensures consistent usage of CUID2 across your schema.

4. **Integration with Drizzle ORM**: Seamless integration with Drizzle's query builder.

## Migration Process

To migrate your existing schema to use drizzle-cuid2:

1. Update your schema.ts file as shown above.

2. Generate a new migration:

```bash
npm run db:generate
```

3. Review the generated migration to ensure it doesn't make any unnecessary changes to your database schema.

4. Apply the migration:

```bash
npm run db:migrate
```

## Note on Database Performance

Using drizzle-cuid2 doesn't change the underlying storage type in PostgreSQL - IDs are still stored as text. To optimize performance with text-based IDs, make sure to:

1. Apply the index optimization migration:

```bash
npm run db:optimize
```

2. Monitor query performance and add additional indexes as needed.

For more information on ID type optimization, see the [ID Type Optimization Analysis](./id-type-optimization.md) document.
