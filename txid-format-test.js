#!/usr/bin/env node

/**
 * TxID Format Test
 * 
 * This script tests various TxID formats to determine which ones are accepted by the Flow2Pay API.
 * It helps identify the correct format for transaction IDs when generating PIX QR codes.
 * 
 * @version 1.0.0
 * <AUTHOR> Team
 */

const fs = require('fs');
const path = require('path');

// Configuration - Override with environment variables if needed
const CONFIG = {
  // API endpoints
  apiUrl: process.env.API_URL || 'https://apipix.cloud.pluggou.io',
  apiKey: process.env.API_KEY || '19af7b34417f24a7f29d333dfc3dcb473d60e18ff91a712ffc07475a5631ea99',
  
  // Test parameters
  outputDir: './test-results',
  verbose: process.env.VERBOSE === 'true'
};

// Flow2Pay example TxID from documentation
const FLOW2PAY_EXAMPLE_TXID = 'wc8lrivmecuugia5id2y5gyo4yy91jqeanh';

/**
 * Ensures the output directory exists
 */
function ensureOutputDir() {
  if (!fs.existsSync(CONFIG.outputDir)) {
    fs.mkdirSync(CONFIG.outputDir, { recursive: true });
  }
}

/**
 * Generates a TxID with the specified format
 * @param {string} format - Format type ('lowercase', 'uppercase', 'mixed', 'example_clone', 'pix_prefix')
 * @param {number} length - Length of the TxID (default: 30)
 * @returns {string} Generated TxID
 */
function generateTxId(format, length = 30) {
  // Character sets for different formats
  const LOWERCASE = 'abcdefghijklmnopqrstuvwxyz';
  const UPPERCASE = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const NUMBERS = '0123456789';
  
  let chars = '';
  let prefix = '';
  
  // Determine character set and prefix based on format
  switch (format) {
    case 'lowercase':
      chars = LOWERCASE + NUMBERS;
      break;
    case 'uppercase':
      chars = UPPERCASE + NUMBERS;
      break;
    case 'mixed':
      chars = LOWERCASE + UPPERCASE + NUMBERS;
      break;
    case 'example_clone':
      // Return a copy of the example with a random number at the end
      return FLOW2PAY_EXAMPLE_TXID.slice(0, -1) + Math.floor(Math.random() * 10);
    case 'pix_lowercase':
      prefix = 'pix';
      chars = LOWERCASE + NUMBERS;
      break;
    case 'pix_uppercase':
      prefix = 'PIX';
      chars = LOWERCASE + NUMBERS;
      break;
    default:
      chars = LOWERCASE + NUMBERS; // Default to lowercase (safest option)
  }
  
  // Generate random part
  let randomPart = '';
  const remainingLength = length - prefix.length;
  
  for (let i = 0; i < remainingLength; i++) {
    randomPart += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return prefix + randomPart;
}

/**
 * Tests a specific TxID format
 * @param {string} txId - TxID to test
 * @param {string} description - Description of the test
 * @returns {Promise<Object>} Test result
 */
async function testTxId(txId, description) {
  console.log(`\n🧪 Testing TxID: "${txId}"`);
  console.log(`Description: ${description}`);
  console.log(`Length: ${txId.length} characters`);
  console.log(`Format validation: ${/^[a-zA-Z0-9]{26,35}$/.test(txId) ? '✅ Valid' : '❌ Invalid'}`);
  console.log(`Lowercase format: ${/^[a-z0-9]{26,35}$/.test(txId) ? '✅ Yes' : '❌ No'}`);
  
  try {
    // Prepare request payload
    const payload = {
      txId: txId,
      informacaoAdicional: `Test: ${description}`,
      valor: 100,
      tempoExpiracao: 3600,
      comImagem: true
    };
    
    if (CONFIG.verbose) {
      console.log('\nPayload:');
      console.log(JSON.stringify(payload, null, 2));
    }
    
    // Send request to API
    console.log('\n🔄 Sending request...');
    const response = await fetch(`${CONFIG.apiUrl}/qrcode-passthrough`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': CONFIG.apiKey
      },
      body: JSON.stringify(payload)
    });
    
    // Process response
    const statusCode = response.status;
    let responseData;
    
    try {
      responseData = await response.json();
    } catch (e) {
      responseData = await response.text();
    }
    
    if (response.ok && responseData.success) {
      console.log(`✅ SUCCESS! TxID format accepted.`);
      
      if (CONFIG.verbose) {
        console.log('\nResponse:');
        console.log(JSON.stringify(responseData, null, 2));
      }
      
      return { 
        success: true, 
        txId, 
        description,
        statusCode
      };
    } else {
      console.log(`❌ FAILED! TxID format rejected.`);
      
      if (CONFIG.verbose) {
        console.log('\nError details:');
        console.log(JSON.stringify(responseData, null, 2));
      }
      
      return { 
        success: false, 
        txId, 
        description,
        statusCode,
        error: responseData.error || 'Unknown error'
      };
    }
  } catch (error) {
    console.error(`❌ ERROR: ${error.message}`);
    return { 
      success: false, 
      txId, 
      description,
      error: error.message
    };
  }
}

/**
 * Tests multiple TxID formats
 * @returns {Promise<Array>} Test results
 */
async function testMultipleFormats() {
  console.log('🔍 TxID Format Test Suite');
  console.log('=======================');
  
  // Define test cases
  const testCases = [
    {
      txId: FLOW2PAY_EXAMPLE_TXID,
      description: 'Flow2Pay example TxID'
    },
    {
      txId: generateTxId('lowercase'),
      description: 'Lowercase letters and numbers'
    },
    {
      txId: generateTxId('uppercase'),
      description: 'Uppercase letters and numbers'
    },
    {
      txId: generateTxId('mixed'),
      description: 'Mixed case letters and numbers'
    },
    {
      txId: generateTxId('example_clone'),
      description: 'Clone of Flow2Pay example with slight variation'
    },
    {
      txId: generateTxId('pix_lowercase'),
      description: 'Lowercase "pix" prefix + random characters'
    },
    {
      txId: generateTxId('pix_uppercase'),
      description: 'Uppercase "PIX" prefix + random characters'
    },
    {
      txId: generateTxId('lowercase', 26),
      description: 'Minimum length (26 characters)'
    },
    {
      txId: generateTxId('lowercase', 35),
      description: 'Maximum length (35 characters)'
    }
  ];
  
  // Run tests
  const results = [];
  
  for (const testCase of testCases) {
    const result = await testTxId(testCase.txId, testCase.description);
    results.push(result);
  }
  
  return results;
}

/**
 * Analyzes test results and provides recommendations
 * @param {Array} results - Test results
 */
function analyzeResults(results) {
  console.log('\n📊 Test Results Summary:');
  console.log('=======================');
  
  let successCount = 0;
  let failureCount = 0;
  
  results.forEach((result, index) => {
    if (result.success) {
      successCount++;
      console.log(`✅ Test ${index + 1}: SUCCESS - "${result.description}"`);
    } else {
      failureCount++;
      console.log(`❌ Test ${index + 1}: FAILED - "${result.description}"`);
    }
  });
  
  console.log(`\nTotal: ${results.length} tests, ${successCount} passed, ${failureCount} failed`);
  
  // Generate recommendations
  console.log('\n🔍 Analysis and Recommendations:');
  
  // Check if the Flow2Pay example works
  const exampleResult = results.find(r => r.txId === FLOW2PAY_EXAMPLE_TXID);
  if (exampleResult && exampleResult.success) {
    console.log('✅ The Flow2Pay example TxID works correctly.');
  } else if (exampleResult) {
    console.log('❌ The Flow2Pay example TxID failed. This suggests an issue with the API or authentication.');
  }
  
  // Check which formats work
  const lowercaseResult = results.find(r => r.description.includes('Lowercase letters'));
  const uppercaseResult = results.find(r => r.description.includes('Uppercase letters'));
  const mixedResult = results.find(r => r.description.includes('Mixed case'));
  
  if (lowercaseResult && lowercaseResult.success) {
    console.log('✅ Lowercase alphanumeric TxIDs are accepted.');
  }
  
  if (uppercaseResult && uppercaseResult.success) {
    console.log('✅ Uppercase alphanumeric TxIDs are accepted.');
  }
  
  if (mixedResult && mixedResult.success) {
    console.log('✅ Mixed case alphanumeric TxIDs are accepted.');
  }
  
  // Check prefix formats
  const pixLowerResult = results.find(r => r.description.includes('Lowercase "pix" prefix'));
  const pixUpperResult = results.find(r => r.description.includes('Uppercase "PIX" prefix'));
  
  if (pixLowerResult && pixLowerResult.success) {
    console.log('✅ TxIDs with lowercase "pix" prefix are accepted.');
  }
  
  if (pixUpperResult && pixUpperResult.success) {
    console.log('✅ TxIDs with uppercase "PIX" prefix are accepted.');
  }
  
  // Check length constraints
  const minLengthResult = results.find(r => r.description.includes('Minimum length'));
  const maxLengthResult = results.find(r => r.description.includes('Maximum length'));
  
  if (minLengthResult && minLengthResult.success) {
    console.log('✅ Minimum length (26 characters) TxIDs are accepted.');
  }
  
  if (maxLengthResult && maxLengthResult.success) {
    console.log('✅ Maximum length (35 characters) TxIDs are accepted.');
  }
  
  // Final recommendation
  console.log('\n📝 Recommended TxID format:');
  
  if (lowercaseResult && lowercaseResult.success) {
    console.log('Based on the test results, the recommended TxID format is:');
    console.log('- 30 characters (safe middle of 26-35 range)');
    console.log('- Lowercase letters and numbers only');
    console.log('- Example implementation:');
    console.log(`
function generateTxId() {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 30; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}
    `);
  } else if (exampleResult && exampleResult.success) {
    console.log('Based on the test results, you should use the exact format from the Flow2Pay example:');
    console.log(`- Format similar to: ${FLOW2PAY_EXAMPLE_TXID}`);
    console.log('- 32 characters');
    console.log('- Lowercase letters and numbers only');
  } else {
    console.log('❌ None of the tested formats worked consistently. Please check your API configuration and credentials.');
  }
  
  // Save results to file
  ensureOutputDir();
  const resultsFile = path.join(CONFIG.outputDir, 'txid-test-results.json');
  fs.writeFileSync(resultsFile, JSON.stringify(results, null, 2));
  console.log(`\n💾 Test results saved to: ${resultsFile}`);
}

/**
 * Main function
 */
async function main() {
  try {
    // Run tests for multiple TxID formats
    const results = await testMultipleFormats();
    
    // Analyze results and provide recommendations
    analyzeResults(results);
    
    console.log('\n🎉 All tests completed!');
  } catch (error) {
    console.error(`\n❌ Fatal error: ${error.message}`);
    process.exit(1);
  }
}

// Run the main function
main().catch(error => {
  console.error('\n❌ Unhandled error:', error);
  process.exit(1);
});
