import axios, { AxiosError } from 'axios';

/**
 * Configured axios client for Flow2Pay API with SOCKS proxy support for SSH tunneling
 */
export const flow2payClient = axios.create({
  baseURL: 'https://api.flow2pay.com',
  proxy: {
    host: '127.0.0.1',
    port: 1080,
    protocol: 'socks'
  },
  timeout: 10000
});

/**
 * Tests the SSH tunnel connection by making a request to Flow2Pay API
 * @returns Promise<boolean> True if the connection is successful
 */
export async function testTunnelConnection(): Promise<boolean> {
  try {
    // Make a simple request to test the connection
    const response = await flow2payClient.get('/health');
    return response.status === 200;
  } catch (error: unknown) {
    const axiosError = error as AxiosError;
    console.error('Error testing tunnel connection:', axiosError.message);
    return false;
  }
}
