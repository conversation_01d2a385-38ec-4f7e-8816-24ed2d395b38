FROM node:20-slim

# Install PostgreSQL client for database initialization
RUN apt-get update && apt-get install -y postgresql-client && rm -rf /var/lib/apt/lists/*

# Install Encore CLI
RUN curl -s https://encore.dev/install.sh | bash

# Set working directory
WORKDIR /app

# Copy the entire project
COPY . .

# Make the startup script executable
RUN chmod +x start.sh

# Expose the application port
EXPOSE 4000

# Set environment variables
ENV PORT=4000
ENV ENCORE_FORCE_MIGRATIONS=true
ENV ENCORE_RUNTIME_APPLY_MIGRATIONS=true
ENV ENCORE_RUNTIME_MIGRATION_TIMEOUT=60s
ENV PATH="/root/.encore/bin:${PATH}"

# Use our custom startup script
CMD ["./start.sh"]
