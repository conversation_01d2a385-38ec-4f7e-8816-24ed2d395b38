{"name": "pix-api", "version": "1.0.0", "description": "PIX Gateway API", "main": "index.js", "scripts": {"start": "encore run", "dev": "encore run --watch", "dev:tunnel": "node -r ./proxy-bootstrap.js $(which encore) run --watch", "test:auth": "./check-flow2pay-url.sh", "test:proxy": "./test-proxy.sh", "test:global-proxy": "node test-global-proxy.js", "setup:tunnel": "./setup-tunnel.sh", "db:init": "./scripts/initialize-drizzle-migrations.sh", "db:generate": "./scripts/generate-migration.sh", "db:migrate": "./scripts/apply-drizzle-migrations.sh", "db:studio": "npx drizzle-kit studio", "db:push": "npx drizzle-kit push:pg", "db:generate-cuid": "./scripts/generate-cuid-migrations.sh", "db:fix-ids": "./scripts/apply-id-fix.sh", "db:reset": "./scripts/reset-database.sh", "db:fresh": "./scripts/apply-fresh-schema.sh", "db:cleanup": "./scripts/cleanup-migrations.sh", "db:optimize": "./scripts/optimize-indexes.sh", "db:quick-init": "./scripts/quick-db-init.sh", "admin:create": "./scripts/create-admin-user.sh", "build:docker": "./scripts/build-and-push.sh"}, "engines": {"node": ">=20.0.0"}, "dependencies": {"@paralleldrive/cuid2": "^2.2.2", "@types/uuid": "^10.0.0", "axios": "^1.9.0", "crypto": "^1.0.1", "dotenv": "^16.5.0", "drizzle-cuid2": "^2.0.0", "drizzle-kit": "^0.31.1", "drizzle-orm": "^0.43.1", "encore.dev": "^1.46.22", "global-agent": "^3.0.0", "pg": "^8.16.0", "socks-proxy-agent": "^8.0.5", "svix": "^1.65.0", "uuid": "^11.1.0"}, "devDependencies": {"@types/node": "^20.17.46", "@types/pg": "^8.15.2", "tsx": "^4.19.4", "typescript": "^5.0.0", "vite": "^6.3.5"}, "keywords": ["encore", "pix", "payment", "gateway"], "author": "", "license": "MIT"}