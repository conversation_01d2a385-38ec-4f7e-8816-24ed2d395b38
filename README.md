# Pluggou PIX API

Uma API robusta e segura para transações PIX integrada com o serviço Flow2Pay, construída com Encore.ts.

![PIX Logo](https://www.bcb.gov.br/content/estabilidadefinanceira/pix/logo_pix.png)

## Visão Geral

A Pluggou PIX API oferece uma solução completa para integração com o sistema de pagamentos PIX através do serviço Flow2Pay, com recursos como:

- Geração de QR codes para recebimento de pagamentos
- Transferências PIX para outras contas
- Consulta de saldo
- Histórico de transações
- Webhooks para notificações em tempo real
- Tratamento abrangente de erros e retentativas

## Começando

### Pré-requisitos

- Node.js 20+ instalado
- Docker instalado e em execução
- Credenciais Flow2Pay (Client ID e Secret)

### Configuração do Ambiente

Configure os segredos necessários:

```bash
encore secret set --type local FLOW2PAY_CLIENT_ID
encore secret set --type local FLOW2PAY_CLIENT_SECRET
encore secret set --type local FLOW2PAY_EVENT_TOKEN
encore secret set --type local SVIX_TOKEN
encore secret set --type local SVIX_WEBHOOK_SECRET
```

### Configurando o Túnel SSH para Desenvolvimento Local

Para desenvolvimento local, é necessário configurar um túnel SSH para que as requisições à API Flow2Pay sejam roteadas através do seu servidor EC2, já que a Flow2Pay só aceita requisições do IP do servidor.

1. Configure o túnel SSH usando o arquivo .pem:

```bash
# Método simples
ssh -i "pluggou-baas.pem" -D 1080 <EMAIL>

# OU usando autossh para conexão persistente (recomendado)
autossh -M 0 -i "pluggou-baas.pem" -o "ServerAliveInterval 30" -o "ServerAliveCountMax 3" -D 1080 <EMAIL>
```

2. Ou use nosso script auxiliar aprimorado:

```bash
# Torne o script executável (apenas na primeira vez)
chmod +x scripts/setup-ssh-tunnel.sh

# Execute o script
./scripts/setup-ssh-tunnel.sh -i "pluggou-baas.pem"

# Para uma conexão persistente (recomendado)
./scripts/setup-ssh-tunnel.sh -i "pluggou-baas.pem" -a
```

3. Teste o túnel e os endpoints:

```bash
# Instale as dependências necessárias (se ainda não tiver feito)
npm install axios socks-proxy-agent

# Teste básico do túnel
node test-tunnel.js

# Teste completo de todos os endpoints do serviço pixapi-simplified
node scripts/test-pixapi-endpoints.js



# OU teste direto na API da Flow2Pay (gera QR code PIX)
./scripts/run-flow2pay-test.sh
```

Para mais detalhes, consulte a [documentação do túnel SSH](./_docs/ssh-tunnel.md).

### Executando a API

```bash
encore run
```

Para ambientes de produção, você pode usar nossa imagem Docker:

```bash
docker pull pluggou/pix-api:latest
docker run -d --name pix-api \
  -p 8080:4000 \
  -e DB_HOST=seu-host-db:5432 \
  -e DB_USER=seu-usuario-db \
  -e DB_PASSWORD=sua-senha-db \
  -e FLOW2PAY_CLIENT_ID=seu-cliente-id \
  -e FLOW2PAY_CLIENT_SECRET=seu-cliente-secret \
  pluggou/pix-api:latest
```

## Documentação da API

### Autenticação

Todos os endpoints da API requerem autenticação via chave API fornecida no cabeçalho `X-API-Key`.

### Endpoints Principais

#### Gerenciamento de Usuários

- `POST /createUser` - Criar um novo usuário com token Flow2Pay
- `GET /listUsers` - Listar todos os usuários

#### Operações PIX

- `POST /qrcode` - Gerar um QR code para recebimento PIX
- `GET /qrcode/:txid` - Obter detalhes do QR code por ID de transação
- `POST /transfer` - Transferir PIX para outra conta
- `GET /balance` - Consultar saldo da conta
- `GET /transactions` - Obter histórico de transações



#### Gerenciamento de Chaves API

- `POST /api-keys` - Criar uma nova chave API
- (Endpoints adicionais de gerenciamento de chaves API disponíveis)

#### Webhooks

- `POST /baas` - Receber webhooks diretamente do Flow2Pay
- `POST /svix-webhook` - Receber webhooks do Svix (recomendado)
- `POST /webhook-config` - Criar configuração de webhook para receber notificações
- `GET /webhook-configs` - Listar configurações de webhook
- `GET /webhook-configs/:id` - Obter detalhes da configuração de webhook
- `PUT /webhook-configs/:id` - Atualizar configuração de webhook
- `DELETE /webhook-configs/:id` - Excluir configuração de webhook
- `GET /webhook-configs/:id/logs` - Visualizar logs de entrega de webhook

### Eventos de Webhook

Sua aplicação pode receber notificações em tempo real para:

- `pix_in` - Quando um pagamento PIX é recebido
- `pix_out` - Quando uma transferência PIX é concluída
- `pix_in_reversal` - Quando um PIX recebido é estornado
- `pix_out_reversal` - Quando um PIX enviado é estornado

### Exemplo de Integração

```typescript
// Criar um usuário
const response = await fetch('https://api.pluggou.io/createUser', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    name: 'Usuário Exemplo',
    partner_id: 'flow2pay',
    partner_token: 'TOKEN_FLOW2PAY',
  }),
});

const { id, api_key } = await response.json();

// Gerar um QR code
const qrCodeResponse = await fetch('https://api.pluggou.io/qrcode', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': api_key,
  },
  body: JSON.stringify({
    amount: 1000, // R$ 10,00
    description: 'Pagamento pelo pedido #123',
  }),
});

const { txid, qr_code_text, qr_code_image } = await qrCodeResponse.json();
```

## Versão Simplificada para Self-Hosting

Para ambientes onde você precisa hospedar a API em seu próprio servidor, oferecemos uma versão simplificada que:

1. Utiliza Svix para gerenciamento de webhooks em vez de NSQ
2. Configura para uso com banco de dados PostgreSQL externo
3. Fornece endpoints simplificados para testar a integração com Flow2Pay

Consulte o [DOCKER_README.md](./DOCKER_README.md) para instruções detalhadas sobre como configurar e implantar a versão self-hosted.

## Segurança

- Todos os endpoints da API são autenticados usando chaves API
- Os endpoints de webhook utilizam assinaturas HMAC para verificação
- Dados sensíveis são armazenados de forma segura
- Tratamento abrangente de erros e logging

## Arquitetura

- **Banco de Dados**: PostgreSQL para persistência robusta de dados
- **Processamento de Eventos**: Svix para gerenciamento de webhooks e PubSub para tratamento assíncrono de eventos
- **Tarefas Agendadas**: Cron jobs para tarefas de manutenção
- **Webhooks**: Notificações em tempo real com Svix para validação, entrega confiável e mecanismo de retry
- **Túnel SSH**: Para desenvolvimento local, permitindo que requisições à API Flow2Pay sejam roteadas através do servidor EC2

## Suporte

Para suporte técnico, entre em contato conosco:

- Email: <EMAIL>
- WhatsApp: +55 (11) 99999-9999
- Horário de atendimento: Segunda a Sexta, 9h às 18h (exceto feriados)

## Licença

© 2023-2024 Pluggou. Todos os direitos reservados.

# PIX API Gateway

This is an Encore.ts application that provides a gateway for PIX payment operations.

## Local Development

To run the application locally:

```bash
encore run
```

The application will be available at http://localhost:4000

## Deployment to Coolify

This application is configured for deployment to Coolify. To deploy:

1. Push your changes to your Git repository
2. In Coolify, create a new service from your Git repository
3. Set the following configuration:
   - FQDN: apipix.cloud.pluggou.io
   - Port: 4000
   - Build Command: (leave empty, handled by Dockerfile)
   - Start Command: (leave empty, handled by Dockerfile)

### Important Configuration Notes

- The application uses the Encore.ts framework and exposes endpoints at the root path
- The Dockerfile builds the application and runs it on port 4000
- The Coolify configuration in `.coolify/config.yaml` configures Traefik to properly route requests

## API Endpoints

- `GET /health` - Health check endpoint
- `POST /user` - Create a new user
- `POST /qrcode` - Generate a PIX QR code
- `GET /qrcode/:txid` - Get QR code details
- `POST /transfer` - Transfer PIX to another account
- `GET /balance` - Get account balance
- `GET /transactions` - Get transaction history


# PIX API - Encore.ts Application

## Configuração do Ambiente

Este projeto utiliza Encore.ts para desenvolvimento e Docker para implantação.

## Arquivos Importantes

- `docker-compose.yaml`: Contém apenas os serviços NSQ para desenvolvimento local
- `docker-compose.prod.yaml`: Configuração para produção, usando a imagem pré-construída do Docker Hub e banco de dados PostgreSQL
- `build-and-push.sh`: Script para construir e enviar a imagem Docker para o Docker Hub

## Fluxo de Trabalho

### 1. Desenvolvimento Local

Para desenvolvimento local, utilize o comando Encore:

```bash
encore run
```

### 2. Build e Push da Imagem Docker

Para construir e enviar a imagem para o Docker Hub:

```bash
# Opcionalmente, defina variáveis de ambiente para personalizar o build
export FLOW2PAY_CLIENT_ID=seu_client_id
export FLOW2PAY_CLIENT_SECRET=seu_client_secret
export FLOW2PAY_EVENT_TOKEN=seu_token
export API_BASE_URL=https://sua-api.exemplo.com

# Execute o script de build
./build-and-push.sh
```

O script irá:
1. Verificar os pré-requisitos (Docker, Encore CLI)
2. Criar um arquivo `.secrets.local.cue` temporário para o build
3. Construir a imagem Docker usando o comando `encore build docker`
4. Enviar a imagem para o Docker Hub

### 3. Implantação em Produção

Para implantar em produção usando a imagem pré-construída:

```bash
# Usar o script de execução que configura tudo automaticamente
./run-app.sh

# Ou executar manualmente
docker-compose -f docker-compose.prod.yaml up -d
```

### Configuração do Banco de Dados

A aplicação está configurada para se conectar ao banco de dados PostgreSQL que é executado como um serviço Docker no mesmo ambiente:

- Host: postgres (nome do serviço no docker-compose)
- Porta: 5432
- Usuário: encore
- Senha: encore
- Banco: pix_db

O script `init-postgres.sh` é executado automaticamente na inicialização do banco de dados para configurar as tabelas necessárias.

## Variáveis de Ambiente

As seguintes variáveis de ambiente são necessárias para execução:

- `PORT`: Porta em que a aplicação escuta (padrão: 4000)
- `API_BASE_URL`: URL base pública da API
- `DB_ADDR`: Endereço do banco de dados (padrão: postgres)
- `DB_PORT`: Porta do banco de dados (padrão: 5432)
- `DB_USER`: Usuário do banco de dados (padrão: encore)
- `DB_PASSWORD`: Senha do banco de dados (padrão: encore)
- `DB_NAME`: Nome do banco de dados (padrão: pix_db)
- `FLOW2PAY_CLIENT_ID`: Client ID da Flow2Pay
- `FLOW2PAY_CLIENT_SECRET`: Client Secret da Flow2Pay
- `FLOW2PAY_EVENT_TOKEN`: Token de evento da Flow2Pay
- `SVIX_TOKEN`: Token de autenticação para a API Svix
- `SVIX_WEBHOOK_SECRET`: Segredo para verificação de assinaturas de webhooks Svix
- `NSQD_TCP_ADDR`: Endereço TCP do NSQd (opcional, apenas se ainda estiver usando NSQ)
