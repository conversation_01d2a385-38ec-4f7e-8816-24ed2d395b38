#!/bin/bash
set -e

# Cores para saída
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Iniciando a aplicação PIX API com a imagem pré-construída...${NC}"

# Verificar se o Docker está instalado
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker não está instalado. Por favor, instale o Docker primeiro.${NC}"
    exit 1
fi

# Verificar se o Docker está rodando
if ! docker info >/dev/null 2>&1; then
    echo -e "${RED}❌ Docker não está rodando. Por favor, inicie o serviço Docker.${NC}"
    exit 1
fi

# Verificar se o docker-compose está instalado
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ Docker Compose não está instalado. Por favor, instale o Docker Compose primeiro.${NC}"
    exit 1
fi

# Verificar se o arquivo docker-compose.prod.yaml existe
if [ ! -f "docker-compose.prod.yaml" ]; then
    echo -e "${RED}❌ Arquivo docker-compose.prod.yaml não encontrado.${NC}"
    exit 1
fi

# Puxar a imagem mais recente do Docker Hub
echo -e "${BLUE}📥 Baixando a imagem mais recente do Docker Hub...${NC}"
docker-compose -f docker-compose.prod.yaml pull

# Iniciar os serviços
echo -e "${BLUE}🚀 Iniciando os serviços...${NC}"
docker-compose -f docker-compose.prod.yaml up -d

# Aguardar o banco de dados ficar saudável
echo -e "${BLUE}⏳ Aguardando o banco de dados inicializar...${NC}"
sleep 5

# Verificar se o banco de dados está saudável
echo -e "${BLUE}🔍 Verificando a saúde do banco de dados...${NC}"
DB_HEALTH=$(docker-compose -f docker-compose.prod.yaml ps -q postgres | xargs docker inspect -f '{{.State.Health.Status}}')
if [ "$DB_HEALTH" = "healthy" ]; then
    echo -e "${GREEN}✅ Banco de dados está saudável!${NC}"
else
    echo -e "${YELLOW}⚠️ Aguardando o banco de dados ficar saudável...${NC}"
    # Esperar até que o banco de dados esteja saudável (máximo 30 segundos)
    for i in {1..6}; do
        sleep 5
        DB_HEALTH=$(docker-compose -f docker-compose.prod.yaml ps -q postgres | xargs docker inspect -f '{{.State.Health.Status}}')
        if [ "$DB_HEALTH" = "healthy" ]; then
            echo -e "${GREEN}✅ Banco de dados está saudável!${NC}"
            break
        fi
        if [ $i -eq 6 ]; then
            echo -e "${YELLOW}⚠️ Banco de dados ainda não está saudável, mas continuando mesmo assim...${NC}"
        fi
    done
fi

# Verificar se os serviços foram iniciados corretamente
echo -e "${BLUE}🔍 Verificando o status dos serviços...${NC}"
docker-compose -f docker-compose.prod.yaml ps

echo -e "${GREEN}✅ Aplicação iniciada com sucesso!${NC}"
echo -e "${YELLOW}Para visualizar os logs da aplicação, execute:${NC}"
echo -e "docker-compose -f docker-compose.prod.yaml logs -f pixapi"
echo -e "${YELLOW}Para parar a aplicação, execute:${NC}"
echo -e "docker-compose -f docker-compose.prod.yaml down"
