{"$schema": "https://encore.dev/schemas/infra.schema.json", "metadata": {"app_id": "pixapi", "env_name": "production", "env_type": "production", "cloud": "custom", "base_url": "https://apipix.cloud.pluggou.io"}, "sql_servers": [{"host": "ep-muddy-darkness-acfy40ru-pooler.sa-east-1.aws.neon.tech:5432", "tls_config": {"disabled": false}, "databases": {"pixdb": {"username": "pixdb_owner", "password": {"$env": "NEON_DB_PASSWORD"}, "max_connections": 20, "min_connections": 5}}}], "secrets": {"FLOW2PAY_CLIENT_ID": {"$env": "FLOW2PAY_CLIENT_ID"}, "FLOW2PAY_CLIENT_SECRET": {"$env": "FLOW2PAY_CLIENT_SECRET"}, "FLOW2PAY_EVENT_TOKEN": {"$env": "FLOW2PAY_EVENT_TOKEN"}, "ADMIN_API_KEY": {"$env": "ADMIN_API_KEY"}, "POSTGRES_PASSWORD": {"$env": "POSTGRES_PASSWORD"}, "NEON_DB_PASSWORD": {"$env": "NEON_DB_PASSWORD"}, "NEON_DB_CONNECTION_STRING": {"$env": "NEON_DB_CONNECTION_STRING"}, "SVIX_TOKEN": {"$env": "SVIX_TOKEN"}, "SVIX_WEBHOOK_SECRET": {"$env": "SVIX_WEBHOOK_SECRET"}}}