// In pixapi/svix-webhook.ts
// Get Svix signature headers
const svixId = req.headers['svix-id'] as string;
const svixTimestamp = req.headers['svix-timestamp'] as string;
const svixSignature = req.headers['svix-signature'] as string;

// Verify the Svix signature
const isValid = verifySvixSignature(
  body,
  svixSignature,
  svixTimestamp,
  SVIX_WEBHOOK_SECRET(),
  svixId // Pass the svix-id from the request
);

// In pixapi/svix.ts
export function verifySvixSignature(
  payload: string,
  signature: string,
  timestamp: string,
  secret: string,
  messageId: string // Add messageId parameter
): boolean {
  try {
    // Use the imported Webhook class
    const wh = new Webhook(secret);

    // Create headers object with all required fields
    const headers = {
      'svix-id': messageId, // Use the provided messageId
      'svix-timestamp': timestamp,
      'svix-signature': signature,
    };

    // Verify the signature
    wh.verify(payload, headers);

    // If verification doesn't throw an error, it's valid
    return true;
  } catch (error) {
    const logger = log.with({ action: 'verify_svix_signature' });
    logger.error('Failed to verify Svix signature', {
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    return false;
  }
}
