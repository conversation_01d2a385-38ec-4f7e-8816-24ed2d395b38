#!/bin/bash
set -e

echo "Starting database initialization..."

# Script de inicialização do PostgreSQL para a aplicação PIX API
# Este script roda como parte da inicialização do container do PostgreSQL

# Connect to the database as the postgres user
psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    -- Create pixapi schema
    CREATE SCHEMA IF NOT EXISTS pixapi;

    -- Set search_path to include pixapi schema
    ALTER DATABASE "$POSTGRES_DB" SET search_path TO pixapi, public;

    -- Create users table
    CREATE TABLE IF NOT EXISTS pixapi.users (
        id BIGSERIAL PRIMARY KEY,
        email TEXT NOT NULL UNIQUE,
        name TEXT NOT NULL,
        partner_id TEXT NOT NULL,
        partner_token TEXT NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
    );

    -- Create QR codes table
    CREATE TABLE IF NOT EXISTS pixapi.qr_codes (
        id BIGSERIAL PRIMARY KEY,
        user_id BIGINT NOT NULL REFERENCES pixapi.users(id),
        txid TEXT NOT NULL UNIQUE,
        amount BIGINT NOT NULL,
        description TEXT,
        expiration_time INTEGER NOT NULL,
        qr_code_image TEXT,
        qr_code_text TEXT NOT NULL,
        status TEXT NOT NULL,
        partner_id TEXT NOT NULL,
        external_id TEXT,
        created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
    );

    -- Create transactions table
    CREATE TABLE IF NOT EXISTS pixapi.transactions (
        id BIGSERIAL PRIMARY KEY,
        user_id BIGINT NOT NULL REFERENCES pixapi.users(id),
        qr_code_id BIGINT REFERENCES pixapi.qr_codes(id),
        type TEXT NOT NULL,
        amount BIGINT NOT NULL,
        currency TEXT NOT NULL DEFAULT 'BRL',
        status TEXT NOT NULL,
        description TEXT,
        flow2pay_txid TEXT,
        flow2pay_end_to_end_id TEXT,
        flow2pay_id_envio TEXT,
        webhook_payload JSONB,
        created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
    );

    -- Create API keys table
    CREATE TABLE IF NOT EXISTS pixapi.api_keys (
        id BIGSERIAL PRIMARY KEY,
        user_id BIGINT NOT NULL REFERENCES pixapi.users(id),
        api_key TEXT NOT NULL UNIQUE,
        name TEXT NOT NULL,
        permissions JSONB NOT NULL,
        is_active BOOLEAN NOT NULL DEFAULT TRUE,
        last_used_at TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
        expires_at TIMESTAMP WITH TIME ZONE
    );

    -- Create webhook_configs table
    CREATE TABLE IF NOT EXISTS pixapi.webhook_configs (
        id BIGSERIAL PRIMARY KEY,
        user_id BIGINT NOT NULL REFERENCES pixapi.users(id),
        url TEXT NOT NULL,
        secret_key TEXT NOT NULL,
        events JSONB NOT NULL,
        description TEXT,
        is_active BOOLEAN NOT NULL DEFAULT TRUE,
        created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
        last_called_at TIMESTAMP WITH TIME ZONE
    );

    -- Create webhook_delivery_logs table
    CREATE TABLE IF NOT EXISTS pixapi.webhook_delivery_logs (
        id BIGSERIAL PRIMARY KEY,
        webhook_config_id BIGINT NOT NULL REFERENCES pixapi.webhook_configs(id),
        event_type TEXT NOT NULL,
        payload JSONB NOT NULL,
        status TEXT NOT NULL,
        status_code INTEGER,
        response_body TEXT,
        attempt_count INTEGER NOT NULL DEFAULT 1,
        next_retry_at TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
    );

    -- Create indexes
    CREATE INDEX IF NOT EXISTS idx_api_keys_api_key ON pixapi.api_keys(api_key);
    CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON pixapi.transactions(user_id);
    CREATE INDEX IF NOT EXISTS idx_qr_codes_user_id ON pixapi.qr_codes(user_id);
    CREATE INDEX IF NOT EXISTS idx_qr_codes_txid ON pixapi.qr_codes(txid);
    CREATE INDEX IF NOT EXISTS idx_transactions_flow2pay_txid ON pixapi.transactions(flow2pay_txid);
    CREATE INDEX IF NOT EXISTS idx_transactions_flow2pay_end_to_end_id ON pixapi.transactions(flow2pay_end_to_end_id);
    CREATE INDEX IF NOT EXISTS idx_transactions_flow2pay_id_envio ON pixapi.transactions(flow2pay_id_envio);
    CREATE INDEX IF NOT EXISTS idx_webhook_configs_user_id ON pixapi.webhook_configs(user_id);
    CREATE INDEX IF NOT EXISTS idx_webhook_configs_is_active ON pixapi.webhook_configs(is_active);
    CREATE INDEX IF NOT EXISTS idx_webhook_delivery_logs_webhook_config_id ON pixapi.webhook_delivery_logs(webhook_config_id);
    CREATE INDEX IF NOT EXISTS idx_webhook_delivery_logs_status ON pixapi.webhook_delivery_logs(status);
    CREATE INDEX IF NOT EXISTS idx_webhook_delivery_logs_next_retry_at ON pixapi.webhook_delivery_logs(next_retry_at) WHERE next_retry_at IS NOT NULL;

    -- Grant permissions
    GRANT ALL PRIVILEGES ON SCHEMA pixapi TO "$POSTGRES_USER";
    GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO "$POSTGRES_USER";
    GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA pixapi TO "$POSTGRES_USER";
    GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO "$POSTGRES_USER";
    GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA pixapi TO "$POSTGRES_USER";
EOSQL

# Make the script executable
chmod +x /docker-entrypoint-initdb.d/init-postgres.sh

echo "Database initialization completed successfully!"
