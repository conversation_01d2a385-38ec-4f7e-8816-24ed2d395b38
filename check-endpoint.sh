#!/bin/bash
set -e

# Cores para saída
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 Verificando o endpoint atual no container...${NC}"

# Verificar se o container está rodando
if ! docker ps | grep -q pixapi-app; then
  echo -e "${RED}❌ Container pixapi-app não está rodando${NC}"
  exit 1
fi

# Extrair o código do container para verificar o endpoint
echo -e "${BLUE}📋 Extraindo código do container para verificação...${NC}"
TEMP_DIR=$(mktemp -d)
docker cp pixapi-app:/app/pixapi/pix-api.ts "$TEMP_DIR/pix-api.ts"

# Verificar qual endpoint está sendo usado
echo -e "${BLUE}🔍 Verificando qual endpoint está sendo usado no código...${NC}"
ENDPOINT=$(grep -A 5 "Call Flow2Pay API to initiate transfer" "$TEMP_DIR/pix-api.ts" | grep -o "'/[^']*'" | head -1)

echo -e "${YELLOW}Endpoint atual no container: ${ENDPOINT}${NC}"

# Verificar a tag da imagem
echo -e "${BLUE}🔍 Verificando a tag da imagem em uso...${NC}"
IMAGE_TAG=$(docker inspect pixapi-app --format='{{.Config.Image}}')

echo -e "${YELLOW}Imagem atual: ${IMAGE_TAG}${NC}"

# Limpar arquivos temporários
rm -rf "$TEMP_DIR"

echo -e "${GREEN}✅ Verificação concluída${NC}"
echo -e "${BLUE}Para atualizar o container com a nova versão, execute:${NC}"
echo -e "${YELLOW}./force-update.sh${NC}"
