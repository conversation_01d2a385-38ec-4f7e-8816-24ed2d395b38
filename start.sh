#!/bin/bash
set -e

echo "Starting PIX API initialization..."

# Parse DB_ADDR to get host and port
DB_HOST=$DB_ADDR
# Use DB_PORT if defined, otherwise use default
DB_PORT=${DB_PORT:-5432}

# Wait for PostgreSQL to be ready
echo "Waiting for PostgreSQL to be ready at $DB_HOST:$DB_PORT..."
until PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c '\q'; do
  echo "PostgreSQL is unavailable - sleeping"
  sleep 1
done

echo "PostgreSQL is up - initializing database..."

# Initialize the database schema and tables
if [ -f "/app/scripts/initialize-database.sh" ]; then
    echo "Using initialize-database.sh script to set up the database..."
    chmod +x /app/scripts/initialize-database.sh
    /app/scripts/initialize-database.sh
else
    echo "initialize-database.sh script not found, using basic initialization..."
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME <<-EOSQL
        -- Create pixapi schema
        CREATE SCHEMA IF NOT EXISTS pixapi;

        -- Set search_path to include pixapi schema
        ALTER DATABASE "$DB_NAME" SET search_path TO pixapi, public;

        -- Create schema_migrations table if it doesn't exist
        CREATE TABLE IF NOT EXISTS schema_migrations (
            version bigint NOT NULL,
            dirty boolean NOT NULL,
            PRIMARY KEY (version)
        );

        -- Initialize schema_migrations if empty
        DO \$\$
        BEGIN
            IF NOT EXISTS (SELECT 1 FROM schema_migrations) THEN
                INSERT INTO schema_migrations (version, dirty) VALUES (0, false);
            END IF;
        END \$\$;

        -- Create flow2pay_webhooks table
        CREATE TABLE IF NOT EXISTS flow2pay_webhooks (
            id BIGSERIAL PRIMARY KEY,
            event_type TEXT NOT NULL,
            txid TEXT,
            id_envio TEXT,
            end_to_end_id TEXT,
            valor BIGINT NOT NULL,
            status TEXT NOT NULL,
            payload JSONB NOT NULL,
            processed BOOLEAN NOT NULL DEFAULT FALSE,
            processed_at TIMESTAMP WITH TIME ZONE,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
        );

        -- Create indexes for flow2pay_webhooks
        CREATE INDEX IF NOT EXISTS idx_flow2pay_webhooks_txid ON flow2pay_webhooks(txid) WHERE txid IS NOT NULL;
        CREATE INDEX IF NOT EXISTS idx_flow2pay_webhooks_id_envio ON flow2pay_webhooks(id_envio) WHERE id_envio IS NOT NULL;
        CREATE INDEX IF NOT EXISTS idx_flow2pay_webhooks_end_to_end_id ON flow2pay_webhooks(end_to_end_id) WHERE end_to_end_id IS NOT NULL;
        CREATE INDEX IF NOT EXISTS idx_flow2pay_webhooks_processed ON flow2pay_webhooks(processed) WHERE processed = FALSE;
    EOSQL
fi

echo "Database schema and tables initialized successfully!"

# Start the application using Node.js directly
echo "Starting application using Node.js directly..."
cd /app && node node_modules/.bin/encore run --prod || node server.js
