#!/usr/bin/env node

/**
 * API Connection Test
 * 
 * This script tests the connectivity to the PIX API and verifies that the basic
 * endpoints are functioning correctly. It checks the health endpoint and attempts
 * to generate a simple QR code to validate the full request-response cycle.
 * 
 * @version 1.0.0
 * <AUTHOR> Team
 */

// Configuration - Override with environment variables if needed
const CONFIG = {
  // API endpoints
  apiUrl: process.env.API_URL || 'https://apipix.cloud.pluggou.io',
  apiKey: process.env.API_KEY || '19af7b34417f24a7f29d333dfc3dcb473d60e18ff91a712ffc07475a5631ea99',
  
  // Test parameters
  verbose: process.env.VERBOSE === 'true'
};

/**
 * Tests the API health endpoint
 * @returns {Promise<Object>} Test result
 */
async function testApiHealth() {
  console.log('\n🧪 TEST 1: API Health Check');
  console.log('========================');
  console.log(`API URL: ${CONFIG.apiUrl}`);
  
  try {
    console.log('\n🔄 Testing connection...');
    
    const response = await fetch(`${CONFIG.apiUrl}/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': CONFIG.apiKey
      }
    });
    
    const statusCode = response.status;
    let responseData;
    
    try {
      responseData = await response.json();
    } catch (e) {
      responseData = await response.text();
    }
    
    console.log(`\n📥 Response received (Status: ${statusCode})`);
    
    if (response.ok) {
      console.log('✅ SUCCESS! API is healthy and responding.');
      
      if (CONFIG.verbose) {
        console.log('\nResponse details:');
        console.log(typeof responseData === 'object' ? JSON.stringify(responseData, null, 2) : responseData);
      }
      
      return { success: true, statusCode, response: responseData };
    } else {
      console.log('❌ FAILED! API health check returned an error.');
      console.log('\nError details:');
      console.log(typeof responseData === 'object' ? JSON.stringify(responseData, null, 2) : responseData);
      
      return { success: false, statusCode, response: responseData };
    }
  } catch (error) {
    console.error(`\n❌ ERROR: ${error.message}`);
    
    // Check for common connection errors
    if (error.code === 'ENOTFOUND') {
      console.log('\nDNS Error: The domain could not be resolved.');
      console.log('Check if the server is running and the URL is correct.');
    }
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\nConnection Refused Error: The server is not accepting connections.');
      console.log('Check if the server is running on the correct port.');
    }
    
    return { success: false, error: error.message };
  }
}

/**
 * Tests the QR code generation endpoint
 * @returns {Promise<Object>} Test result
 */
async function testQrCodeGeneration() {
  console.log('\n🧪 TEST 2: QR Code Generation');
  console.log('==========================');
  
  try {
    console.log('\n🔄 Sending request to /qrcode...');
    
    const payload = {
      amount: 1.00,
      description: 'API Connection Test',
      expiration_time: 3600
    };
    
    if (CONFIG.verbose) {
      console.log('\nRequest payload:');
      console.log(JSON.stringify(payload, null, 2));
    }
    
    const response = await fetch(`${CONFIG.apiUrl}/qrcode`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': CONFIG.apiKey
      },
      body: JSON.stringify(payload)
    });
    
    const statusCode = response.status;
    let responseData;
    
    try {
      responseData = await response.json();
    } catch (e) {
      responseData = await response.text();
    }
    
    console.log(`\n📥 Response received (Status: ${statusCode})`);
    
    if (response.ok) {
      console.log('✅ SUCCESS! QR code generated successfully.');
      
      // Display transaction ID
      if (responseData.txid) {
        console.log(`\n📋 Transaction ID: ${responseData.txid}`);
      }
      
      // Display QR code text if available
      if (responseData.qr_code_text) {
        console.log('\n📱 PIX code:');
        console.log('----------------------------------');
        console.log(responseData.qr_code_text);
        console.log('----------------------------------');
      }
      
      if (CONFIG.verbose) {
        console.log('\nFull response:');
        console.log(JSON.stringify(responseData, null, 2));
      }
      
      return { success: true, statusCode, response: responseData };
    } else {
      console.log('❌ FAILED! QR code generation failed.');
      console.log('\nError details:');
      console.log(typeof responseData === 'object' ? JSON.stringify(responseData, null, 2) : responseData);
      
      return { success: false, statusCode, response: responseData };
    }
  } catch (error) {
    console.error(`\n❌ ERROR: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * Tests the API version endpoint
 * @returns {Promise<Object>} Test result
 */
async function testApiVersion() {
  console.log('\n🧪 TEST 3: API Version Check');
  console.log('=========================');
  
  try {
    console.log('\n🔄 Testing connection to /version...');
    
    const response = await fetch(`${CONFIG.apiUrl}/version`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': CONFIG.apiKey
      }
    });
    
    const statusCode = response.status;
    let responseData;
    
    try {
      responseData = await response.json();
    } catch (e) {
      responseData = await response.text();
    }
    
    console.log(`\n📥 Response received (Status: ${statusCode})`);
    
    if (response.ok) {
      console.log('✅ SUCCESS! Version endpoint is responding.');
      
      if (typeof responseData === 'object' && responseData.version) {
        console.log(`\n📋 API Version: ${responseData.version}`);
      }
      
      if (CONFIG.verbose) {
        console.log('\nResponse details:');
        console.log(typeof responseData === 'object' ? JSON.stringify(responseData, null, 2) : responseData);
      }
      
      return { success: true, statusCode, response: responseData };
    } else {
      console.log('❌ FAILED! Version endpoint returned an error.');
      console.log('\nError details:');
      console.log(typeof responseData === 'object' ? JSON.stringify(responseData, null, 2) : responseData);
      
      return { success: false, statusCode, response: responseData };
    }
  } catch (error) {
    console.error(`\n❌ ERROR: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * Main function to run all tests
 */
async function main() {
  console.log('🔍 API Connection Test Suite');
  console.log('==========================');
  console.log(`API URL: ${CONFIG.apiUrl}`);
  console.log(`Verbose mode: ${CONFIG.verbose ? 'Enabled' : 'Disabled'}`);
  
  const results = [];
  
  // Test 1: API Health
  const healthResult = await testApiHealth();
  results.push({ name: 'API Health Check', ...healthResult });
  
  // If health check passes, run additional tests
  if (healthResult.success) {
    // Test 2: QR Code Generation
    const qrCodeResult = await testQrCodeGeneration();
    results.push({ name: 'QR Code Generation', ...qrCodeResult });
    
    // Test 3: API Version
    const versionResult = await testApiVersion();
    results.push({ name: 'API Version Check', ...versionResult });
  } else {
    console.log('\n⚠️ Health check failed. Skipping additional tests.');
  }
  
  // Display summary
  console.log('\n📊 Test Results Summary:');
  console.log('=======================');
  
  let successCount = 0;
  
  results.forEach((result) => {
    if (result.success) {
      successCount++;
      console.log(`✅ ${result.name}: SUCCESS`);
    } else {
      console.log(`❌ ${result.name}: FAILED - ${result.error || 'See details above'}`);
    }
  });
  
  console.log(`\nTotal: ${results.length} tests, ${successCount} passed, ${results.length - successCount} failed`);
  
  // Provide troubleshooting suggestions if any tests failed
  if (successCount < results.length) {
    console.log('\n🔧 Troubleshooting Suggestions:');
    
    if (!healthResult.success) {
      console.log('1. Verify the API URL is correct');
      console.log('2. Check if the API server is running');
      console.log('3. Ensure network connectivity to the API server');
      console.log('4. Check firewall settings that might block the connection');
    } else if (results.some(r => r.name === 'QR Code Generation' && !r.success)) {
      console.log('1. Verify your API key has the necessary permissions');
      console.log('2. Check if the QR code generation service is properly configured');
      console.log('3. Ensure the Flow2Pay integration is working correctly');
    }
    
    console.log('\nFor more detailed diagnostics, run the test with verbose mode:');
    console.log('VERBOSE=true node api-connection-test.js');
  }
}

// Run the main function
main().catch(error => {
  console.error('\n❌ Unhandled error:', error);
  process.exit(1);
});
