#!/usr/bin/env node

/**
 * PIX API Transfer Script
 *
 * Este script realiza transferências PIX usando a API.
 * Pode ser usado com argumentos de linha de comando para automação.
 *
 * Uso:
 *   node pix-transfer.js --key <chave_pix> --amount <valor> [--desc <descrição>]
 *
 * Exemplo:
 *   node pix-transfer.js --key <EMAIL> --amount 10.50 --desc "Pagamento de teste"
 */

const fs = require('fs');
const path = require('path');

// Configuração - Substitua com variáveis de ambiente se necessário
const CONFIG = {
	// Configurações da API
	apiUrl: 'https://apipix.cloud.pluggou.io',
	apiKey: 'pix_516a85828ff755622bde7d182a7b43f3d9362cb2bfbdbdfc',

	// Parâmetros de teste
	saveResponses: true,
	outputDir: './transfer-results',
	verbose: true,
};

// Processar argumentos da linha de comando
function processarArgumentos() {
	const args = process.argv.slice(2);
	const params = {};

	for (let i = 0; i < args.length; i++) {
		if (args[i] === '--key' && i + 1 < args.length) {
			params.pixKey = args[i + 1];
			i++;
		} else if (args[i] === '--amount' && i + 1 < args.length) {
			params.amount = parseFloat(args[i + 1]);
			i++;
		} else if (args[i] === '--desc' && i + 1 < args.length) {
			params.description = args[i + 1];
			i++;
		} else if (args[i] === '--help' || args[i] === '-h') {
			exibirAjuda();
			process.exit(0);
		}
	}

	return params;
}

// Exibir ajuda
function exibirAjuda() {
	console.log(`
PIX API Transfer Script

Uso:
  node pix-transfer.js --key <chave_pix> --amount <valor> [--desc <descrição>]

Argumentos:
  --key <chave_pix>      Chave PIX de destino (obrigatório)
  --amount <valor>       Valor a transferir em reais (obrigatório)
  --desc <descrição>     Descrição da transferência (opcional)
  --help, -h             Exibe esta ajuda

Exemplo:
  node pix-transfer.js --key <EMAIL> --amount 10.50 --desc "Pagamento de teste"
  `);
}

// Diretório de saída
if (CONFIG.saveResponses) {
	if (!fs.existsSync(CONFIG.outputDir)) {
		fs.mkdirSync(CONFIG.outputDir, { recursive: true });
	}
}

// Formatar data para nomes de arquivos
function formatDate(date) {
	return date.toISOString().replace(/[:.]/g, '-');
}

// Salvar resposta em arquivo
function saveResponse(response, prefix) {
	const timestamp = formatDate(new Date());
	const filename = `${CONFIG.outputDir}/${prefix}_${timestamp}.json`;
	fs.writeFileSync(filename, JSON.stringify(response, null, 2));
	console.log(`Resposta salva em ${filename}`);
}

// Função principal
async function realizarTransferenciaPIX() {
	// Obter parâmetros da linha de comando
	const params = processarArgumentos();

	// Verificar parâmetros obrigatórios
	if (!params.pixKey || !params.amount) {
		console.error('❌ Erro: Chave PIX e valor são obrigatórios');
		exibirAjuda();
		process.exit(1);
	}

	// Validar valor
	if (isNaN(params.amount) || params.amount <= 0) {
		console.error('❌ Erro: Valor inválido. Deve ser um número positivo');
		process.exit(1);
	}

	console.log('🚀 Iniciando Transferência PIX');
	console.log('============================');
	console.log('📋 Detalhes da transferência:');
	console.log(`  - Chave PIX: ${params.pixKey}`);
	console.log(`  - Valor: R$ ${params.amount.toFixed(2)}`);
	console.log(`  - Descrição: ${params.description || 'Transferência PIX'}`);

	try {
		// Preparar payload
		const payload = {
			pix_key: params.pixKey,
			amount: params.amount,
			description: params.description || 'Transferência PIX',
			ip_address: '127.0.0.1',
			user_agent: 'PIX Transfer Script',
		};

		console.log('\n🔄 Enviando solicitação de transferência...');

		// Chamar o endpoint de transferência
		const startTime = Date.now();
		const response = await fetch(`${CONFIG.apiUrl}/transfer`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'X-API-Key': CONFIG.apiKey,
			},
			body: JSON.stringify(payload),
		});

		const responseTime = Date.now() - startTime;
		const responseData = await response.json();

		// Registrar os resultados
		console.log(`\n⏱️  Tempo de resposta: ${responseTime}ms`);
		console.log(`📊 Status: ${response.status} ${response.statusText}`);

		if (response.ok) {
			console.log('✅ Transferência PIX iniciada com sucesso!');
			console.log('📋 Dados da resposta:');
			console.log(`  - ID de Envio: ${responseData.id_envio}`);
			console.log(`  - Status: ${responseData.status}`);
			console.log(`  - Mensagem: ${responseData.message}`);
			if (responseData.transaction_id) {
				console.log(`  - ID da Transação: ${responseData.transaction_id}`);
			}

			// Salvar a resposta bem-sucedida
			if (CONFIG.saveResponses) {
				saveResponse(responseData, 'transfer_success');
			}

			console.log(
				'\n⚠️  Importante: A transferência foi iniciada, mas pode levar alguns'
			);
			console.log(
				'   instantes para ser processada. Verifique o status posteriormente.'
			);
		} else {
			console.log('❌ Falha na transferência PIX');
			console.log('📋 Detalhes do erro:');
			console.log(`  - Código: ${responseData.code}`);
			console.log(`  - Mensagem: ${responseData.message}`);

			// Salvar a resposta de erro
			if (CONFIG.saveResponses) {
				saveResponse(responseData, 'transfer_error');
			}
		}
	} catch (error) {
		console.error('❌ Exceção ocorrida:', error.message);
		process.exit(1);
	}
}

// Executar a função principal
realizarTransferenciaPIX();
