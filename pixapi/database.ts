import { SQLDatabase } from 'encore.dev/storage/sqldb';
import { drizzle } from 'drizzle-orm/node-postgres';
import { migrate } from 'drizzle-orm/node-postgres/migrator';
import * as schema from './db/schema';
import log from 'encore.dev/log';
import { Pool, types } from 'pg';

// Create SQLDatabase instance with migrations configuration
export const sqlDb = new SQLDatabase('pixdb', {
	migrations: {
		path: './migrations',
		source: 'drizzle',
	},
});

// Parse numeric types as floats instead of strings
// This helps with handling money values correctly
types.setTypeParser(1700, function (val) {
	return parseFloat(val);
});

// Extract connection string and add SSL parameters if needed
const connectionString = sqlDb.connectionString;
const isCloudDatabase =
	connectionString.includes('neon.tech') ||
	connectionString.includes('amazonaws.com');

// Configure connection pool with optimized settings for cloud databases
const pgPool = new Pool({
	connectionString: connectionString,
	max: 10, // Reduced from 20 to avoid overwhelming the connection pool
	min: 2, // Reduced from 5 to minimize idle connections
	connectionTimeoutMillis: 15000, // Increased to 15 seconds for cloud databases
	idleTimeoutMillis: 60000, // Increased to 60 seconds
	allowExitOnIdle: false,
	// Add SSL configuration for cloud databases
	ssl: isCloudDatabase
		? {
				rejectUnauthorized: false, // For Neon and some AWS RDS instances
		  }
		: undefined,
	// Add statement timeout to prevent long-running queries
	statement_timeout: 30000, // 30 seconds
	// Add query timeout to prevent hanging queries
	query_timeout: 30000, // 30 seconds
});

// Define PostgreSQL error type
interface PgError extends Error {
	code?: string;
	severity?: string;
	detail?: string;
	hint?: string;
	position?: string;
	internalPosition?: string;
	internalQuery?: string;
	where?: string;
	schema?: string;
	table?: string;
	column?: string;
	dataType?: string;
	constraint?: string;
}

// Add error handler for the pool with reconnection logic
pgPool.on('error', (err: Error) => {
	// Cast to PgError to access PostgreSQL specific error properties
	const pgError = err as PgError;

	const logger = log.with({
		action: 'database_pool_error',
		error_message: pgError.message,
		error_code: pgError.code || 'unknown',
	});

	logger.error('Unexpected error on database client', {
		error: pgError.message,
		code: pgError.code || 'unknown',
		severity: pgError.severity,
		detail: pgError.detail,
	});

	// Log specific error types for better diagnostics
	if (
		pgError.code === 'ECONNRESET' ||
		pgError.code === 'EPIPE' ||
		pgError.message.includes('terminated unexpectedly')
	) {
		logger.warn(
			'Connection reset or terminated unexpectedly. This may be due to network issues or database server restart.'
		);
	} else if (pgError.code === 'ETIMEDOUT') {
		logger.warn(
			'Connection timed out. This may be due to network latency or database server overload.'
		);
	}

	// Don't exit the process, just log the error
});

// Initialize Drizzle ORM with the connection pool and improved error handling
export const db = drizzle(pgPool, { schema });

// Function to test database connectivity with retry logic
export async function testDatabaseConnection(
	retries = 3,
	delay = 1000
): Promise<boolean> {
	const logger = log.with({ action: 'test_database_connection' });
	let client;
	let lastError: Error | null = null;
	let attempt = 0;

	// Extract host for logging
	let host: string;
	try {
		host = new URL(sqlDb.connectionString).host;
	} catch (e) {
		host = 'unknown';
	}

	while (attempt <= retries) {
		try {
			logger.info('Testing database connection...', {
				attempt: attempt + 1,
				max_attempts: retries + 1,
				host,
			});

			// Set a timeout for the connection attempt
			const connectPromise = pgPool.connect();
			const timeoutPromise = new Promise<never>((_, reject) => {
				setTimeout(() => {
					reject(new Error('Database connection timed out after 10 seconds'));
				}, 10000);
			});

			// Race the connection attempt against the timeout
			client = await Promise.race([connectPromise, timeoutPromise]);

			// Test the connection with a simple query
			const result = await client.query('SELECT 1 as connection_test');

			logger.info('Database connection successful', {
				result: result.rows[0],
				host,
				attempt: attempt + 1,
			});

			return true;
		} catch (error) {
			lastError = error instanceof Error ? error : new Error(String(error));

			// Cast to PgError to access PostgreSQL specific error properties
			const pgError = lastError as PgError;

			// Log detailed error information
			logger.error('Database connection failed', {
				error: lastError.message,
				code: pgError.code || 'unknown',
				host,
				attempt: attempt + 1,
				max_attempts: retries + 1,
			});

			// Check if this is a connection error that might be temporary
			const isTemporaryError =
				pgError.code === 'ECONNREFUSED' ||
				pgError.code === 'ETIMEDOUT' ||
				pgError.code === 'ECONNRESET' ||
				pgError.message.includes('terminated unexpectedly') ||
				pgError.message.includes('connection timeout');

			// If this is the last attempt or not a temporary error, return false
			if (attempt >= retries || !isTemporaryError) {
				return false;
			}

			// Wait before retrying
			logger.info(`Retrying database connection in ${delay}ms...`, {
				attempt: attempt + 1,
				max_attempts: retries + 1,
			});

			await new Promise((resolve) => setTimeout(resolve, delay));

			// Exponential backoff for next retry
			delay = Math.min(delay * 2, 5000);
			attempt++;
		} finally {
			if (client) {
				try {
					client.release();
				} catch (releaseError) {
					logger.warn('Error releasing database client', {
						error:
							releaseError instanceof Error
								? releaseError.message
								: 'Unknown error',
					});
				}
			}
		}
	}

	// If we get here, all retries failed
	logger.error('All database connection attempts failed', {
		max_attempts: retries + 1,
		last_error: lastError?.message || 'Unknown error',
		host,
	});

	return false;
}

// Function to run migrations programmatically if needed
export async function runMigrations(maxRetries = 3) {
	const logger = log.with({ action: 'run_migrations' });

	try {
		// Test connection first with retries
		const isConnected = await testDatabaseConnection(maxRetries, 2000);
		if (!isConnected) {
			logger.error(
				'Cannot run migrations: database connection failed after multiple attempts'
			);
			throw new Error('Database connection failed after multiple attempts');
		}

		logger.info('Running database migrations...');

		// Set a timeout for the migration operation
		const migrationPromise = migrate(db, { migrationsFolder: './migrations' });
		const timeoutPromise = new Promise<never>((_, reject) => {
			setTimeout(() => {
				reject(new Error('Database migration timed out after 5 minutes'));
			}, 5 * 60 * 1000); // 5 minutes
		});

		// Race the migration against the timeout
		await Promise.race([migrationPromise, timeoutPromise]);

		logger.info('Database migrations completed successfully');
	} catch (error) {
		const err = error instanceof Error ? error : new Error(String(error));

		logger.error('Error running database migrations', {
			error: err.message,
			stack: err.stack,
		});

		// Check if this is a connection error
		if (
			err.message.includes('connection') ||
			err.message.includes('terminated') ||
			err.message.includes('timeout')
		) {
			logger.error(
				'Migration failed due to database connection issues. Please check your database configuration and connectivity.'
			);
		}

		throw err;
	}
}

// Export all schema objects for easy access
export * from './db/schema';
