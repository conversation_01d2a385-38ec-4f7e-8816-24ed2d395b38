import { api, Header } from 'encore.dev/api';
import { Flow2PayWebhookPayload } from './webhooks';
import { validateAPIKey, API_PERMISSIONS } from './auth';
import log from 'encore.dev/log';
import { db } from './db';
import { flow2payWebhooks } from './db/schema';
import { eq } from 'drizzle-orm';
import { forwardToPluggou } from './webhook-forwarder';
import { processWebhook } from './webhooks';

/**
 * Send a test webhook through the system
 */
export const sendTestWebhook = api(
  { method: 'POST', path: '/test-webhook', expose: true },
  async (params: {
    api_key: Header<'X-API-Key'>;
    event_type: 'PixIn' | 'PixOut' | 'PixInReversal' | 'PixOutReversalExternal';
    txid?: string;
    id_envio?: string;
    end_to_end_id?: string;
    user_id?: string;
    amount?: number;
    status?: string;
  }): Promise<{ success: boolean; message: string; details: any }> => {
    const logger = log.with({ action: 'send_test_webhook' });

    try {
      // Validate API key
      await validateAPIKey(
        params.api_key,
        API_PERMISSIONS.ALL,
        '/test-webhook'
      );

      // Generate a unique ID for tracking
      const eventId = `test-${params.event_type}-${Date.now()}`;

      // Create webhook payload
      const webhook: Flow2PayWebhookPayload = {
        evento: params.event_type,
        txid: params.txid || (params.event_type === 'PixIn' ? generateRandomId() : undefined),
        idEnvio: params.id_envio || (params.event_type === 'PixOut' ? generateRandomId() : undefined),
        endToEndId: params.end_to_end_id || `E${Math.floor(Math.random() * 1000000000000)}`,
        valor: params.amount || Math.floor(Math.random() * 10000) + 100, // Random amount between 1 and 100 reais
        status: params.status || 'Sucesso',
        horario: new Date().toISOString(),
      };

      // Add payer/receiver info based on event type
      if (params.event_type === 'PixIn') {
        webhook.pagador = {
          nome: 'Cliente de Teste',
          chave: '<EMAIL>',
          tipoConta: 'CACC',
        };
      } else if (params.event_type === 'PixOut') {
        webhook.recebedor = {
          nome: 'Destinatário de Teste',
          codigoBanco: '260',
          cpf_cnpj: '12345678901',
        };
      }

      // Store webhook in database
      await db.insert(flow2payWebhooks).values({
        id: eventId,
        eventType: webhook.evento,
        txid: webhook.txid || null,
        idEnvio: webhook.idEnvio || null,
        endToEndId: webhook.endToEndId || null,
        valor: webhook.valor,
        status: 'processing',
        payload: webhook,
        processed: false,
      });

      logger.info('Test webhook created', {
        event_type: webhook.evento,
        event_id: eventId,
        txid: webhook.txid,
        id_envio: webhook.idEnvio,
      });

      // Forward to Pluggou
      if (params.user_id) {
        try {
          // Prepare a PixEvent from the webhook
          const pixEvent = {
            event_type: webhook.evento,
            user_id: params.user_id,
            amount: webhook.valor,
            flow2pay_id: webhook.txid || webhook.idEnvio,
            end_to_end_id: webhook.endToEndId,
            webhook_payload: webhook,
            timestamp: webhook.horario,
            status: webhook.status || 'Sucesso',
            event_id: eventId,
            published_at: new Date().toISOString(),
          };

          await forwardToPluggou(pixEvent);
          logger.info('Test webhook forwarded to Pluggou', { event_id: eventId });
        } catch (error) {
          logger.error('Failed to forward test webhook to Pluggou', {
            error: error instanceof Error ? error.message : 'Unknown error',
            event_id: eventId,
          });
        }
      }

      // Process webhook
      try {
        await processWebhook(webhook);
        logger.info('Test webhook processed', { event_id: eventId });

        // Mark as processed
        await db.update(flow2payWebhooks)
          .set({
            processed: true,
            processedAt: new Date(),
            status: 'completed',
          })
          .where(eq(flow2payWebhooks.id, eventId));
      } catch (error) {
        logger.error('Failed to process test webhook', {
          error: error instanceof Error ? error.message : 'Unknown error',
          event_id: eventId,
        });

        // Mark as failed
        await db.update(flow2payWebhooks)
          .set({
            processed: true,
            processedAt: new Date(),
            status: 'failed',
            errorMessage: error instanceof Error ? error.message : 'Unknown error',
          })
          .where(eq(flow2payWebhooks.id, eventId));
      }

      return {
        success: true,
        message: `Test webhook for ${params.event_type} created and processed`,
        details: {
          event_id: eventId,
          event_type: webhook.evento,
          txid: webhook.txid,
          id_envio: webhook.idEnvio,
          end_to_end_id: webhook.endToEndId,
          amount: webhook.valor,
          user_id: params.user_id,
        },
      };
    } catch (error) {
      logger.error('Error creating test webhook', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }
);

/**
 * Generate a random ID for test webhooks
 */
function generateRandomId(): string {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  // Generate a random ID between 26 and 35 characters (Flow2Pay requirement)
  const length = Math.floor(Math.random() * 10) + 26;
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}
