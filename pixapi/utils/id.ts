import { createId } from '@paralleldrive/cuid2';

/**
 * Generate a new CUID
 * @returns A new CUID string
 */
export function generateId(): string {
  return createId();
}

/**
 * Validate if a string is a valid CUID
 * @param id The ID to validate
 * @returns True if the ID is a valid CUID, false otherwise
 */
export function isValidId(id: string): boolean {
  // Basic validation for CUID format
  // CUIDs are typically 24-36 characters long and contain only lowercase letters and numbers
  return /^[a-z0-9]{24,36}$/.test(id);
}
