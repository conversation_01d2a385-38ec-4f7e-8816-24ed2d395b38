-- Migration to add optimized indexes for text-based CUID IDs
-- This improves performance for foreign key relationships and common queries

-- Set search path
SET search_path TO pixapi, public;

-- Add indexes on foreign key columns
CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON pixapi.transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_transactions_qr_code_id ON pixapi.transactions(qr_code_id);
CREATE INDEX IF NOT EXISTS idx_qr_codes_user_id ON pixapi.qr_codes(user_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_user_id ON pixapi.api_keys(user_id);
CREATE INDEX IF NOT EXISTS idx_api_key_usage_api_key_id ON pixapi.api_key_usage(api_key_id);
CREATE INDEX IF NOT EXISTS idx_webhook_configs_user_id ON pixapi.webhook_configs(user_id);
CREATE INDEX IF NOT EXISTS idx_webhook_delivery_logs_webhook_config_id ON pixapi.webhook_delivery_logs(webhook_config_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON pixapi.audit_logs(user_id);

-- Add composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_transactions_user_id_status ON pixapi.transactions(user_id, status);
CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON pixapi.transactions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_qr_codes_status ON pixapi.qr_codes(status);
CREATE INDEX IF NOT EXISTS idx_flow2pay_webhooks_event_type ON pixapi.flow2pay_webhooks(event_type);
CREATE INDEX IF NOT EXISTS idx_flow2pay_webhooks_txid ON pixapi.flow2pay_webhooks(txid) WHERE txid IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_flow2pay_webhooks_end_to_end_id ON pixapi.flow2pay_webhooks(end_to_end_id) WHERE end_to_end_id IS NOT NULL;

-- Add partial indexes for active records
CREATE INDEX IF NOT EXISTS idx_api_keys_active ON pixapi.api_keys(id) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_webhook_configs_active ON pixapi.webhook_configs(id) WHERE is_active = true;

-- Add indexes for webhook processing
CREATE INDEX IF NOT EXISTS idx_webhooks_processed ON pixapi.webhooks(id) WHERE processed = false;
CREATE INDEX IF NOT EXISTS idx_flow2pay_webhooks_processed ON pixapi.flow2pay_webhooks(id) WHERE processed = false;

-- Add indexes for timestamp-based queries
CREATE INDEX IF NOT EXISTS idx_transactions_updated_at ON pixapi.transactions(updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_qr_codes_created_at ON pixapi.qr_codes(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_webhooks_received_at ON pixapi.webhooks(received_at DESC);

-- Insert this migration into the tracking table
INSERT INTO pixapi.drizzle_migrations (id, hash, created_at)
VALUES ('0002_optimize_indexes', 'optimize_indexes_for_cuid', NOW());
