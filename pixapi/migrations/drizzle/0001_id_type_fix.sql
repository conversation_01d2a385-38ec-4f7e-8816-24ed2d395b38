-- Migration to fix ID type mismatch between Dr<PERSON><PERSON> schema and PostgreSQL tables
-- Convert BIGSERIAL/SERIAL IDs to TEXT with CUID format

-- Create a temporary function to generate CUIDs
CREATE OR REPLACE FUNCTION pixapi.generate_cuid() RETURNS TEXT AS $$
DECLARE
    timestamp_part TEXT;
    random_part TEXT;
    counter_part TEXT;
    result TEXT;
BEGIN
    -- Generate a simple CUID-like string (not a real CUID but similar format)
    -- Format: c + timestamp + random + counter
    timestamp_part := to_char(extract(epoch FROM now()), 'FM9999999999');
    random_part := encode(gen_random_bytes(8), 'hex');
    counter_part := encode(gen_random_bytes(4), 'hex');
    result := 'c' || timestamp_part || random_part || counter_part;
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Create schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS pixapi;

-- Set search path
SET search_path TO pixapi, public;

-- Update users table
ALTER TABLE IF EXISTS pixapi.users 
    ALTER COLUMN id TYPE TEXT,
    ALTER COLUMN id SET DEFAULT pixapi.generate_cuid(),
    ALTER COLUMN id DROP IDENTITY IF EXISTS;

-- Update api_keys table
ALTER TABLE IF EXISTS pixapi.api_keys 
    ALTER COLUMN id TYPE TEXT,
    ALTER COLUMN id SET DEFAULT pixapi.generate_cuid(),
    ALTER COLUMN id DROP IDENTITY IF EXISTS;

-- Update api_key_usage table
ALTER TABLE IF EXISTS pixapi.api_key_usage 
    ALTER COLUMN id TYPE TEXT,
    ALTER COLUMN id SET DEFAULT pixapi.generate_cuid(),
    ALTER COLUMN id DROP IDENTITY IF EXISTS;

-- Update auth_tests table
ALTER TABLE IF EXISTS pixapi.auth_tests 
    ALTER COLUMN id TYPE TEXT,
    ALTER COLUMN id SET DEFAULT pixapi.generate_cuid(),
    ALTER COLUMN id DROP IDENTITY IF EXISTS;

-- Update qr_codes table
ALTER TABLE IF EXISTS pixapi.qr_codes 
    ALTER COLUMN id TYPE TEXT,
    ALTER COLUMN id SET DEFAULT pixapi.generate_cuid(),
    ALTER COLUMN id DROP IDENTITY IF EXISTS;

-- Update transactions table
ALTER TABLE IF EXISTS pixapi.transactions 
    ALTER COLUMN id TYPE TEXT,
    ALTER COLUMN id SET DEFAULT pixapi.generate_cuid(),
    ALTER COLUMN id DROP IDENTITY IF EXISTS;

-- Update webhook_configs table
ALTER TABLE IF EXISTS pixapi.webhook_configs 
    ALTER COLUMN id TYPE TEXT,
    ALTER COLUMN id SET DEFAULT pixapi.generate_cuid(),
    ALTER COLUMN id DROP IDENTITY IF EXISTS;

-- Update webhook_delivery_logs table
ALTER TABLE IF EXISTS pixapi.webhook_delivery_logs 
    ALTER COLUMN id TYPE TEXT,
    ALTER COLUMN id SET DEFAULT pixapi.generate_cuid(),
    ALTER COLUMN id DROP IDENTITY IF EXISTS;

-- Update webhooks table
ALTER TABLE IF EXISTS pixapi.webhooks 
    ALTER COLUMN id TYPE TEXT,
    ALTER COLUMN id SET DEFAULT pixapi.generate_cuid(),
    ALTER COLUMN id DROP IDENTITY IF EXISTS;

-- Update flow2pay_webhooks table
ALTER TABLE IF EXISTS pixapi.flow2pay_webhooks 
    ALTER COLUMN id TYPE TEXT,
    ALTER COLUMN id SET DEFAULT pixapi.generate_cuid(),
    ALTER COLUMN id DROP IDENTITY IF EXISTS;

-- Update audit_logs table
ALTER TABLE IF EXISTS pixapi.audit_logs 
    ALTER COLUMN id TYPE TEXT,
    ALTER COLUMN id SET DEFAULT pixapi.generate_cuid(),
    ALTER COLUMN id DROP IDENTITY IF EXISTS;

-- Update foreign key columns to match primary key type
ALTER TABLE IF EXISTS pixapi.api_keys 
    ALTER COLUMN user_id TYPE TEXT USING user_id::TEXT;

ALTER TABLE IF EXISTS pixapi.api_key_usage 
    ALTER COLUMN api_key_id TYPE TEXT USING api_key_id::TEXT;

ALTER TABLE IF EXISTS pixapi.qr_codes 
    ALTER COLUMN user_id TYPE TEXT USING user_id::TEXT;

ALTER TABLE IF EXISTS pixapi.transactions 
    ALTER COLUMN user_id TYPE TEXT USING user_id::TEXT,
    ALTER COLUMN qr_code_id TYPE TEXT USING qr_code_id::TEXT;

ALTER TABLE IF EXISTS pixapi.webhook_configs 
    ALTER COLUMN user_id TYPE TEXT USING user_id::TEXT;

ALTER TABLE IF EXISTS pixapi.webhook_delivery_logs 
    ALTER COLUMN webhook_config_id TYPE TEXT USING webhook_config_id::TEXT;

ALTER TABLE IF EXISTS pixapi.audit_logs 
    ALTER COLUMN user_id TYPE TEXT USING user_id::TEXT;

-- Drop the temporary function
DROP FUNCTION IF EXISTS pixapi.generate_cuid();

-- Update the journal to mark this migration as applied
INSERT INTO pixapi.drizzle_migrations (id, hash, created_at)
VALUES ('0001_id_type_fix', 'id_type_fix_migration', NOW())
ON CONFLICT (id) DO NOTHING;
