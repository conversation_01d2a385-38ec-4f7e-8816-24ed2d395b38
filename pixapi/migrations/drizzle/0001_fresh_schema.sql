-- Migration to create a fresh schema with consistent ID types (all text-based CUID)
-- This ensures all foreign key relationships work correctly

-- Create schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS pixapi;

-- Set search path
SET search_path TO pixapi, public;

-- Create users table
CREATE TABLE pixapi.users (
    id TEXT PRIMARY KEY NOT NULL,
    email TEXT NOT NULL UNIQUE,
    name TEXT NOT NULL,
    partner_id TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create QR codes table
CREATE TABLE pixapi.qr_codes (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT REFERENCES pixapi.users(id),
    txid TEXT NOT NULL UNIQUE,
    amount INTEGER NOT NULL,
    description TEXT,
    expiration_time INTEGER NOT NULL,
    qr_code_image TEXT,
    qr_code_text TEXT NOT NULL,
    status TEXT NOT NULL,
    partner_id TEXT,
    external_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create transactions table
CREATE TABLE pixapi.transactions (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL REFERENCES pixapi.users(id),
    qr_code_id TEXT REFERENCES pixapi.qr_codes(id),
    type TEXT NOT NULL,
    amount INTEGER NOT NULL,
    currency TEXT NOT NULL DEFAULT 'BRL',
    status TEXT NOT NULL,
    description TEXT,
    flow2pay_txid TEXT,
    flow2pay_end_to_end_id TEXT,
    flow2pay_id_envio TEXT,
    webhook_payload JSONB,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create API keys table
CREATE TABLE pixapi.api_keys (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL REFERENCES pixapi.users(id),
    api_key TEXT NOT NULL UNIQUE,
    name TEXT NOT NULL,
    permissions JSONB NOT NULL,
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    last_used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    rate_limit INTEGER,
    rate_limit_window INTEGER,
    usage_count INTEGER DEFAULT 0
);

-- Create API key usage table
CREATE TABLE pixapi.api_key_usage (
    id TEXT PRIMARY KEY NOT NULL,
    api_key_id TEXT NOT NULL REFERENCES pixapi.api_keys(id) ON DELETE CASCADE,
    endpoint TEXT NOT NULL,
    ip_address TEXT,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create webhook configs table
CREATE TABLE pixapi.webhook_configs (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL REFERENCES pixapi.users(id),
    url TEXT NOT NULL,
    secret_key TEXT NOT NULL,
    events JSONB NOT NULL,
    flow2pay_events JSONB NOT NULL DEFAULT '["PixIn", "PixInReversal", "PixOut", "PixOutReversalExternal"]',
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    last_called_at TIMESTAMP WITH TIME ZONE,
    usage_count INTEGER
);

-- Create webhook delivery logs table
CREATE TABLE pixapi.webhook_delivery_logs (
    id TEXT PRIMARY KEY NOT NULL,
    webhook_config_id TEXT NOT NULL REFERENCES pixapi.webhook_configs(id),
    event_type TEXT NOT NULL,
    payload JSONB NOT NULL,
    status TEXT NOT NULL,
    status_code INTEGER,
    response_body TEXT,
    attempt_count INTEGER NOT NULL DEFAULT 1,
    next_retry_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create auth tests table
CREATE TABLE pixapi.auth_tests (
    id TEXT PRIMARY KEY NOT NULL,
    partner_id TEXT NOT NULL,
    success BOOLEAN NOT NULL,
    token TEXT,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create webhooks table
CREATE TABLE pixapi.webhooks (
    id TEXT PRIMARY KEY NOT NULL,
    source TEXT NOT NULL,
    event_type TEXT NOT NULL,
    payload JSONB NOT NULL,
    processed BOOLEAN NOT NULL DEFAULT FALSE,
    error_message TEXT,
    received_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE
);

-- Create Flow2Pay webhooks table
CREATE TABLE pixapi.flow2pay_webhooks (
    id TEXT PRIMARY KEY NOT NULL,
    event_type TEXT NOT NULL,
    txid TEXT,
    id_envio TEXT,
    end_to_end_id TEXT,
    valor INTEGER NOT NULL,
    status TEXT NOT NULL,
    payload JSONB NOT NULL,
    processed BOOLEAN NOT NULL DEFAULT FALSE,
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create audit logs table
CREATE TABLE pixapi.audit_logs (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT REFERENCES pixapi.users(id),
    action TEXT NOT NULL,
    entity_type TEXT NOT NULL,
    entity_id TEXT,
    ip_address TEXT,
    user_agent TEXT,
    details JSONB,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create Drizzle migrations tracking table
CREATE TABLE pixapi.drizzle_migrations (
    id TEXT PRIMARY KEY,
    hash TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Insert this migration into the tracking table
INSERT INTO pixapi.drizzle_migrations (id, hash, created_at)
VALUES ('0001_fresh_schema', 'fresh_schema_with_consistent_ids', NOW());
