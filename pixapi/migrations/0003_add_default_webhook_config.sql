-- Migration: Add default webhook configuration for app.pluggou.io
-- This resolves foreign key constraint issues when logging webhook deliveries

-- First, ensure we have a default user (if not exists)
INSERT INTO users (id, email, name, partner_id, created_at, updated_at)
VALUES (
    'default-user-pluggou',
    '<EMAIL>',
    'Sistema Pluggou',
    'pluggou',
    NOW(),
    NOW()
) ON CONFLICT (id) DO NOTHING;

-- Create a default webhook configuration for app.pluggou.io
INSERT INTO webhook_configs (
    id,
    user_id,
    url,
    secret_key,
    events,
    flow2pay_events,
    description,
    is_active,
    created_at,
    updated_at
) VALUES (
    'default',
    'default-user-pluggou',
    'https://app.pluggou.io/api/webhooks/pluggou-pix',
    'default-secret-key-for-pluggou',
    '["pix_in", "pix_out", "pix_in_reversal", "pix_out_reversal"]',
    '["PixIn", "PixInReversal", "PixOut", "PixOutReversalExternal"]',
    'Configuração padrão para app.pluggou.io',
    true,
    NOW(),
    NOW()
) ON CONFLICT (id) DO UPDATE SET
    url = EXCLUDED.url,
    events = EXCLUDED.events,
    flow2pay_events = EXCLUDED.flow2pay_events,
    description = EXCLUDED.description,
    updated_at = NOW();
