-- Add Svix-related fields to flow2pay_webhooks table
ALTER TABLE flow2pay_webhooks 
ADD COLUMN IF NOT EXISTS published_to_svix BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS svix_message_id TEXT,
ADD COLUMN IF NOT EXISTS svix_event_type TEXT,
ADD COLUMN IF NOT EXISTS published_at TIMESTAMP WITH TIME ZONE;

-- Create index on flow2pay_webhooks.svix_message_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_flow2pay_webhooks_svix_message_id ON flow2pay_webhooks(svix_message_id);

-- Create index on flow2pay_webhooks.svix_event_type for faster lookups
CREATE INDEX IF NOT EXISTS idx_flow2pay_webhooks_svix_event_type ON flow2pay_webhooks(svix_event_type);
