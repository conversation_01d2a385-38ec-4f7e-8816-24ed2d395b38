{"id": "9140ae64-b809-4711-9d0e-adc8d96c711e", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.api_key_usage": {"name": "api_key_usage", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "api_key_id": {"name": "api_key_id", "type": "text", "primaryKey": false, "notNull": true}, "endpoint": {"name": "endpoint", "type": "text", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"api_key_usage_api_key_id_api_keys_id_fk": {"name": "api_key_usage_api_key_id_api_keys_id_fk", "tableFrom": "api_key_usage", "tableTo": "api_keys", "columnsFrom": ["api_key_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.api_keys": {"name": "api_keys", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "api_key": {"name": "api_key", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "permissions": {"name": "permissions", "type": "jsonb", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "last_used_at": {"name": "last_used_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "rate_limit": {"name": "rate_limit", "type": "integer", "primaryKey": false, "notNull": false}, "rate_limit_window": {"name": "rate_limit_window", "type": "integer", "primaryKey": false, "notNull": false}, "usage_count": {"name": "usage_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}}, "indexes": {}, "foreignKeys": {"api_keys_user_id_users_id_fk": {"name": "api_keys_user_id_users_id_fk", "tableFrom": "api_keys", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"api_keys_api_key_unique": {"name": "api_keys_api_key_unique", "nullsNotDistinct": false, "columns": ["api_key"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.audit_logs": {"name": "audit_logs", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false}, "action": {"name": "action", "type": "text", "primaryKey": false, "notNull": true}, "entity_type": {"name": "entity_type", "type": "text", "primaryKey": false, "notNull": true}, "entity_id": {"name": "entity_id", "type": "text", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "details": {"name": "details", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"audit_logs_user_id_users_id_fk": {"name": "audit_logs_user_id_users_id_fk", "tableFrom": "audit_logs", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.auth_tests": {"name": "auth_tests", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "partner_id": {"name": "partner_id", "type": "text", "primaryKey": false, "notNull": true}, "success": {"name": "success", "type": "boolean", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": false}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.flow2pay_webhooks": {"name": "flow2pay_webhooks", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "event_type": {"name": "event_type", "type": "text", "primaryKey": false, "notNull": true}, "txid": {"name": "txid", "type": "text", "primaryKey": false, "notNull": false}, "id_envio": {"name": "id_envio", "type": "text", "primaryKey": false, "notNull": false}, "end_to_end_id": {"name": "end_to_end_id", "type": "text", "primaryKey": false, "notNull": false}, "valor": {"name": "valor", "type": "integer", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "payload": {"name": "payload", "type": "jsonb", "primaryKey": false, "notNull": true}, "processed": {"name": "processed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "processed_at": {"name": "processed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.qr_codes": {"name": "qr_codes", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false}, "txid": {"name": "txid", "type": "text", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "expiration_time": {"name": "expiration_time", "type": "integer", "primaryKey": false, "notNull": true}, "qr_code_image": {"name": "qr_code_image", "type": "text", "primaryKey": false, "notNull": false}, "qr_code_text": {"name": "qr_code_text", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "partner_id": {"name": "partner_id", "type": "text", "primaryKey": false, "notNull": false}, "external_id": {"name": "external_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"qr_codes_user_id_users_id_fk": {"name": "qr_codes_user_id_users_id_fk", "tableFrom": "qr_codes", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"qr_codes_txid_unique": {"name": "qr_codes_txid_unique", "nullsNotDistinct": false, "columns": ["txid"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.transactions": {"name": "transactions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "qr_code_id": {"name": "qr_code_id", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true, "default": "'BRL'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "flow2pay_txid": {"name": "flow2pay_txid", "type": "text", "primaryKey": false, "notNull": false}, "flow2pay_end_to_end_id": {"name": "flow2pay_end_to_end_id", "type": "text", "primaryKey": false, "notNull": false}, "flow2pay_id_envio": {"name": "flow2pay_id_envio", "type": "text", "primaryKey": false, "notNull": false}, "webhook_payload": {"name": "webhook_payload", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"transactions_user_id_users_id_fk": {"name": "transactions_user_id_users_id_fk", "tableFrom": "transactions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "transactions_qr_code_id_qr_codes_id_fk": {"name": "transactions_qr_code_id_qr_codes_id_fk", "tableFrom": "transactions", "tableTo": "qr_codes", "columnsFrom": ["qr_code_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "partner_id": {"name": "partner_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.webhook_configs": {"name": "webhook_configs", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true}, "secret_key": {"name": "secret_key", "type": "text", "primaryKey": false, "notNull": true}, "events": {"name": "events", "type": "jsonb", "primaryKey": false, "notNull": true}, "flow2pay_events": {"name": "flow2pay_events", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'[\"PixIn\", \"PixInReversal\", \"PixOut\", \"PixOutReversalExternal\"]'"}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "last_called_at": {"name": "last_called_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "usage_count": {"name": "usage_count", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"webhook_configs_user_id_users_id_fk": {"name": "webhook_configs_user_id_users_id_fk", "tableFrom": "webhook_configs", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.webhook_delivery_logs": {"name": "webhook_delivery_logs", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "webhook_config_id": {"name": "webhook_config_id", "type": "text", "primaryKey": false, "notNull": true}, "event_type": {"name": "event_type", "type": "text", "primaryKey": false, "notNull": true}, "payload": {"name": "payload", "type": "jsonb", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "status_code": {"name": "status_code", "type": "integer", "primaryKey": false, "notNull": false}, "response_body": {"name": "response_body", "type": "text", "primaryKey": false, "notNull": false}, "attempt_count": {"name": "attempt_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "next_retry_at": {"name": "next_retry_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"webhook_delivery_logs_webhook_config_id_webhook_configs_id_fk": {"name": "webhook_delivery_logs_webhook_config_id_webhook_configs_id_fk", "tableFrom": "webhook_delivery_logs", "tableTo": "webhook_configs", "columnsFrom": ["webhook_config_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.webhooks": {"name": "webhooks", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "source": {"name": "source", "type": "text", "primaryKey": false, "notNull": true}, "event_type": {"name": "event_type", "type": "text", "primaryKey": false, "notNull": true}, "payload": {"name": "payload", "type": "jsonb", "primaryKey": false, "notNull": true}, "processed": {"name": "processed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "received_at": {"name": "received_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "processed_at": {"name": "processed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {"pixapi": "pixapi"}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}