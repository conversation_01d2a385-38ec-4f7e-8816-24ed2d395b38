import { api, Header } from 'encore.dev/api';
import log from 'encore.dev/log';
import { Flow2PayWebhookPayload, processWebhook } from './webhooks';
import { operations } from './db';
import { forwardToPluggou } from './webhook-forwarder';
import { sendSvixMessage, mapToSvixEventType, isSvixAvailable } from './svix';
import { eq, desc, and, or, not, isNull, gte } from 'drizzle-orm';
import { flow2payWebhooks, webhookConfigs, users } from './db/schema';
import { db } from './db';
import { validateAPIKey, API_PERMISSIONS } from './auth';

/**
 * Debug API to check Flow2Pay webhook delivery and processing
 */
export const debugTestWebhook = api(
	{ method: 'POST', path: '/debug/webhooks/test', expose: true },
	async (params: {
		webhook_payload: Flow2PayWebhookPayload;
		skip_processing?: boolean;
		only_svix?: boolean;
		only_pluggou?: boolean;
		user_id?: string;
	}): Promise<{
		success: boolean;
		flow2pay_receipt?: { success: boolean; message: string };
		svix_delivery?: { success: boolean; message_id?: string; message?: string };
		pluggou_delivery?: { success: boolean; message?: string };
		db_storage?: { success: boolean; message?: string };
	}> => {
		const logger = log.with({ action: 'debug_webhook_process' });
		const {
			webhook_payload,
			skip_processing,
			only_svix,
			only_pluggou,
			user_id,
		} = params;

		logger.info('Processing test webhook', {
			event_type: webhook_payload.evento,
			has_txid: !!webhook_payload.txid,
			has_id_envio: !!webhook_payload.idEnvio,
			has_end_to_end_id: !!webhook_payload.endToEndId,
		});

		const results: any = {
			success: false,
		};

		// 1. Store in database
		if (!only_svix && !only_pluggou) {
			try {
				await operations.webhookOperations.storeFlow2PayWebhook({
					eventType: webhook_payload.evento,
					txid:
						webhook_payload.evento === 'PixIn'
							? webhook_payload.txid
							: undefined,
					idEnvio:
						webhook_payload.evento === 'PixOut'
							? webhook_payload.idEnvio
							: undefined,
					endToEndId: webhook_payload.endToEndId,
					valor: webhook_payload.valor,
					status: webhook_payload.status || 'Sucesso',
					payload: webhook_payload,
				});

				results.db_storage = {
					success: true,
					message: 'Webhook stored in database successfully',
				};

				logger.info('Test webhook stored in database');
			} catch (dbError) {
				logger.error('Error storing test webhook in database', {
					error: dbError instanceof Error ? dbError.message : 'Unknown error',
				});

				results.db_storage = {
					success: false,
					message: dbError instanceof Error ? dbError.message : 'Unknown error',
				};
			}
		}

		// 2. Send to Svix
		if (only_svix || (!only_pluggou && !skip_processing)) {
			try {
				const svixEventType = mapToSvixEventType(
					webhook_payload.evento,
					webhook_payload.status
				);

				const payload = {
					event_type: webhook_payload.evento,
					flow2pay_event_type: webhook_payload.evento,
					svix_event_type: svixEventType,
					user_id: user_id || '0',
					webhook_payload,
				};

				const messageId = await sendSvixMessage(
					svixEventType,
					payload,
					user_id
				);

				results.svix_delivery = {
					success: true,
					message_id: messageId,
					message: 'Webhook sent to Svix successfully',
				};

				logger.info('Test webhook sent to Svix', { message_id: messageId });
			} catch (svixError) {
				logger.error('Error sending test webhook to Svix', {
					error:
						svixError instanceof Error ? svixError.message : 'Unknown error',
				});

				results.svix_delivery = {
					success: false,
					message:
						svixError instanceof Error ? svixError.message : 'Unknown error',
				};
			}
		}

		// 3. Send to Pluggou
		if (only_pluggou || (!only_svix && !skip_processing)) {
			try {
				// Create a PixEvent from the webhook payload
				const pixEvent = {
					event_type: webhook_payload.evento,
					user_id: user_id || '0',
					amount: Math.abs(webhook_payload.valor),
					flow2pay_id: webhook_payload.txid || webhook_payload.idEnvio,
					end_to_end_id: webhook_payload.endToEndId,
					webhook_payload: webhook_payload,
					timestamp: webhook_payload.horario,
					status: webhook_payload.status || 'Sucesso',
					event_id: `test-${Date.now()}`,
					published_at: new Date().toISOString(),
				};

				await forwardToPluggou(pixEvent);

				results.pluggou_delivery = {
					success: true,
					message: 'Webhook forwarded to app.pluggou.io successfully',
				};

				logger.info('Test webhook forwarded to app.pluggou.io');
			} catch (pluggouError) {
				logger.error('Error forwarding test webhook to app.pluggou.io', {
					error:
						pluggouError instanceof Error
							? pluggouError.message
							: 'Unknown error',
				});

				results.pluggou_delivery = {
					success: false,
					message:
						pluggouError instanceof Error
							? pluggouError.message
							: 'Unknown error',
				};
			}
		}

		// 4. Process webhook
		if (!only_svix && !only_pluggou && !skip_processing) {
			try {
				await processWebhook(webhook_payload);

				results.flow2pay_receipt = {
					success: true,
					message: 'Webhook processed successfully',
				};

				logger.info('Test webhook processed successfully');
			} catch (processError) {
				logger.error('Error processing test webhook', {
					error:
						processError instanceof Error
							? processError.message
							: 'Unknown error',
				});

				results.flow2pay_receipt = {
					success: false,
					message:
						processError instanceof Error
							? processError.message
							: 'Unknown error',
				};
			}
		}

		// Set overall success
		results.success =
			(!results.db_storage || results.db_storage.success) &&
			(!results.svix_delivery || results.svix_delivery.success) &&
			(!results.pluggou_delivery || results.pluggou_delivery.success) &&
			(!results.flow2pay_receipt || results.flow2pay_receipt.success);

		return results;
	}
);

/**
 * Debug API to check database records for webhooks
 */
export const listWebhookRecords = api(
	{ method: 'GET', path: '/debug/webhooks/list', expose: true },
	async (params: {
		limit?: number;
		event_type?: string;
	}): Promise<{
		records: any[];
		total: number;
	}> => {
		const logger = log.with({ action: 'list_webhook_records' });
		const { limit = 10, event_type } = params;

		try {
			// Query webhooks directly using Drizzle
			const records = await db.query.flow2payWebhooks.findMany({
				limit,
				where: event_type
					? eq(flow2payWebhooks.eventType, event_type)
					: undefined,
				orderBy: (webhooks, { desc }) => [desc(webhooks.createdAt)],
			});

			logger.info('Retrieved webhook records', { count: records.length });

			return {
				records,
				total: records.length,
			};
		} catch (error) {
			logger.error('Error retrieving webhook records', {
				error: error instanceof Error ? error.message : 'Unknown error',
			});

			throw error;
		}
	}
);

/**
 * Debug endpoint to test webhook processing without affecting the main webhook flow
 * This is useful for testing webhook handling without triggering real events
 */
export const debugWebhookProcess = api(
	{ method: 'POST', path: '/debug-webhook', expose: true },
	async (params: {
		api_key: Header<'X-API-Key'>;
		webhook_payload: Flow2PayWebhookPayload;
		skip_processing?: boolean;
		only_svix?: boolean;
		only_pluggou?: boolean;
		user_id?: string;
	}): Promise<{
		success: boolean;
		flow2pay_receipt?: { success: boolean; message: string };
		svix_delivery?: { success: boolean; message_id?: string; message?: string };
		pluggou_delivery?: { success: boolean; message?: string };
		db_storage?: { success: boolean; message?: string };
	}> => {
		const logger = log.with({ action: 'debug_webhook_process' });

		try {
			// Validate API key
			await validateAPIKey(
				params.api_key,
				API_PERMISSIONS.ALL,
				'/debug-webhook'
			);

			const {
				webhook_payload,
				skip_processing,
				only_svix,
				only_pluggou,
				user_id,
			} = params;

			const results: {
				success: boolean;
				flow2pay_receipt?: { success: boolean; message: string };
				svix_delivery?: {
					success: boolean;
					message_id?: string;
					message?: string;
				};
				pluggou_delivery?: { success: boolean; message?: string };
				db_storage?: { success: boolean; message?: string };
			} = {
				success: true,
			};

			// 1. Store in database
			try {
				// Generate a unique ID for this test
				const webhookId = `debug-${Date.now()}`;

				await db.insert(flow2payWebhooks).values({
					id: webhookId,
					eventType: webhook_payload.evento,
					txid: webhook_payload.txid || null,
					idEnvio: webhook_payload.idEnvio || null,
					endToEndId: webhook_payload.endToEndId || null,
					valor: webhook_payload.valor,
					status: 'debug',
					payload: webhook_payload,
					processed: false,
				});

				results.db_storage = {
					success: true,
					message: `Debug webhook stored with ID: ${webhookId}`,
				};

				logger.info('Debug webhook stored in database', {
					webhook_id: webhookId,
				});
			} catch (dbError) {
				logger.error('Failed to store debug webhook', {
					error: dbError instanceof Error ? dbError.message : 'Unknown error',
				});

				results.db_storage = {
					success: false,
					message: dbError instanceof Error ? dbError.message : 'Unknown error',
				};
			}

			// 2. Send to Svix
			if (only_svix || (!only_pluggou && !skip_processing)) {
				try {
					// Map Flow2Pay event type to Svix event type
					const svixEventType = mapToSvixEventType(
						webhook_payload.evento,
						webhook_payload.status
					);

					// Prepare the payload for Svix
					const idField = webhook_payload.idEnvio || webhook_payload.txid;
					const svixPayload = {
						event_type: webhook_payload.evento,
						flow2pay_event_type: webhook_payload.evento,
						svix_event_type: svixEventType,
						user_id: user_id || '0',
						amount: Math.abs(webhook_payload.valor),
						flow2pay_id: idField,
						end_to_end_id: webhook_payload.endToEndId,
						webhook_payload: webhook_payload,
						timestamp: webhook_payload.horario,
						status: webhook_payload.status || 'Sucesso',
						event_id: `debug-${webhook_payload.evento}-${Date.now()}`,
					};

					// Send the message to Svix
					const messageId = await sendSvixMessage(
						svixEventType,
						svixPayload,
						user_id
					);

					results.svix_delivery = {
						success: true,
						message_id: messageId,
						message: 'Message sent to Svix successfully',
					};

					logger.info('Debug webhook sent to Svix', { message_id: messageId });
				} catch (svixError) {
					logger.error('Failed to send debug webhook to Svix', {
						error:
							svixError instanceof Error ? svixError.message : 'Unknown error',
					});

					results.svix_delivery = {
						success: false,
						message:
							svixError instanceof Error ? svixError.message : 'Unknown error',
					};
				}
			}

			// 3. Send to Pluggou
			if (only_pluggou || (!only_svix && !skip_processing)) {
				try {
					// Create a PixEvent from the webhook payload
					const pixEvent = {
						event_type: webhook_payload.evento,
						user_id: user_id || '0',
						amount: Math.abs(webhook_payload.valor),
						flow2pay_id: webhook_payload.txid || webhook_payload.idEnvio,
						end_to_end_id: webhook_payload.endToEndId,
						webhook_payload: webhook_payload,
						timestamp: webhook_payload.horario,
						status: webhook_payload.status || 'Sucesso',
						event_id: `debug-${Date.now()}`,
						published_at: new Date().toISOString(),
					};

					await forwardToPluggou(pixEvent);

					results.pluggou_delivery = {
						success: true,
						message: 'Webhook forwarded to app.pluggou.io successfully',
					};

					logger.info('Debug webhook forwarded to app.pluggou.io');
				} catch (pluggouError) {
					logger.error('Error forwarding debug webhook to app.pluggou.io', {
						error:
							pluggouError instanceof Error
								? pluggouError.message
								: 'Unknown error',
					});

					results.pluggou_delivery = {
						success: false,
						message:
							pluggouError instanceof Error
								? pluggouError.message
								: 'Unknown error',
					};
				}
			}

			// 4. Process webhook
			if (!only_svix && !only_pluggou && !skip_processing) {
				try {
					await processWebhook(webhook_payload);

					results.flow2pay_receipt = {
						success: true,
						message: 'Webhook processed successfully',
					};

					logger.info('Debug webhook processed successfully');
				} catch (processError) {
					logger.error('Error processing debug webhook', {
						error:
							processError instanceof Error
								? processError.message
								: 'Unknown error',
					});

					results.flow2pay_receipt = {
						success: false,
						message:
							processError instanceof Error
								? processError.message
								: 'Unknown error',
					};
				}
			}

			return results;
		} catch (error) {
			logger.error('Error in debug webhook process', {
				error: error instanceof Error ? error.message : 'Unknown error',
			});

			throw error;
		}
	}
);

/**
 * Get information about webhooks and their processing status
 */
export const getWebhookStats = api(
	{ method: 'GET', path: '/webhook-stats', expose: true },
	async (params: {
		api_key: Header<'X-API-Key'>;
		since?: string; // ISO date string
		limit?: number;
	}): Promise<{
		total_webhooks: number;
		processed_webhooks: number;
		failed_webhooks: number;
		pending_webhooks: number;
		by_event_type: Record<
			string,
			{
				total: number;
				processed: number;
				failed: number;
				pending: number;
			}
		>;
		recent_webhooks: Array<{
			id: string;
			event_type: string;
			status: string;
			processed: boolean;
			created_at: string;
			value: number;
		}>;
	}> => {
		const logger = log.with({ action: 'get_webhook_stats' });

		try {
			// Validate API key
			await validateAPIKey(
				params.api_key,
				API_PERMISSIONS.ALL,
				'/webhook-stats'
			);

			const limit = params.limit || 10;
			const since = params.since
				? new Date(params.since)
				: new Date(Date.now() - 24 * 60 * 60 * 1000); // Default to last 24 hours

			// Get webhook statistics
			const webhooks = await db.query.flow2payWebhooks.findMany({
				where: gte(flow2payWebhooks.createdAt, since),
				orderBy: [desc(flow2payWebhooks.createdAt)],
			});

			// Calculate statistics
			const totalWebhooks = webhooks.length;
			const processedWebhooks = webhooks.filter((wh) => wh.processed).length;
			const failedWebhooks = webhooks.filter(
				(wh) => wh.processed && wh.status === 'failed'
			).length;
			const pendingWebhooks = totalWebhooks - processedWebhooks;

			// Group by event type
			const byEventType: Record<
				string,
				{
					total: number;
					processed: number;
					failed: number;
					pending: number;
				}
			> = {};

			webhooks.forEach((wh) => {
				const eventType = wh.eventType;
				if (!byEventType[eventType]) {
					byEventType[eventType] = {
						total: 0,
						processed: 0,
						failed: 0,
						pending: 0,
					};
				}

				byEventType[eventType].total++;

				if (wh.processed) {
					byEventType[eventType].processed++;
					if (wh.status === 'failed') {
						byEventType[eventType].failed++;
					}
				} else {
					byEventType[eventType].pending++;
				}
			});

			// Get recent webhooks
			const recentWebhooks = webhooks.slice(0, limit).map((wh) => ({
				id: wh.id,
				event_type: wh.eventType,
				status: wh.status,
				processed: wh.processed,
				created_at: wh.createdAt.toISOString(),
				value: wh.valor,
			}));

			return {
				total_webhooks: totalWebhooks,
				processed_webhooks: processedWebhooks,
				failed_webhooks: failedWebhooks,
				pending_webhooks: pendingWebhooks,
				by_event_type: byEventType,
				recent_webhooks: recentWebhooks,
			};
		} catch (error) {
			logger.error('Error getting webhook stats', {
				error: error instanceof Error ? error.message : 'Unknown error',
			});

			throw error;
		}
	}
);

/**
 * List failed or unprocessed webhooks
 */
export const listFailedWebhooks = api(
	{ method: 'GET', path: '/failed-webhooks', expose: true },
	async (params: {
		api_key: Header<'X-API-Key'>;
		since?: string; // ISO date string
		include_pending?: boolean;
		limit?: number;
	}): Promise<{
		failed_webhooks: Array<{
			id: string;
			event_type: string;
			status: string;
			txid?: string;
			id_envio?: string;
			end_to_end_id?: string;
			created_at: string;
			error_message?: string;
			value: number;
		}>;
		total_count: number;
	}> => {
		const logger = log.with({ action: 'list_failed_webhooks' });

		try {
			// Validate API key
			await validateAPIKey(
				params.api_key,
				API_PERMISSIONS.ALL,
				'/failed-webhooks'
			);

			const limit = params.limit || 50;
			const since = params.since
				? new Date(params.since)
				: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // Default to last 7 days
			const includePending = params.include_pending || false;

			// Build the where clause
			let whereClause;
			if (includePending) {
				whereClause = and(
					gte(flow2payWebhooks.createdAt, since),
					or(
						eq(flow2payWebhooks.status, 'failed'),
						not(flow2payWebhooks.processed)
					)
				);
			} else {
				whereClause = and(
					gte(flow2payWebhooks.createdAt, since),
					eq(flow2payWebhooks.status, 'failed')
				);
			}

			// Get failed webhooks
			const webhooks = await db.query.flow2payWebhooks.findMany({
				where: whereClause,
				orderBy: [desc(flow2payWebhooks.createdAt)],
				limit,
			});

			// Format response
			const failedWebhooks = webhooks.map((wh) => ({
				id: wh.id,
				event_type: wh.eventType,
				status: wh.status,
				txid: wh.txid || undefined,
				id_envio: wh.idEnvio || undefined,
				end_to_end_id: wh.endToEndId || undefined,
				created_at: wh.createdAt.toISOString(),
				error_message: wh.errorMessage || undefined,
				value: wh.valor,
			}));

			return {
				failed_webhooks: failedWebhooks,
				total_count: failedWebhooks.length,
			};
		} catch (error) {
			logger.error('Error listing failed webhooks', {
				error: error instanceof Error ? error.message : 'Unknown error',
			});

			throw error;
		}
	}
);

/**
 * Retry processing a failed webhook
 */
export const retryWebhook = api(
	{ method: 'POST', path: '/retry-webhook/:id', expose: true },
	async (params: {
		api_key: Header<'X-API-Key'>;
		id: string;
		force_pluggou?: boolean;
	}): Promise<{
		success: boolean;
		message: string;
		retry_result?: {
			processing_success: boolean;
			forwarding_success: boolean;
			error?: string;
		};
	}> => {
		const logger = log.with({ action: 'retry_webhook', webhook_id: params.id });

		try {
			// Validate API key
			await validateAPIKey(
				params.api_key,
				API_PERMISSIONS.ALL,
				'/retry-webhook/:id'
			);

			const { id, force_pluggou } = params;

			// Get the webhook
			const webhook = await db.query.flow2payWebhooks.findFirst({
				where: eq(flow2payWebhooks.id, id),
			});

			if (!webhook) {
				logger.warn('Webhook not found', { id });
				return {
					success: false,
					message: `Webhook with ID ${id} not found`,
				};
			}

			const retryResult = {
				processing_success: false,
				forwarding_success: false,
				error: undefined as string | undefined,
			};

			// Extract webhook payload
			let webhookPayload: Flow2PayWebhookPayload;
			if (webhook.payload && typeof webhook.payload === 'object') {
				// If payload is stored as webhook_payload property (older format)
				if (
					'webhook_payload' in webhook.payload &&
					webhook.payload.webhook_payload
				) {
					webhookPayload = webhook.payload
						.webhook_payload as Flow2PayWebhookPayload;
				}
				// If payload is the Flow2Pay payload directly
				else if ('evento' in webhook.payload) {
					webhookPayload = webhook.payload as Flow2PayWebhookPayload;
				}
				// Try to extract from a different format
				else if (
					'event_type' in webhook.payload &&
					webhook.payload.event_type
				) {
					webhookPayload = {
						evento: webhook.payload.event_type as
							| 'PixIn'
							| 'PixOut'
							| 'PixInReversal'
							| 'PixOutReversalExternal',
						txid: webhook.txid || undefined,
						idEnvio: webhook.idEnvio || undefined,
						endToEndId: webhook.endToEndId || undefined,
						valor: webhook.valor,
						status: webhook.status,
						horario: webhook.createdAt.toISOString(),
					};
				} else {
					return {
						success: false,
						message: 'Could not extract webhook payload from database record',
					};
				}
			} else {
				return {
					success: false,
					message: 'Invalid webhook payload format',
				};
			}

			// First retry processing the webhook
			try {
				await processWebhook(webhookPayload);
				retryResult.processing_success = true;
				logger.info('Successfully reprocessed webhook', { id });
			} catch (error) {
				retryResult.processing_success = false;
				retryResult.error =
					error instanceof Error ? error.message : 'Unknown error';
				logger.error('Failed to reprocess webhook', {
					id,
					error: retryResult.error,
				});
			}

			// Force forward to Pluggou if requested
			if (force_pluggou) {
				try {
					// Create a PixEvent from the webhook
					let userId = '0';

					// Try to extract user_id from the webhook payload
					if (webhook.payload && typeof webhook.payload === 'object') {
						if ('user_id' in webhook.payload && webhook.payload.user_id) {
							userId = String(webhook.payload.user_id);
						}
					}

					const pixEvent = {
						event_type: webhookPayload.evento,
						user_id: userId,
						amount: Math.abs(webhookPayload.valor),
						flow2pay_id: webhookPayload.txid || webhookPayload.idEnvio,
						end_to_end_id: webhookPayload.endToEndId,
						webhook_payload: webhookPayload,
						timestamp: webhookPayload.horario,
						status: webhookPayload.status || 'Sucesso',
						event_id: `retry-${id}-${Date.now()}`,
						published_at: new Date().toISOString(),
					};

					await forwardToPluggou(pixEvent);
					retryResult.forwarding_success = true;
					logger.info('Successfully forwarded webhook to Pluggou', { id });
				} catch (error) {
					retryResult.forwarding_success = false;
					retryResult.error =
						error instanceof Error ? error.message : 'Unknown error';
					logger.error('Failed to forward webhook to Pluggou', {
						id,
						error: retryResult.error,
					});
				}
			}

			// Update the webhook status
			await db
				.update(flow2payWebhooks)
				.set({
					processed: true,
					processedAt: new Date(),
					status: retryResult.processing_success ? 'completed' : 'failed',
				})
				.where(eq(flow2payWebhooks.id, id));

			return {
				success: true,
				message: `Webhook retry ${
					retryResult.processing_success ? 'successful' : 'failed'
				}`,
				retry_result: retryResult,
			};
		} catch (error) {
			logger.error('Error retrying webhook', {
				error: error instanceof Error ? error.message : 'Unknown error',
				webhook_id: params.id,
			});

			throw error;
		}
	}
);

/**
 * Test Svix connectivity and configuration
 */
export const testSvixStatus = api(
	{ method: 'GET', path: '/test-svix-status', expose: true },
	async (params: {
		api_key: Header<'X-API-Key'>;
	}): Promise<{
		svix_status: {
			available: boolean;
			error?: string;
			app_id?: string;
			token_configured?: boolean;
			error_details?: any;
		};
		test_message?: {
			success: boolean;
			message_id?: string;
			error?: string;
		};
	}> => {
		const logger = log.with({ action: 'test_svix_status' });

		try {
			// Validate API key
			await validateAPIKey(
				params.api_key,
				API_PERMISSIONS.ALL,
				'/test-svix-status'
			);

			// Check Svix availability
			const svixStatus = await isSvixAvailable();

			logger.info('Svix status check completed', {
				available: svixStatus.available,
				token_configured: svixStatus.token_configured,
				error: svixStatus.error,
			});

			const result: any = {
				svix_status: svixStatus,
			};

			// If Svix is available, try to send a test message
			if (svixStatus.available) {
				try {
					const testPayload = {
						event_type: 'test',
						test_message: 'Svix connectivity test',
						timestamp: new Date().toISOString(),
						test_id: `test-${Date.now()}`,
					};

					const messageId = await sendSvixMessage(
						'test.connectivity',
						testPayload,
						'test-user'
					);

					result.test_message = {
						success: true,
						message_id: messageId,
					};

					logger.info('Test message sent to Svix successfully', {
						message_id: messageId,
					});
				} catch (testError) {
					result.test_message = {
						success: false,
						error:
							testError instanceof Error ? testError.message : 'Unknown error',
					};

					logger.error('Failed to send test message to Svix', {
						error:
							testError instanceof Error ? testError.message : 'Unknown error',
					});
				}
			}

			return result;
		} catch (error) {
			logger.error('Error testing Svix status', {
				error: error instanceof Error ? error.message : 'Unknown error',
			});

			throw error;
		}
	}
);

/**
 * Initialize default webhook configuration
 */
export const initializeDefaultWebhookConfig = api(
	{ method: 'POST', path: '/initialize-default-webhook-config', expose: true },
	async (params: {
		api_key: Header<'X-API-Key'>;
	}): Promise<{
		success: boolean;
		message: string;
		user_created?: boolean;
		webhook_config_created?: boolean;
	}> => {
		const logger = log.with({ action: 'initialize_default_webhook_config' });

		try {
			// Validate API key
			await validateAPIKey(
				params.api_key,
				API_PERMISSIONS.ALL,
				'/initialize-default-webhook-config'
			);

			let userCreated = false;
			let webhookConfigCreated = false;

			// First, ensure we have a default user
			try {
				const existingUser = await db.query.users.findFirst({
					where: eq(users.id, 'default-user-pluggou'),
				});

				if (!existingUser) {
					await db.insert(users).values({
						id: 'default-user-pluggou',
						email: '<EMAIL>',
						name: 'Sistema Pluggou',
						partnerId: 'pluggou',
					});
					userCreated = true;
					logger.info('Default user created');
				} else {
					logger.info('Default user already exists');
				}
			} catch (userError) {
				logger.error('Error creating default user', {
					error:
						userError instanceof Error ? userError.message : 'Unknown error',
				});
				throw userError;
			}

			// Create default webhook configuration
			try {
				const existingConfig = await db.query.webhookConfigs.findFirst({
					where: eq(webhookConfigs.id, 'default'),
				});

				if (!existingConfig) {
					await db.insert(webhookConfigs).values({
						id: 'default',
						userId: 'default-user-pluggou',
						url: 'https://app.pluggou.io/api/webhooks/pluggou-pix',
						secretKey: 'default-secret-key-for-pluggou',
						events: [
							'pix_in',
							'pix_out',
							'pix_in_reversal',
							'pix_out_reversal',
						],
						flow2payEvents: [
							'PixIn',
							'PixInReversal',
							'PixOut',
							'PixOutReversalExternal',
						],
						description: 'Configuração padrão para app.pluggou.io',
						isActive: true,
					});
					webhookConfigCreated = true;
					logger.info('Default webhook config created');
				} else {
					// Update existing config
					await db
						.update(webhookConfigs)
						.set({
							url: 'https://app.pluggou.io/api/webhooks/pluggou-pix',
							events: [
								'pix_in',
								'pix_out',
								'pix_in_reversal',
								'pix_out_reversal',
							],
							flow2payEvents: [
								'PixIn',
								'PixInReversal',
								'PixOut',
								'PixOutReversalExternal',
							],
							description: 'Configuração padrão para app.pluggou.io',
							isActive: true,
							updatedAt: new Date(),
						})
						.where(eq(webhookConfigs.id, 'default'));
					logger.info('Default webhook config updated');
				}
			} catch (configError) {
				logger.error('Error creating/updating default webhook config', {
					error:
						configError instanceof Error
							? configError.message
							: 'Unknown error',
				});
				throw configError;
			}

			return {
				success: true,
				message: 'Default webhook configuration initialized successfully',
				user_created: userCreated,
				webhook_config_created: webhookConfigCreated,
			};
		} catch (error) {
			logger.error('Error initializing default webhook config', {
				error: error instanceof Error ? error.message : 'Unknown error',
			});

			throw error;
		}
	}
);
