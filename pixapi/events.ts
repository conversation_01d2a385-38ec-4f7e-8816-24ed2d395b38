import log from 'encore.dev/log';
import { sendSvixMessage, mapToSvixEventType } from './svix';

/**
 * PIX event interface
 * Used for internal event broadcasting between services
 */
export interface PixEvent {
	event_type: string; // Event type for routing
	transaction_id?: number;
	original_transaction_id?: number;
	user_id: string;
	amount: number;
	flow2pay_id?: string;
	end_to_end_id?: string;
	webhook_payload?: any;
	timestamp: string;
	status?: string;
	event_id?: string; // Unique identifier for deduplication
	published_at?: string; // When the event was published
	retry_count?: number; // Number of retry attempts
}

/**
 * Publish an event to Svix with proper logging and tracking
 * @param event The event to publish
 * @returns The message ID of the published event
 */
export async function publishPixEvent(event: PixEvent): Promise<string> {
	const logger = log.with({
		action: 'publish_pix_event',
		event_type: event.event_type,
		transaction_id: event.transaction_id,
		user_id: event.user_id,
	});

	// Generate a unique event ID if not provided
	if (!event.event_id) {
		event.event_id = `${Date.now()}-${Math.random()
			.toString(36)
			.substring(2, 15)}`;
	}

	// Add published timestamp
	event.published_at = new Date().toISOString();

	// Set retry count to 0 if not provided
	if (event.retry_count === undefined) {
		event.retry_count = 0;
	}

	logger.info('Publishing PIX event to Svix', {
		event_id: event.event_id,
		flow2pay_id: event.flow2pay_id,
		end_to_end_id: event.end_to_end_id,
		status: event.status,
		event_type: event.event_type,
	});

	try {
		// Map the Flow2Pay event type to a Svix event type
		const svixEventType = mapToSvixEventType(event.event_type, event.status);

		// Prepare the payload for Svix
		const svixPayload = {
			...event,
			flow2pay_event_type: event.event_type,
			svix_event_type: svixEventType,
		};

		// Send the message to Svix
		const messageId = await sendSvixMessage(
			svixEventType,
			svixPayload,
			event.user_id
		);

		logger.info('Successfully published PIX event to Svix', {
			event_id: event.event_id,
			event_type: event.event_type,
			svix_event_type: svixEventType,
			message_id: messageId,
		});

		return messageId;
	} catch (error) {
		logger.error('Failed to publish PIX event to Svix', {
			event_id: event.event_id,
			error: error instanceof Error ? error.message : 'Unknown error',
		});
		throw error;
	}
}

/**
 * PIX event types
 */
export const PIX_EVENT_TYPES = {
	PIX_IN: 'PixIn',
	PIX_OUT: 'PixOut',
	PIX_IN_REVERSAL: 'PixInReversal',
	PIX_OUT_REVERSAL: 'PixOutReversalExternal',
	QR_CODE_GENERATED: 'QrCodeGenerated',
	QR_CODE_EXPIRED: 'QrCodeExpired',
};

/**
 * PIX transaction status
 */
export const TRANSACTION_STATUS = {
	PENDING: 'pending',
	COMPLETED: 'completed',
	FAILED: 'failed',
	EXPIRED: 'expired',
	REVERSED: 'reversed',
};

/**
 * PIX transaction types
 */
export const TRANSACTION_TYPES = {
	PIX_IN: 'pix_in',
	PIX_OUT: 'pix_out',
	PIX_IN_REVERSAL: 'pix_in_reversal',
	PIX_OUT_REVERSAL: 'pix_out_reversal',
	PIX_IN_UNKNOWN: 'pix_in_unknown',
	PIX_OUT_UNKNOWN: 'pix_out_unknown',
	PIX_IN_REVERSAL_UNKNOWN: 'pix_in_reversal_unknown',
	PIX_OUT_REVERSAL_UNKNOWN: 'pix_out_reversal_unknown',
};

/**
 * Helper function to create a properly formatted PixEvent
 * @param event The event data
 * @returns A properly formatted PixEvent
 */
export function createPixEvent(
	event: Omit<PixEvent, 'event_type'> & { event_type: string }
): PixEvent {
	// Simply return the event as is, since we no longer need NSQ attribute formatting
	return {
		...event,
		event_type: event.event_type,
	};
}
