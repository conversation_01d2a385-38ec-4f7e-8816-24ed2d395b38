import { api, APIError, ErrC<PERSON>, Header } from 'encore.dev/api';
import { generateTxId, isValidTxId, callFlow2Pay } from './flow2pay';
import { db } from './db';
import { API_PERMISSIONS, validateAPIKey } from './auth';
import log from 'encore.dev/log';

/**
 * Endpoint to test transaction ID generation and validation
 * This is useful for debugging issues with Flow2Pay integration
 */
export const testTransactionIds = api(
	{ method: 'GET', path: '/internal/diagnostics/txids', expose: false },
	async (): Promise<{
		results: Array<{
			txid: string;
			length: number;
			isValid: boolean;
		}>;
		summary: {
			totalGenerated: number;
			validCount: number;
			invalidCount: number;
		};
	}> => {
		const results = [];
		const count = 10; // Generate multiple IDs for testing

		// Generate and validate multiple transaction IDs
		for (let i = 0; i < count; i++) {
			const txid = generateTxId();
			const isValid = isValidTxId(txid);
			results.push({
				txid,
				length: txid.length,
				isValid,
			});
		}

		// Calculate summary statistics
		const validCount = results.filter((r) => r.isValid).length;

		return {
			results,
			summary: {
				totalGenerated: count,
				validCount,
				invalidCount: count - validCount,
			},
		};
	}
);

/**
 * Endpoint to test Flow2Pay API connection
 * Tests authentication and basic API access
 */
export const testFlow2PayConnection = api(
	{
		method: 'GET',
		path: '/internal/diagnostics/flow2pay-connection',
		expose: false,
	},
	async (params: {
		user_id: number;
	}): Promise<{
		status: 'success' | 'failure';
		message: string;
		details?: any;
	}> => {
		const { user_id } = params;

		try {
			// Get user's payment partner information
			const user = await db.queryRow`
        SELECT partner_id
        FROM users
        WHERE id = ${user_id}
      `;

			if (!user) {
				throw APIError.notFound('User not found');
			}

			// Test API connection by calling a simple endpoint
			const result = await callFlow2Pay<any>('/transacao/v1/saldo', 'GET');

			return {
				status: 'success',
				message: 'Successfully connected to Flow2Pay API',
				details: {
					balance: result.saldo / 100,
					currency: result.moeda || 'BRL',
				},
			};
		} catch (error) {
			console.error('Flow2Pay connection test failed:', error);

			return {
				status: 'failure',
				message: error instanceof Error ? error.message : 'Unknown error',
				details: error,
			};
		}
	}
);

/**
 * Direct passthrough to Flow2Pay QR code generation API
 * This endpoint is used for testing and debugging Flow2Pay integration
 */
export const qrcodePassthrough = api(
	{ method: 'POST', path: '/qrcode-passthrough', expose: true },
	async (params: {
		api_key: Header<'X-API-Key'>;
		txId: string;
		valor: number;
		tempoExpiracao?: number;
		informacaoAdicional?: string;
		comImagem?: boolean;
		numeroDocumento?: string;
	}): Promise<{
		success: boolean;
		txid: string;
		qr_code_text: string;
		qr_code_image?: string;
		flow2pay_response: any;
	}> => {
		const logger = log.with({ action: 'qrcode_passthrough' });

		let {
			api_key,
			txId,
			valor,
			tempoExpiracao = 3600,
			informacaoAdicional = 'PIX payment',
			comImagem = true,
			numeroDocumento,
		} = params;

		try {
			// Validate API key
			await validateAPIKey(
				api_key,
				API_PERMISSIONS.GENERATE,
				'/qrcode-passthrough'
			);

			// Validate txId
			if (!isValidTxId(txId)) {
				throw APIError.invalidArgument(
					'Invalid transaction ID format. Must be 25-35 alphanumeric characters [a-zA-Z0-9].'
				);
			}

			// Prepare the request body for Flow2Pay
			const requestBody: {
				txId: string;
				valor: number;
				tempoExpiracao: number;
				informacaoAdicional: string;
				comImagem: boolean;
				numeroDocumento?: string;
			} = {
				txId,
				valor,
				tempoExpiracao,
				informacaoAdicional,
				comImagem,
			};

			// Add numeroDocumento if provided
			if (numeroDocumento) {
				requestBody.numeroDocumento = numeroDocumento;
				logger.debug('Including numeroDocumento in request', {
					numeroDocumento,
				});
			}

			logger.info('Calling Flow2Pay QR code generation API directly', {
				txId,
				valor,
			});

			// Log the request details
			logger.debug('Flow2Pay QR code passthrough request', {
				request_body: requestBody,
			});

			// Call Flow2Pay API to generate QR code
			const response = await callFlow2Pay<any>(
				'/qrcode/v2/gerar',
				'POST',
				requestBody
			);

			// Log the raw response for debugging
			logger.debug('Flow2Pay QR code passthrough raw response', {
				txId,
				response_keys: Object.keys(response),
				imagemQrcode_type: typeof response.imagemQrcode,
				imagemQrcode_is_null: response.imagemQrcode === null,
				imagemQrcode_is_undefined: response.imagemQrcode === undefined,
				qrcode_type: typeof response.qrcode,
				qrcode_length: response.qrcode ? response.qrcode.length : 0,
			});

			// Ensure the QR code image is a string if it exists
			let qrCodeImage: string | undefined;

			// First, check if imagemQrcode exists
			if (
				response.imagemQrcode === undefined ||
				response.imagemQrcode === null
			) {
				logger.warn('Flow2Pay returned null or undefined imagemQrcode', {
					txId,
				});
				qrCodeImage = undefined;
			} else if (typeof response.imagemQrcode === 'string') {
				// If it's already a string, use it directly
				qrCodeImage = response.imagemQrcode;
				logger.debug('Using string imagemQrcode directly', {
					txId,
					length: qrCodeImage ? qrCodeImage.length : 0,
				});
			} else if (typeof response.imagemQrcode === 'object') {
				try {
					// Try to convert the object to a JSON string
					const stringified = JSON.stringify(response.imagemQrcode);
					qrCodeImage = stringified;

					logger.debug('Converted object imagemQrcode to JSON string', {
						txId,
						original_type: typeof response.imagemQrcode,
						is_array: Array.isArray(response.imagemQrcode),
						object_keys: Object.keys(response.imagemQrcode),
						converted_length: stringified.length,
						sample:
							stringified.substring(0, 100) +
							(stringified.length > 100 ? '...' : ''),
					});
				} catch (e) {
					// If JSON conversion fails, log the error and use a placeholder
					logger.error('Failed to convert imagemQrcode object to string', {
						txId,
						error: e instanceof Error ? e.message : 'Unknown error',
						object_type: Object.prototype.toString.call(response.imagemQrcode),
					});
					qrCodeImage = undefined;
				}
			} else {
				// For any other type, convert to string
				try {
					qrCodeImage = String(response.imagemQrcode);
					logger.warn(
						'Converted non-object, non-string imagemQrcode to string',
						{
							txId,
							original_type: typeof response.imagemQrcode,
							converted_value: qrCodeImage,
						}
					);
				} catch (e) {
					logger.error('Failed to convert imagemQrcode to string', {
						txId,
						error: e instanceof Error ? e.message : 'Unknown error',
					});
					qrCodeImage = undefined;
				}
			}

			logger.info('Flow2Pay QR code generation successful', { txId });

			// Return the QR code information without storing in database
			return {
				success: true,
				txid: txId,
				qr_code_text: response.qrcode,
				qr_code_image: qrCodeImage,
				flow2pay_response: response,
			};
		} catch (error) {
			// Log detailed error information
			logger.error('Flow2Pay QR code generation failed', {
				error: error instanceof Error ? error.message : 'Unknown error',
				txId,
				valor,
			});

			// Re-throw the error
			throw error;
		}
	}
);

/**
 * Test QR code generation with diagnostic information
 */
export const testQrCodeGeneration = api(
	{ method: 'POST', path: '/internal/diagnostics/qrcode', expose: false },
	async (params: {
		user_id: number;
		amount: number;
		description?: string;
	}): Promise<{
		status: 'success' | 'failure';
		message: string;
		txid?: string;
		details?: any;
	}> => {
		const { user_id, amount, description = 'Test QR Code' } = params;

		try {
			// Get user's payment partner information
			const user = await db.queryRow`
        SELECT partner_id
        FROM users
        WHERE id = ${user_id}
      `;

			if (!user) {
				throw APIError.notFound('User not found');
			}

			// Generate test transaction ID
			const txid = generateTxId();
			console.log(
				`Test QR code with txId: ${txid} (length: ${
					txid.length
				}, valid: ${isValidTxId(txid)})`
			);

			// Convert amount to centavos
			const amountInCents = Math.round(amount * 100);

			// Call Flow2Pay API to generate QR code
			const data = await callFlow2Pay<any>('/qrcode/v2/gerar', 'POST', {
				txId: txid,
				valor: amountInCents,
				tempoExpiracao: 3600,
				informacaoAdicional: description,
				comImagem: true,
			});

			return {
				status: 'success',
				message: 'Successfully generated QR code',
				txid,
				details: {
					qr_code_length: data.qrcode ? data.qrcode.length : 0,
					image_length: data.imagemQrcode ? data.imagemQrcode.length : 0,
					response: data,
				},
			};
		} catch (error) {
			console.error('QR code test generation failed:', error);

			return {
				status: 'failure',
				message: error instanceof Error ? error.message : 'Unknown error',
				details: error,
			};
		}
	}
);
