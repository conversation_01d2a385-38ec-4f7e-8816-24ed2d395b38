import { secret } from 'encore.dev/config';
import { APIError, ErrCode } from 'encore.dev/api';
import log from 'encore.dev/log';
import crypto from 'crypto';

// Flow2Pay API configuration
const FLOW2PAY_API_URL = 'https://pixv2.flow2pay.com.br';
const FLOW2PAY_CLIENT_ID = secret('FLOW2PAY_CLIENT_ID');
const FLOW2PAY_CLIENT_SECRET = secret('FLOW2PAY_CLIENT_SECRET');
export const FLOW2PAY_EVENT_TOKEN = secret('FLOW2PAY_EVENT_TOKEN');

// Authentication cache
let accessToken: string | null = null;
let tokenExpiry: number = 0;

// Partner definitions
export const PARTNERS = {
	FLOW2PAY: 1,
};

/**
 * Validates that a transaction ID meets Flow2Pay's requirements
 * @param txid Transaction ID to validate
 * @returns True if valid, false otherwise
 */
export function isValidTxId(txid: string): boolean {
	const logger = log.with({ action: 'validate_txid', txid });

	// Flow2Pay requires txids to be 25-35 alphanumeric characters
	// Accept both uppercase and lowercase letters for better compatibility
	// Note: We've adjusted the minimum length to 25 to accommodate CUID format
	const validRegex = /^[a-zA-Z0-9]{25,35}$/;
	const isValid = validRegex.test(txid);

	if (!isValid) {
		// Provide more detailed diagnostics about why validation failed
		const length = txid ? txid.length : 0;
		const hasInvalidChars = txid ? /[^a-zA-Z0-9]/.test(txid) : false;

		logger.warn('Invalid txId format', {
			txid,
			length,
			required_length: '25-35',
			required_format: 'alphanumeric [a-zA-Z0-9]',
			length_issue: length < 25 ? 'too_short' : length > 35 ? 'too_long' : null,
			has_invalid_chars: hasInvalidChars,
			endpoint: 'getQRCode',
		});
	}

	return isValid;
}

/**
 * Generate a unique transaction ID that complies with Flow2Pay requirements
 * Transaction IDs must be between 26-35 alphanumeric characters
 * Flow2Pay example format uses lowercase letters and numbers only
 */
export function generateTxId(): string {
	const logger = log.with({ action: 'generate_txid' });

	// Generate a random string of 16 characters (increased from 13 to ensure minimum length)
	const randomBytes = crypto.randomBytes(16).toString('hex');

	// Add a timestamp prefix to ensure uniqueness
	const timestamp = Date.now().toString(36);

	// Combine and ensure it's lowercase and within the required length
	// Ensure minimum length of 26 characters by padding if necessary
	let txid = (timestamp + randomBytes).toLowerCase();

	// Trim to maximum length if needed
	if (txid.length > 35) {
		txid = txid.substring(0, 35);
	}

	// Ensure minimum length by padding if needed
	if (txid.length < 26) {
		// Add padding with random lowercase letters and numbers
		const padding = 'abcdefghijklmnopqrstuvwxyz0123456789';
		while (txid.length < 26) {
			txid += padding.charAt(Math.floor(Math.random() * padding.length));
		}
		logger.info('Padded txid to meet minimum length', {
			original_length: (timestamp + randomBytes).length,
			padded_length: txid.length,
		});
	}

	// Validate the generated txid
	if (!isValidTxId(txid)) {
		logger.error('Generated invalid txid', { txid });
		// Fallback to a guaranteed valid format
		return 'a'.repeat(30);
	}

	logger.info('Generated txid', { txid, length: txid.length });
	return txid;
}

/**
 * Get a valid Flow2Pay access token, refreshing if necessary
 */
async function getAccessToken(retryCount: number = 2): Promise<string> {
	const logger = log.with({ action: 'get_flow2pay_auth_token' });
	const now = Date.now();

	// If token exists and is not expired (with 5 minute buffer)
	if (accessToken && tokenExpiry > now + 300000) {
		return accessToken;
	}

	let lastError: Error | null = null;

	for (let attempt = 0; attempt <= retryCount; attempt++) {
		try {
			logger.info('Getting new Flow2Pay authentication token', {
				attempt: attempt + 1,
				max_attempts: retryCount + 1,
			});

			const controller = new AbortController();
			const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

			try {
				const response = await fetch(
					`${FLOW2PAY_API_URL}/no-auth/autenticacao/v1/api/login`,
					{
						method: 'POST',
						headers: {
							'Content-Type': 'application/json',
						},
						body: JSON.stringify({
							clientId: FLOW2PAY_CLIENT_ID(),
							clientSecret: FLOW2PAY_CLIENT_SECRET(),
						}),
						signal: controller.signal,
					}
				);

				clearTimeout(timeoutId);

				if (!response.ok) {
					const errorText = await response.text();
					logger.error('Flow2Pay authentication failed', {
						status: response.status,
						error: errorText,
						attempt: attempt + 1,
					});

					// Determine if we should retry based on status code
					if (
						attempt < retryCount &&
						(response.status >= 500 || response.status === 429)
					) {
						// Exponential backoff
						const delay = Math.min(100 * Math.pow(2, attempt), 2000);
						logger.info(`Retrying Flow2Pay authentication`, {
							delay_ms: delay,
							attempt: attempt + 1,
							max_attempts: retryCount + 1,
						});
						await new Promise((resolve) => setTimeout(resolve, delay));
						continue;
					}

					throw new APIError(
						ErrCode.Unavailable,
						`Failed to authenticate with Flow2Pay: ${response.status} ${errorText}`
					);
				}

				const data = await response.json();

				if (!data.accessToken) {
					logger.error('Flow2Pay authentication response missing token', {
						data,
					});
					throw new APIError(
						ErrCode.Internal,
						'Invalid authentication response from Flow2Pay'
					);
				}

				// Store token and set expiry (token valid for 24 hours)
				accessToken = data.accessToken;
				tokenExpiry = now + data.expiresIn * 1000;

				logger.info('Flow2Pay authentication successful');
				return data.accessToken;
			} finally {
				clearTimeout(timeoutId);
			}
		} catch (error) {
			lastError = error instanceof Error ? error : new Error(String(error));

			// Check if this was a timeout
			const isTimeout =
				error instanceof Error &&
				(error.name === 'AbortError' || error.message.includes('timeout'));

			logger.error('Flow2Pay authentication error', {
				error: lastError.message,
				attempt: attempt + 1,
				is_timeout: isTimeout,
			});

			// Determine if we should retry
			if (attempt < retryCount) {
				// Exponential backoff
				const delay = Math.min(100 * Math.pow(2, attempt), 2000);
				logger.info(`Retrying Flow2Pay authentication`, {
					delay_ms: delay,
					attempt: attempt + 1,
					max_attempts: retryCount + 1,
				});
				await new Promise((resolve) => setTimeout(resolve, delay));
				continue;
			}
		}
	}

	// If we got here, all retries failed
	throw (
		lastError ||
		new Error('Failed to authenticate with Flow2Pay after multiple attempts')
	);
}

/**
 * Make an authenticated call to the Flow2Pay API with retries
 * @param endpoint API endpoint path
 * @param method HTTP method
 * @param body Request body (for POST/PUT)
 * @param retryCount Maximum number of retry attempts
 * @returns API response
 */
export async function callFlow2Pay<T>(
	endpoint: string,
	method: string,
	body?: any,
	retryCount: number = 2
): Promise<T> {
	const logger = log.with({
		action: 'flow2pay_api_call',
		endpoint,
		method,
	});

	let lastError: Error | null = null;

	// Log request details
	logger.info('Flow2Pay API request', {
		url: `${FLOW2PAY_API_URL}${endpoint}`,
		method,
		has_body: !!body,
	});

	if (body) {
		// Safely log the request body without sensitive data
		const sanitizedBody = { ...body };
		logger.debug('Flow2Pay request details', {
			body: sanitizedBody,
		});
	}

	for (let attempt = 0; attempt <= retryCount; attempt++) {
		try {
			// Get authentication token with retries
			const token = await getAccessToken(1); // Use 1 retry for token
			const eventToken = FLOW2PAY_EVENT_TOKEN();

			const headers: Record<string, string> = {
				Authorization: `Bearer ${token}`,
				Token: eventToken,
				'Content-Type': 'application/json',
			};

			const options: RequestInit = {
				method,
				headers,
			};

			if (body && (method === 'POST' || method === 'PUT')) {
				options.body = JSON.stringify(body);
			}

			const url = `${FLOW2PAY_API_URL}${endpoint}`;

			// Set up timeout for the request
			const controller = new AbortController();
			const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

			try {
				options.signal = controller.signal;
				const response = await fetch(url, options);

				clearTimeout(timeoutId);

				if (!response.ok) {
					let errorData;
					try {
						errorData = await response.json();
					} catch (e) {
						errorData = await response.text();
					}

					logger.error('Flow2Pay API error', {
						status: response.status,
						url,
						method,
						error: errorData,
					});

					// Handle specific error cases
					if (response.status === 401) {
						// Clear the cached token on authentication errors
						accessToken = null;
						throw new APIError(
							ErrCode.Unauthenticated,
							'Authentication failed with Flow2Pay'
						);
					}

					// Extract error message for better error reporting
					const errorMessage =
						typeof errorData === 'object' && errorData.mensagem
							? errorData.mensagem
							: typeof errorData === 'string'
							? errorData
							: response.statusText;

					throw new APIError(
						ErrCode.Internal,
						`Flow2Pay API error: ${errorMessage}`
					);
				}

				const data = await response.json();

				// Check for business errors
				if (!data.sucesso && data.mensagem) {
					logger.warn('Flow2Pay business error', {
						message: data.mensagem,
						data,
					});
					throw new APIError(
						ErrCode.FailedPrecondition,
						`Flow2Pay business error: ${data.mensagem}`
					);
				}

				logger.info('Flow2Pay API call successful');
				return data as T;
			} finally {
				clearTimeout(timeoutId);
			}
		} catch (error) {
			lastError = error instanceof Error ? error : new Error(String(error));

			// Check if this was a timeout
			const isTimeout =
				error instanceof Error &&
				(error.name === 'AbortError' || error.message.includes('timeout'));

			logger.error('Flow2Pay API error', {
				attempt: attempt + 1,
				max_attempts: retryCount + 1,
				error: lastError.message,
				is_timeout: isTimeout,
			});

			// Determine if we should retry based on the error
			const shouldRetry =
				attempt < retryCount &&
				// Network errors, timeouts, or server errors (5xx)
				(error instanceof TypeError ||
					isTimeout ||
					(error instanceof APIError && error.code === ErrCode.Internal));

			if (shouldRetry) {
				// Exponential backoff
				const delay = Math.min(100 * Math.pow(2, attempt), 2000);
				logger.info(`Retrying Flow2Pay API call`, {
					delay_ms: delay,
					attempt: attempt + 1,
					max_attempts: retryCount + 1,
				});
				await new Promise((resolve) => setTimeout(resolve, delay));
				continue;
			}

			throw lastError;
		}
	}

	// This should never happen due to the throw in the catch block,
	// but TypeScript requires a return statement
	throw (
		lastError ||
		new APIError(ErrCode.Internal, 'Unknown error in Flow2Pay API call')
	);
}

/**
 * Convert amount to centavos (cents) for Flow2Pay API
 * @param amount Amount in BRL (e.g., 10.50)
 * @returns Amount in centavos (e.g., 1050)
 */
export function toCentavos(amount: number): number {
	return Math.round(amount * 100);
}

/**
 * Convert centavos (cents) to BRL amount
 * @param centavos Amount in centavos (e.g., 1050)
 * @returns Amount in BRL (e.g., 10.50)
 */
export function fromCentavos(centavos: number): number {
	return centavos / 100;
}
