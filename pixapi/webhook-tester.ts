import { api, <PERSON><PERSON><PERSON><PERSON>, Header } from 'encore.dev/api';
import { validateAP<PERSON><PERSON><PERSON>, API_PERMISSIONS } from './auth';
import log from 'encore.dev/log';
import { Flow2PayWebhookPayload, processWebhook } from './webhooks';
import { forwardToPluggou, trackEventProcessing } from './webhook-forwarder';
import { mapToSvixEventType, sendSvixMessage } from './svix';
import { operations } from './db';
import { TRANSACTION_TYPES, TRANSACTION_STATUS } from './events';

/**
 * Interface for webhook test results
 */
interface WebhookTestResults {
  success: boolean;
  flow2pay_receipt: {
    success: boolean;
    event_type?: string;
    payload?: any;
    error?: string;
  };
  svix_forwarding: {
    success: boolean;
    message_id?: string;
    error?: string;
  };
  db_processing: {
    success: boolean;
    transaction_id?: string;
    error?: string;
  };
  pluggou_delivery: {
    success: boolean;
    status_code?: number;
    response?: string;
    error?: string;
  };
  delivery_log?: {
    id?: string;
    status?: string;
    error?: string;
  };
}

/**
 * Generate a random ID for test webhooks
 */
function generateRandomId(): string {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  // Generate a random ID between 26 and 35 characters
  const length = Math.floor(Math.random() * 10) + 26;
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * Endpoint to test webhook delivery path
 */
export const testWebhookDelivery = api(
  { method: 'POST', path: '/test-webhook-delivery', expose: true },
  async (params: {
    api_key: Header<'X-API-Key'>;
    event_type: 'PixIn' | 'PixOut' | 'PixInReversal' | 'PixOutReversalExternal';
    status?: string;
    valor?: number;
    txid?: string;
    idEnvio?: string;
    endToEndId?: string;
    user_id?: string;
    skip_svix?: boolean;
    skip_pluggou?: boolean;
    direct_delivery?: boolean;
  }): Promise<WebhookTestResults> => {
    const logger = log.with({ action: 'test_webhook_delivery' });
    logger.info('Starting webhook delivery test');

    const results: WebhookTestResults = {
      success: false,
      flow2pay_receipt: { success: false },
      svix_forwarding: { success: false },
      db_processing: { success: false },
      pluggou_delivery: { success: false }
    };

    try {
      // Validate API key
      await validateAPIKey(
        params.api_key,
        API_PERMISSIONS.ALL,
        '/test-webhook-delivery'
      );

      // Default values
      const status = params.status || 'Sucesso';
      const valor = params.valor || Math.floor(Math.random() * 10000) + 100; // Random amount between 1 and 100 reais
      const user_id = params.user_id || '1'; // Default to first user

      // Generate IDs if not provided
      const txid = params.event_type === 'PixIn'
        ? (params.txid || generateRandomId())
        : undefined;

      const idEnvio = params.event_type === 'PixOut'
        ? (params.idEnvio || generateRandomId())
        : undefined;

      const endToEndId = params.endToEndId || `E${Math.floor(Math.random() * 1000000000000)}`;

      // Generate a Flow2Pay webhook payload
      const payload: Flow2PayWebhookPayload = {
        evento: params.event_type,
        status: status,
        valor: valor,
        horario: new Date().toISOString(),
        txid,
        idEnvio,
        endToEndId,
      };

      // Add additional fields based on event type
      if (params.event_type === 'PixIn') {
        payload.pagador = {
          nome: 'Cliente de Teste',
          chave: '<EMAIL>',
          tipoConta: 'CACC',
          cpf: '12345678901',
        };
      } else if (params.event_type === 'PixOut') {
        payload.recebedor = {
          nome: 'Destinatário de Teste',
          codigoBanco: '260',
          cpf_cnpj: '12345678901',
        };
      }

      // Find related transaction or QR code if possible
      let foundUserId = user_id;
      let existingTransaction;
      let existingQrCode;

      try {
        // For PixIn events, look for QR code
        if (params.event_type === 'PixIn' && txid) {
          existingQrCode = await operations.qrCodeOperations.getByTxid(txid);
          if (existingQrCode) {
            foundUserId = existingQrCode.userId;
            logger.info('Found matching QR code', { txid, user_id: foundUserId });
          }
        }

        // For PixOut events, look for transaction
        if (params.event_type === 'PixOut' && idEnvio) {
          existingTransaction = await operations.transactionOperations.getByIdEnvio(idEnvio);
          if (existingTransaction) {
            foundUserId = existingTransaction.userId;
            logger.info('Found matching transaction', { id_envio: idEnvio, user_id: foundUserId });
          }
        }

        // For reversal events, look for original transaction
        if ((params.event_type === 'PixInReversal' || params.event_type === 'PixOutReversalExternal')
            && endToEndId) {
          existingTransaction = await operations.transactionOperations.getByEndToEndId(endToEndId);
          if (existingTransaction) {
            foundUserId = existingTransaction.userId || user_id; // Default to user_id if null
            logger.info('Found original transaction for reversal', {
              end_to_end_id: endToEndId,
              user_id: foundUserId
            });
          }
        }
      } catch (findError) {
        logger.warn('Error finding related records', {
          error: findError instanceof Error ? findError.message : 'Unknown error'
        });
      }

      const idField = idEnvio || txid;

      // Step 1: Simulate Flow2Pay webhook receipt
      logger.info('Simulating Flow2Pay webhook receipt', {
        event_type: params.event_type,
        id_field: idField,
        end_to_end_id: endToEndId
      });

      // Store the webhook in the database
      try {
        await operations.webhookOperations.storeFlow2PayWebhook({
          eventType: payload.evento,
          txid: params.event_type === 'PixIn' ? idField : undefined,
          idEnvio: params.event_type === 'PixOut' ? idField : undefined,
          endToEndId: endToEndId,
          valor: valor,
          status: status,
          payload: payload,
        });

        results.flow2pay_receipt.success = true;
        results.flow2pay_receipt.event_type = payload.evento;
        results.flow2pay_receipt.payload = payload;

        logger.info('Successfully stored webhook in database');
      } catch (storeError) {
        results.flow2pay_receipt.error = storeError instanceof Error
          ? storeError.message
          : 'Unknown error';

        logger.error('Error storing webhook in database', {
          error: results.flow2pay_receipt.error
        });
      }

      // Step 2: Send to Svix (skipped if skip_svix is true)
      if (!params.skip_svix) {
        try {
          // Map Flow2Pay event type to Svix event type
          const svixEventType = mapToSvixEventType(payload.evento, payload.status);

          // Prepare the payload for Svix
          const svixPayload = {
            event_type: payload.evento,
            flow2pay_event_type: payload.evento,
            svix_event_type: svixEventType,
            user_id: foundUserId,
            amount: Math.abs(payload.valor),
            flow2pay_id: idField,
            end_to_end_id: payload.endToEndId,
            webhook_payload: payload,
            timestamp: payload.horario,
            status: payload.status,
            event_id: `test-${payload.evento}-${idField || payload.endToEndId || Date.now()}`,
          };

          // Send the message to Svix
          const messageId = await sendSvixMessage(
            svixEventType,
            svixPayload,
            foundUserId
          );

          results.svix_forwarding.success = true;
          results.svix_forwarding.message_id = messageId;

          logger.info('Successfully sent webhook to Svix', {
            message_id: messageId,
            user_id: foundUserId
          });
        } catch (svixError) {
          results.svix_forwarding.error = svixError instanceof Error
            ? svixError.message
            : 'Unknown error';

          logger.error('Error sending webhook to Svix', {
            error: results.svix_forwarding.error
          });
        }
      } else {
        results.svix_forwarding.success = true;
        results.svix_forwarding.message_id = 'skipped';
        logger.info('Skipping Svix forwarding as requested');
      }

      // Step 3: Process the webhook in our database
      try {
        // Process the webhook
        await processWebhook(payload);

        // Try to find the transaction that should have been created/updated
        let transactionId: string | undefined;

        if (params.event_type === 'PixIn' && txid) {
          const transaction = await operations.transactionOperations.getByTxid(txid);
          if (transaction) {
            transactionId = transaction.id;
          }
        } else if (params.event_type === 'PixOut' && idEnvio) {
          const transaction = await operations.transactionOperations.getByIdEnvio(idEnvio);
          if (transaction) {
            transactionId = transaction.id;
          }
        } else if ((params.event_type === 'PixInReversal' || params.event_type === 'PixOutReversalExternal')
                  && endToEndId) {
          // For reversals, we need to find transactions with this endToEndId
          // This is a more direct approach since we don't have a listByEndToEndId method
          try {
            // First, try to find the original transaction
            const originalTransaction = await operations.transactionOperations.getByEndToEndId(endToEndId);

            // Then look for a reversal transaction that might have been created
            // This is a simplification - in a real implementation, you would need more logic here
            if (originalTransaction) {
              // For now, just use the original transaction ID as a placeholder
              // In a real scenario, you would query for reversal transactions
              transactionId = originalTransaction.id;
            }
          } catch (reversalLookupError) {
            logger.warn('Error looking up reversal transaction', {
              error: reversalLookupError instanceof Error ? reversalLookupError.message : 'Unknown error',
              end_to_end_id: endToEndId
            });
          }
        }

        results.db_processing.success = true;
        results.db_processing.transaction_id = transactionId;

        logger.info('Successfully processed webhook', {
          transaction_id: transactionId
        });
      } catch (processError) {
        results.db_processing.error = processError instanceof Error
          ? processError.message
          : 'Unknown error';

        logger.error('Error processing webhook', {
          error: results.db_processing.error
        });
      }

      // Step 4: Direct delivery to app.pluggou.io (if requested or if Svix is skipped)
      if (params.direct_delivery || params.skip_svix) {
        try {
          if (!params.skip_pluggou) {
            // Create a PixEvent from the webhook payload
            const pixEvent = {
              event_type: payload.evento,
              user_id: foundUserId,
              amount: Math.abs(payload.valor),
              flow2pay_id: idField,
              end_to_end_id: endToEndId,
              webhook_payload: payload,
              timestamp: payload.horario,
              status: status,
              event_id: `direct-test-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`,
              published_at: new Date().toISOString(),
              // Convert transaction_id to number if it exists, otherwise undefined
              transaction_id: results.db_processing.transaction_id
                ? Number(results.db_processing.transaction_id)
                : undefined
            };

            // Track the event as received
            await trackEventProcessing(pixEvent, 'received');

            // Forward to app.pluggou.io
            await forwardToPluggou(pixEvent);

            results.pluggou_delivery.success = true;
            logger.info('Successfully forwarded webhook directly to app.pluggou.io');

            // Track the event as completed
            await trackEventProcessing(pixEvent, 'completed');
          } else {
            results.pluggou_delivery.success = true;
            results.pluggou_delivery.status_code = 200;
            results.pluggou_delivery.response = "Skipped as requested";
            logger.info('Skipping Pluggou delivery as requested');
          }
        } catch (deliveryError) {
          results.pluggou_delivery.error = deliveryError instanceof Error
            ? deliveryError.message
            : 'Unknown error';

          logger.error('Error forwarding webhook to app.pluggou.io', {
            error: results.pluggou_delivery.error
          });
        }
      } else {
        results.pluggou_delivery.success = true;
        results.pluggou_delivery.status_code = 200;
        results.pluggou_delivery.response = "Handled via Svix";
        logger.info('Pluggou delivery handled via Svix');
      }

      // Overall success is determined by all the steps that weren't skipped
      results.success = results.flow2pay_receipt.success &&
        (params.skip_svix || results.svix_forwarding.success) &&
        results.db_processing.success &&
        ((params.direct_delivery || params.skip_svix) ? results.pluggou_delivery.success : true);

      logger.info('Webhook delivery test completed', {
        success: results.success,
        flow2pay_success: results.flow2pay_receipt.success,
        svix_success: results.svix_forwarding.success,
        db_success: results.db_processing.success,
        pluggou_success: results.pluggou_delivery.success
      });

      return results;
    } catch (error) {
      logger.error('Error in webhook delivery test', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      // Update the overall result
      results.success = false;

      throw error;
    }
  }
);

/**
 * Command-line webhook tester that can be run directly
 */
export const runWebhookTest = api(
  { method: 'POST', path: '/run-webhook-test', expose: true },
  async (params: {
    api_key: Header<'X-API-Key'>;
    event_type?: 'PixIn' | 'PixOut' | 'PixInReversal' | 'PixOutReversalExternal';
    status?: string;
    skip_svix?: boolean;
    direct_delivery?: boolean;
  }): Promise<{
    results: WebhookTestResults;
    diagnostics: {
      db_connection: boolean;
      svix_available: boolean;
      pluggou_reachable: boolean;
      error_details?: string;
    };
    recommendations: string[];
  }> => {
    const logger = log.with({ action: 'run_webhook_test' });
    logger.info('Running webhook diagnostic test');

    const diagnostics = {
      db_connection: false,
      svix_available: false,
      pluggou_reachable: false,
      error_details: ''
    };

    const recommendations: string[] = [];

    // Default to PixIn if not specified
    const eventType = params.event_type || 'PixIn';

    try {
      // 1. Test database connection
      try {
        // Simplified database connection test
        const testUser = await operations.userOperations.getById('1');
        diagnostics.db_connection = !!testUser;
        logger.info('Database connection test', {
          success: diagnostics.db_connection,
          user_found: !!testUser
        });
      } catch (dbError) {
        diagnostics.db_connection = false;
        diagnostics.error_details = dbError instanceof Error
          ? dbError.message
          : 'Unknown database error';

        logger.error('Database connection test failed', {
          error: diagnostics.error_details
        });

        recommendations.push('Database connection issue detected. Please check database configuration and connectivity.');
      }

      // 2. Test Svix connection (unless skipped)
      if (!params.skip_svix) {
        try {
          // Test Svix by sending a simple test message
          const testMessageId = await sendSvixMessage(
            'WebhookTest',
            { test: true, timestamp: new Date().toISOString() },
            '1'
          );

          diagnostics.svix_available = !!testMessageId;
          logger.info('Svix connection test', {
            success: diagnostics.svix_available,
            message_id: testMessageId
          });
        } catch (svixError) {
          diagnostics.svix_available = false;
          const errorMessage = svixError instanceof Error
            ? svixError.message
            : 'Unknown Svix error';

          diagnostics.error_details += '\nSvix error: ' + errorMessage;

          logger.error('Svix connection test failed', {
            error: errorMessage
          });

          recommendations.push('Svix connection issue detected. Check Svix configuration and API keys.');
        }
      } else {
        diagnostics.svix_available = true;
        logger.info('Skipping Svix connection test as requested');
      }

      // 3. Test Pluggou reachability
      try {
        const pluggouResponse = await fetch('https://app.pluggou.io/api/webhooks/pluggou-pix', {
          method: 'HEAD'
        });

        diagnostics.pluggou_reachable = pluggouResponse.ok;
        logger.info('Pluggou endpoint test', {
          status: pluggouResponse.status,
          success: diagnostics.pluggou_reachable
        });

        if (!pluggouResponse.ok) {
          recommendations.push(`Pluggou endpoint returned status ${pluggouResponse.status}. Verify the endpoint is accessible.`);
        }
      } catch (pluggouError) {
        diagnostics.pluggou_reachable = false;
        const errorMessage = pluggouError instanceof Error
          ? pluggouError.message
          : 'Unknown Pluggou error';

        diagnostics.error_details += '\nPluggou error: ' + errorMessage;

        logger.error('Pluggou connection test failed', {
          error: errorMessage
        });

        recommendations.push('Cannot reach Pluggou endpoint. Check network connectivity and firewall settings.');
      }

      // 4. Run the actual webhook test
      const results = await testWebhookDelivery({
        api_key: params.api_key,
        event_type: eventType,
        status: params.status || 'Sucesso',
        valor: 1000, // 10 reais
        skip_svix: params.skip_svix,
        direct_delivery: params.direct_delivery
      });

      // 5. Analyze results and add recommendations
      if (!results.success) {
        if (!results.flow2pay_receipt.success) {
          recommendations.push('Failed to store Flow2Pay webhook. Check database schema and permissions.');
        }

        if (!results.svix_forwarding.success && !params.skip_svix) {
          recommendations.push('Failed to forward webhook to Svix. Check Svix configuration and API keys.');
        }

        if (!results.db_processing.success) {
          recommendations.push('Failed to process webhook in database. Check database schema and the processWebhook function.');
        }

        if (!results.pluggou_delivery.success && (params.direct_delivery || params.skip_svix)) {
          recommendations.push('Failed to deliver webhook to Pluggou. Check the forwardToPluggou function and Pluggou endpoint.');
        }
      }

      // Add general recommendations based on testing
      if (results.success && !diagnostics.db_connection) {
        recommendations.push('Webhook test succeeded despite database connection issues in diagnostics. This suggests intermittent database problems.');
      }

      if (results.success && !diagnostics.svix_available && !params.skip_svix) {
        recommendations.push('Webhook test succeeded despite Svix connection issues in diagnostics. This suggests intermittent Svix problems.');
      }

      if (results.success && !diagnostics.pluggou_reachable) {
        recommendations.push('Webhook test succeeded despite Pluggou endpoint issues in diagnostics. This suggests intermittent Pluggou connectivity problems.');
      }

      // Add specific Flow2Pay-related recommendations
      if (eventType === 'PixIn' && !results.db_processing.transaction_id) {
        recommendations.push('No transaction was created for PixIn webhook. Check the handlePixIn function.');
      }

      if (eventType === 'PixOut' && !results.db_processing.transaction_id) {
        recommendations.push('No transaction was updated for PixOut webhook. Check the handlePixOut function.');
      }

      // If no specific issues were found but the test failed
      if (!results.success && recommendations.length === 0) {
        recommendations.push('Webhook test failed but no specific issues were identified. Check the logs for more details.');
      }

      // If no recommendations were added and test was successful
      if (results.success && recommendations.length === 0) {
        recommendations.push('Webhook test completed successfully. No issues detected in the webhook delivery path.');
      }

      logger.info('Webhook diagnostic test completed', {
        success: results.success,
        recommendations_count: recommendations.length
      });

      return {
        results,
        diagnostics,
        recommendations
      };
    } catch (error) {
      logger.error('Error running webhook test', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      throw error;
    }
  }
);

/**
 * Simulate a specific webhook callback from Flow2Pay
 */
export const simulateFlow2PayWebhook = api.raw(
  { method: 'POST', path: '/simulate-flow2pay-webhook', expose: true },
  async (req, resp) => {
    const logger = log.with({ action: 'simulate_flow2pay_webhook' });
    logger.info('Simulating Flow2Pay webhook call');

    try {
      // Parse the API key from headers
      const apiKey = req.headers['x-api-key'] as string;
      if (!apiKey) {
        resp.writeHead(401, { 'Content-Type': 'application/json' });
        resp.end(JSON.stringify({
          success: false,
          error: 'Missing API key'
        }));
        return;
      }

      // Validate API key
      try {
        await validateAPIKey(
          apiKey,
          API_PERMISSIONS.ALL,
          '/simulate-flow2pay-webhook'
        );
      } catch (authError) {
        resp.writeHead(401, { 'Content-Type': 'application/json' });
        resp.end(JSON.stringify({
          success: false,
          error: 'Invalid API key'
        }));
        return;
      }

      // Parse the request body
      let body = '';
      for await (const chunk of req) {
        body += chunk.toString();
      }

      if (!body) {
        resp.writeHead(400, { 'Content-Type': 'application/json' });
        resp.end(JSON.stringify({
          success: false,
          error: 'Empty payload'
        }));
        return;
      }

      // Parse the JSON payload
      let payload;
      try {
        payload = JSON.parse(body);
      } catch (error) {
        resp.writeHead(400, { 'Content-Type': 'application/json' });
        resp.end(JSON.stringify({
          success: false,
          error: 'Invalid JSON payload'
        }));
        return;
      }

      // Validate the payload structure
      if (!payload.evento || !['PixIn', 'PixOut', 'PixInReversal', 'PixOutReversalExternal'].includes(payload.evento)) {
        resp.writeHead(400, { 'Content-Type': 'application/json' });
        resp.end(JSON.stringify({
          success: false,
          error: 'Invalid or missing evento field'
        }));
        return;
      }

      if (!payload.valor) {
        resp.writeHead(400, { 'Content-Type': 'application/json' });
        resp.end(JSON.stringify({
          success: false,
          error: 'Missing valor field'
        }));
        return;
      }

      // Ensure we have a timestamp
      if (!payload.horario) {
        payload.horario = new Date().toISOString();
      }

      // Ensure we have a status (default to 'Sucesso' for PixIn)
      if (!payload.status) {
        payload.status = payload.evento === 'PixIn' ? 'Sucesso' : undefined;

        if (!payload.status) {
          resp.writeHead(400, { 'Content-Type': 'application/json' });
          resp.end(JSON.stringify({
            success: false,
            error: 'Missing status field'
          }));
          return;
        }
      }

      // Forward to the real webhook handler
      try {
        // Store the webhook in the database
        await operations.webhookOperations.storeFlow2PayWebhook({
          eventType: payload.evento,
          txid: payload.evento === 'PixIn' ? payload.txid : undefined,
          idEnvio: payload.evento === 'PixOut' ? payload.idEnvio : undefined,
          endToEndId: payload.endToEndId,
          valor: payload.valor,
          status: payload.status,
          payload: payload,
        });

        // Process the webhook
        processWebhook(payload).catch((err) => {
          logger.error('Error processing simulated webhook', {
            error: err.message,
            event_type: payload.evento,
          });
        });

        // Return success
        resp.writeHead(200, { 'Content-Type': 'application/json' });
        resp.end(JSON.stringify({
          success: true,
          message: 'Webhook received and processing started'
        }));
      } catch (processingError) {
        logger.error('Error processing simulated webhook', {
          error: processingError instanceof Error ? processingError.message : 'Unknown error',
        });

        resp.writeHead(500, { 'Content-Type': 'application/json' });
        resp.end(JSON.stringify({
          success: false,
          error: 'Error processing webhook',
          details: processingError instanceof Error ? processingError.message : 'Unknown error'
        }));
      }
    } catch (error) {
      logger.error('Error in simulate-flow2pay-webhook', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      resp.writeHead(500, { 'Content-Type': 'application/json' });
      resp.end(JSON.stringify({
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      }));
    }
  }
);
