import { api, Header } from 'encore.dev/api';
import { validateAPIKey, API_PERMISSIONS } from './auth';
import log from 'encore.dev/log';
import { db } from './db';
import { flow2payWebhooks, webhookDeliveryLogs } from './db/schema';
import { desc, eq, and, gte, lte, or, count, sql } from 'drizzle-orm';

/**
 * Get detailed webhook statistics
 */
export const getWebhookStatistics = api(
  { method: 'GET', path: '/webhook-statistics', expose: true },
  async (params: {
    api_key: Header<'X-API-Key'>;
    start_date?: string; // ISO date string
    end_date?: string; // ISO date string
  }): Promise<{
    total_statistics: {
      total_events: number;
      processed_events: number;
      failed_events: number;
      pending_events: number;
      success_rate: number;
    };
    event_type_statistics: Record<string, {
      total: number;
      processed: number;
      failed: number;
      pending: number;
      success_rate: number;
    }>;
    delivery_statistics: {
      total_deliveries: number;
      successful_deliveries: number;
      failed_deliveries: number;
      pending_deliveries: number;
      success_rate: number;
    };
  }> => {
    const logger = log.with({ action: 'get_webhook_statistics' });

    try {
      // Validate API key
      await validateAPIKey(params.api_key, API_PERMISSIONS.ALL, '/webhook-statistics');

      // Parse date range
      const startDate = params.start_date
        ? new Date(params.start_date)
        : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // Default to last 7 days

      const endDate = params.end_date
        ? new Date(params.end_date)
        : new Date(); // Default to now

      // Get all webhooks within date range
      const webhooks = await db.query.flow2payWebhooks.findMany({
        where: and(
          gte(flow2payWebhooks.createdAt, startDate),
          lte(flow2payWebhooks.createdAt, endDate)
        ),
      });

      // Get all webhook deliveries within date range
      const deliveries = await db.query.webhookDeliveryLogs.findMany({
        where: and(
          gte(webhookDeliveryLogs.createdAt, startDate),
          lte(webhookDeliveryLogs.createdAt, endDate)
        ),
      });

      // Calculate webhook statistics
      const totalEvents = webhooks.length;
      const processedEvents = webhooks.filter(wh => wh.processed).length;
      const failedEvents = webhooks.filter(wh => wh.status === 'failed').length;
      const pendingEvents = totalEvents - processedEvents;
      const successRate = totalEvents > 0
        ? Math.round((processedEvents - failedEvents) / totalEvents * 100)
        : 0;

      // Calculate delivery statistics
      const totalDeliveries = deliveries.length;
      const successfulDeliveries = deliveries.filter(d => d.status === 'success').length;
      const failedDeliveries = deliveries.filter(d => d.status === 'failed').length;
      const pendingDeliveries = totalDeliveries - (successfulDeliveries + failedDeliveries);
      const deliverySuccessRate = totalDeliveries > 0
        ? Math.round(successfulDeliveries / totalDeliveries * 100)
        : 0;

      // Group by event type
      const eventTypeStats: Record<string, {
        total: number;
        processed: number;
        failed: number;
        pending: number;
        success_rate: number;
      }> = {};

      // Group webhooks by event type
      webhooks.forEach(webhook => {
        const eventType = webhook.eventType;

        if (!eventTypeStats[eventType]) {
          eventTypeStats[eventType] = {
            total: 0,
            processed: 0,
            failed: 0,
            pending: 0,
            success_rate: 0
          };
        }

        eventTypeStats[eventType].total++;

        if (webhook.processed) {
          eventTypeStats[eventType].processed++;
          if (webhook.status === 'failed') {
            eventTypeStats[eventType].failed++;
          }
        } else {
          eventTypeStats[eventType].pending++;
        }
      });

      // Calculate success rate for each event type
      Object.keys(eventTypeStats).forEach(eventType => {
        const stats = eventTypeStats[eventType];
        stats.success_rate = stats.total > 0
          ? Math.round((stats.processed - stats.failed) / stats.total * 100)
          : 0;
      });

      return {
        total_statistics: {
          total_events: totalEvents,
          processed_events: processedEvents,
          failed_events: failedEvents,
          pending_events: pendingEvents,
          success_rate: successRate
        },
        event_type_statistics: eventTypeStats,
        delivery_statistics: {
          total_deliveries: totalDeliveries,
          successful_deliveries: successfulDeliveries,
          failed_deliveries: failedDeliveries,
          pending_deliveries: pendingDeliveries,
          success_rate: deliverySuccessRate
        }
      };
    } catch (error) {
      logger.error('Error getting webhook statistics', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }
);

/**
 * Get detailed information about a specific webhook
 */
export const getWebhookDetails = api(
  { method: 'GET', path: '/webhook/:id', expose: true },
  async (params: {
    api_key: Header<'X-API-Key'>;
    id: string;
  }): Promise<{
    webhook: {
      id: string;
      event_type: string;
      status: string;
      processed: boolean;
      created_at: string;
      processed_at?: string;
      txid?: string;
      id_envio?: string;
      end_to_end_id?: string;
      amount: number;
      error_message?: string;
    };
    deliveries: Array<{
      id: string;
      event_id: string;
      destination_url: string;
      status: string;
      status_code?: number;
      attempts: number;
      created_at: string;
      delivered_at?: string;
      error_message?: string;
    }>;
  }> => {
    const logger = log.with({ action: 'get_webhook_details', webhook_id: params.id });

    try {
      // Validate API key
      await validateAPIKey(params.api_key, API_PERMISSIONS.ALL, '/webhook/:id');

      // Get webhook details
      const webhook = await db.query.flow2payWebhooks.findFirst({
        where: eq(flow2payWebhooks.id, params.id)
      });

      if (!webhook) {
        logger.warn('Webhook not found', { id: params.id });
        throw new Error(`Webhook with ID ${params.id} not found`);
      }

      // Get all deliveries for this webhook
      const deliveries = await db.query.webhookDeliveryLogs.findMany({
        where: eq(webhookDeliveryLogs.eventId, params.id),
        orderBy: [desc(webhookDeliveryLogs.createdAt)]
      });

      // Format the response
      const formattedWebhook = {
        id: webhook.id,
        event_type: webhook.eventType,
        status: webhook.status,
        processed: webhook.processed,
        created_at: webhook.createdAt.toISOString(),
        processed_at: webhook.processedAt ? webhook.processedAt.toISOString() : undefined,
        txid: webhook.txid || undefined,
        id_envio: webhook.idEnvio || undefined,
        end_to_end_id: webhook.endToEndId || undefined,
        amount: webhook.valor,
        error_message: webhook.errorMessage || undefined
      };

      const formattedDeliveries = deliveries.map(delivery => ({
        id: delivery.id,
        event_id: delivery.eventId,
        destination_url: delivery.destinationUrl,
        status: delivery.status,
        status_code: delivery.statusCode || undefined,
        attempts: delivery.attempts,
        created_at: delivery.createdAt.toISOString(),
        delivered_at: delivery.deliveredAt ? delivery.deliveredAt.toISOString() : undefined,
        error_message: delivery.errorMessage || undefined
      }));

      return {
        webhook: formattedWebhook,
        deliveries: formattedDeliveries
      };
    } catch (error) {
      logger.error('Error getting webhook details', {
        error: error instanceof Error ? error.message : 'Unknown error',
        webhook_id: params.id
      });
      throw error;
    }
  }
);

/**
 * Retry failed webhooks based on criteria
 */
export const retryFailedWebhooks = api(
  { method: 'POST', path: '/retry-failed-webhooks', expose: true },
  async (params: {
    api_key: Header<'X-API-Key'>;
    event_type?: string;
    max_age_hours?: number;
    limit?: number;
  }): Promise<{
    retried_count: number;
    successful_retries: number;
    failed_retries: number;
    retried_webhooks: Array<{
      id: string;
      event_type: string;
      retry_success: boolean;
      error?: string;
    }>;
  }> => {
    const logger = log.with({ action: 'retry_failed_webhooks' });

    try {
      // Validate API key
      await validateAPIKey(params.api_key, API_PERMISSIONS.ALL, '/retry-failed-webhooks');

      const eventType = params.event_type;
      const maxAgeHours = params.max_age_hours || 24; // Default to 24 hours
      const limit = params.limit || 10; // Default to 10 webhooks

      // Calculate the cutoff time
      const cutoffTime = new Date(Date.now() - maxAgeHours * 60 * 60 * 1000);

      // Build the WHERE clause
      let whereClause = and(
        eq(flow2payWebhooks.status, 'failed'),
        gte(flow2payWebhooks.createdAt, cutoffTime)
      );

      // Add event type filter if provided
      if (eventType) {
        whereClause = and(whereClause, eq(flow2payWebhooks.eventType, eventType));
      }

      // Get failed webhooks based on criteria
      const failedWebhooks = await db.query.flow2payWebhooks.findMany({
        where: whereClause,
        orderBy: [desc(flow2payWebhooks.createdAt)],
        limit
      });

      const results = {
        retried_count: failedWebhooks.length,
        successful_retries: 0,
        failed_retries: 0,
        retried_webhooks: [] as Array<{
          id: string;
          event_type: string;
          retry_success: boolean;
          error?: string;
        }>
      };

      // Process each webhook
      for (const webhook of failedWebhooks) {
        try {
          // Attempt to retry the webhook
          // This would call your webhook retry logic
          const retrySuccess = await retryWebhook(webhook.id);

          results.retried_webhooks.push({
            id: webhook.id,
            event_type: webhook.eventType,
            retry_success: retrySuccess
          });

          if (retrySuccess) {
            results.successful_retries++;
          } else {
            results.failed_retries++;
          }
        } catch (error) {
          logger.error('Error retrying webhook', {
            error: error instanceof Error ? error.message : 'Unknown error',
            webhook_id: webhook.id
          });

          results.retried_webhooks.push({
            id: webhook.id,
            event_type: webhook.eventType,
            retry_success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          });

          results.failed_retries++;
        }
      }

      return results;
    } catch (error) {
      logger.error('Error in retry failed webhooks', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }
);

/**
 * Helper function to retry a webhook
 * This is a placeholder - would need to be implemented based on your webhook retry logic
 */
async function retryWebhook(webhookId: string): Promise<boolean> {
  const logger = log.with({ action: 'retry_webhook', webhook_id: webhookId });

  try {
    // Implement the webhook retry logic here
    // For now, just log and return true
    logger.info('Would retry webhook', { webhook_id: webhookId });

    // Update the webhook status
    await db.update(flow2payWebhooks)
      .set({
        status: 'processing',
        processed: false,
        processedAt: null
      })
      .where(eq(flow2payWebhooks.id, webhookId));

    return true;
  } catch (error) {
    logger.error('Error in retry webhook helper', {
      error: error instanceof Error ? error.message : 'Unknown error',
      webhook_id: webhookId
    });
    return false;
  }
}
