import { api, APIError, Header } from 'encore.dev/api';
import { db, operations } from './db';
import { generateTxId, isValidTxId, callFlow2Pay } from './flow2pay';
import { API_PERMISSIONS, validateAPIKey } from './auth';
import log from 'encore.dev/log';
import { testDatabaseConnection } from './database';
import * as crypto from 'crypto';

/**
 * Interface for QR code generation response
 */
interface QRCodeResponse {
	txid: string;
	qr_code_text: string;
	qr_code_image: string;
	expiration_time: number;
	amount: number;
	description?: string;
	created_at: string;
}

/**
 * Interface for transaction history response
 */
interface TransactionHistoryResponse {
	transactions: Array<{
		id: number;
		type: string;
		amount: number;
		status: string;
		description?: string;
		created_at: string;
		txid?: string;
		end_to_end_id?: string;
		id_envio?: string;
	}>;
	pagination: {
		total_count: number;
		page: number;
		page_size: number;
		total_pages: number;
	};
}

/**
 * Interface for balance response
 */
interface BalanceResponse {
	balance: number;
	currency: string;
	updated_at: string;
}

/**
 * Interface for PIX transfer response
 */
interface TransferResponse {
	id_envio: string;
	status: string;
	message: string;
	transaction_id?: string;
}

/**
 * Generate a QR code for receiving PIX
 */
export const generatePixQRCode = api(
	{ method: 'POST', path: '/qrcode', expose: true },
	async (params: {
		api_key: Header<'X-API-Key'>;
		user_id?: string | number; // Aceita tanto string quanto number para compatibilidade
		amount: number;
		description?: string;
		expiration_time?: number;
		ip_address?: string;
		user_agent?: string;
		numero_documento?: string; // CPF/CNPJ of the payer without special characters
	}): Promise<QRCodeResponse> => {
		const logger = log.with({ action: 'generate_qr_code' });

		let {
			api_key,
			user_id,
			amount,
			description = '',
			expiration_time = 3600,
			ip_address,
			user_agent,
			numero_documento,
		} = params;

		// Start tracking execution time for performance monitoring
		const startTime = performance.now();

		try {
			// Test database connection first
			const isDbConnected = await testDatabaseConnection();
			if (!isDbConnected) {
				logger.error('Database connection failed during QR code generation');
				throw APIError.unavailable(
					'Database connection error. Please try again later.'
				);
			}

			logger.info('Starting QR code generation', {
				user_id,
				amount,
				has_description: !!description,
				expiration_time,
			});

			// Validate API key and get the user_id if not provided
			// Add timeout handling for API key validation
			let authResult;
			try {
				const authPromise = validateAPIKey(
					api_key,
					API_PERMISSIONS.GENERATE,
					'/qrcode',
					ip_address,
					user_agent
				);

				// Set a timeout for the API key validation
				const timeoutPromise = new Promise<never>((_, reject) => {
					setTimeout(() => {
						reject(new Error('API key validation timed out after 5 seconds'));
					}, 5000);
				});

				authResult = await Promise.race([authPromise, timeoutPromise]);
			} catch (authError) {
				logger.error('API key validation error', {
					error:
						authError instanceof Error ? authError.message : 'Unknown error',
					is_timeout:
						authError instanceof Error &&
						authError.message.includes('timed out'),
				});
				throw APIError.internal(
					'Authentication service unavailable. Please try again later.'
				);
			}

			// If user_id wasn't provided, use the one from the API key
			if (!user_id) {
				// authResult.user_id já é string, podemos usar diretamente
				user_id = authResult.user_id;
				logger.info('Using user_id from API key', {
					user_id: authResult.user_id,
				});
			}
			// Se user_id for fornecido como número, converta para string para operações de banco de dados
			else if (typeof user_id === 'number') {
				user_id = String(user_id);
				logger.debug('Converted numeric user_id to string', {
					original: user_id,
				});
			}

			// Get user's payment partner information with timeout handling
			let user;
			try {
				// Garantir que user_id seja string para operações de banco de dados
				const userIdStr =
					typeof user_id === 'number' ? String(user_id) : user_id;
				const userPromise = operations.userOperations.getById(userIdStr);

				// Set a timeout for the user lookup
				const timeoutPromise = new Promise<never>((_, reject) => {
					setTimeout(() => {
						reject(new Error('User lookup timed out after 5 seconds'));
					}, 5000);
				});

				user = await Promise.race([userPromise, timeoutPromise]);
			} catch (userError) {
				logger.error('User lookup error', {
					error:
						userError instanceof Error ? userError.message : 'Unknown error',
					is_timeout:
						userError instanceof Error &&
						userError.message.includes('timed out'),
					user_id,
				});
				throw APIError.internal(
					'User service unavailable. Please try again later.'
				);
			}

			if (!user) {
				throw APIError.notFound('User not found');
			}

			// Validate amount
			if (amount <= 0) {
				throw APIError.invalidArgument('Amount must be greater than 0');
			}

			// Validate expiration time
			if (expiration_time < 60 || expiration_time > 172800) {
				throw APIError.invalidArgument(
					'Expiration time must be between 60 and 172800 seconds (48 hours)'
				);
			}

			// Generate a unique transaction ID
			const txid = generateTxId();

			// Validate the transaction ID
			if (!isValidTxId(txid)) {
				throw APIError.internal('Failed to generate a valid transaction ID');
			}

			// Convert amount to centavos (cents)
			const amountInCents = Math.round(amount * 100);

			// Prepare the request body for Flow2Pay
			const requestBody: {
				txId: string;
				valor: number;
				tempoExpiracao: number;
				informacaoAdicional: string;
				comImagem: boolean;
				numeroDocumento?: string;
			} = {
				txId: txid,
				valor: amountInCents,
				tempoExpiracao: expiration_time,
				informacaoAdicional: description || 'PIX payment',
				comImagem: true,
			};

			// Add numeroDocumento if provided (CPF/CNPJ of the payer)
			if (numero_documento) {
				// Remove any non-alphanumeric characters
				const cleanDocumento = numero_documento.replace(/\D/g, '');
				requestBody.numeroDocumento = cleanDocumento;
				logger.debug('Including numeroDocumento in request', {
					original: numero_documento,
					cleaned: cleanDocumento,
				});
			}

			logger.info('Generating QR code', {
				user_id,
				amount: amountInCents,
				txid,
			});

			// Call Flow2Pay API to generate QR code with enhanced error handling
			let response;
			try {
				// Set a timeout for the Flow2Pay API call
				const apiCallPromise = callFlow2Pay<{
					txid: string;
					qrcode: string;
					imagemQrcode: any;
				}>('/qrcode/v2/gerar', 'POST', requestBody, 3); // Use 3 retries

				// Set a timeout for the entire API call
				const timeoutPromise = new Promise<never>((_, reject) => {
					setTimeout(() => {
						reject(new Error('Flow2Pay API call timed out after 20 seconds'));
					}, 20000);
				});

				response = await Promise.race([apiCallPromise, timeoutPromise]);

				logger.info('Flow2Pay QR code generation successful', {
					txid,
					has_qrcode: !!response.qrcode,
					has_image: !!response.imagemQrcode,
				});
			} catch (apiError) {
				logger.error('Flow2Pay QR code generation failed', {
					error: apiError instanceof Error ? apiError.message : 'Unknown error',
					is_timeout:
						apiError instanceof Error && apiError.message.includes('timed out'),
					txid,
					user_id,
				});

				throw APIError.unavailable(
					'Payment provider service unavailable. Please try again later.'
				);
			}

			// Log the raw response for debugging
			logger.debug('Flow2Pay QR code raw response', {
				txid,
				response_keys: Object.keys(response),
				imagemQrcode_type: typeof response.imagemQrcode,
				imagemQrcode_is_null: response.imagemQrcode === null,
				imagemQrcode_is_undefined: response.imagemQrcode === undefined,
				qrcode_type: typeof response.qrcode,
				qrcode_length: response.qrcode ? response.qrcode.length : 0,
			});

			// Log the full response for debugging
			logger.debug('Flow2Pay full response', {
				response: JSON.stringify(response),
			});

			// Handle the QR code image with extreme caution
			// Initialize with empty string to ensure we always have a valid string
			let qrCodeImage: string = '';

			try {
				// Log detailed information about the imagemQrcode field
				logger.debug('Detailed imagemQrcode analysis', {
					txid,
					type: typeof response.imagemQrcode,
					is_null: response.imagemQrcode === null,
					is_undefined: response.imagemQrcode === undefined,
					is_object: typeof response.imagemQrcode === 'object',
					is_array: Array.isArray(response.imagemQrcode),
					constructor: response.imagemQrcode
						? response.imagemQrcode.constructor?.name
						: 'N/A',
				});

				// First, check if imagemQrcode exists
				if (
					response.imagemQrcode === undefined ||
					response.imagemQrcode === null
				) {
					logger.warn('Flow2Pay returned null or undefined imagemQrcode', {
						txid,
					});
					// Keep qrCodeImage as empty string
				}
				// If it's already a string, use it directly
				else if (typeof response.imagemQrcode === 'string') {
					qrCodeImage = response.imagemQrcode;
					logger.debug('Using string imagemQrcode directly', {
						txid,
						length: qrCodeImage.length,
					});
				}
				// If it's an object, try to extract the image data
				else if (typeof response.imagemQrcode === 'object') {
					// Try different approaches to extract the image data

					// Approach 1: Check if it has a data property
					if (
						response.imagemQrcode.data &&
						typeof response.imagemQrcode.data === 'string'
					) {
						qrCodeImage = response.imagemQrcode.data;
						logger.debug('Extracted data property from imagemQrcode object', {
							txid,
							data_length: qrCodeImage.length,
						});
					}
					// Approach 2: Check if it has a base64 property
					else if (
						response.imagemQrcode.base64 &&
						typeof response.imagemQrcode.base64 === 'string'
					) {
						qrCodeImage = response.imagemQrcode.base64;
						logger.debug('Extracted base64 property from imagemQrcode object', {
							txid,
							base64_length: qrCodeImage.length,
						});
					}
					// Approach 3: Check if it has an image property
					else if (
						response.imagemQrcode.image &&
						typeof response.imagemQrcode.image === 'string'
					) {
						qrCodeImage = response.imagemQrcode.image;
						logger.debug('Extracted image property from imagemQrcode object', {
							txid,
							image_length: qrCodeImage.length,
						});
					}
					// Approach 4: Check if it has a content property
					else if (
						response.imagemQrcode.content &&
						typeof response.imagemQrcode.content === 'string'
					) {
						qrCodeImage = response.imagemQrcode.content;
						logger.debug(
							'Extracted content property from imagemQrcode object',
							{
								txid,
								content_length: qrCodeImage.length,
							}
						);
					}
					// New approach: Try to convert the entire object to a string
					else {
						logger.warn(
							'Could not extract image data from imagemQrcode object, converting to string',
							{
								txid,
								object_keys: Object.keys(response.imagemQrcode),
							}
						);
						try {
							// Convert to a string representation that can be stored in the database
							qrCodeImage = JSON.stringify(response.imagemQrcode);
							logger.debug('Converted object to JSON string', {
								txid,
								converted_length: qrCodeImage.length,
							});
						} catch (e) {
							logger.error('Failed to stringify imagemQrcode object', {
								txid,
								error: e instanceof Error ? e.message : 'Unknown error',
							});
							qrCodeImage = '';
						}
					}
				}
				// For any other type, convert to string if possible
				else {
					try {
						const converted = String(response.imagemQrcode);
						if (converted && typeof converted === 'string') {
							qrCodeImage = converted;
							logger.warn(
								'Converted non-object, non-string imagemQrcode to string',
								{
									txid,
									original_type: typeof response.imagemQrcode,
									converted_length: converted.length,
								}
							);
						}
					} catch (e) {
						logger.error('Failed to convert imagemQrcode to string', {
							txid,
							error: e instanceof Error ? e.message : 'Unknown error',
						});
						// Keep qrCodeImage as empty string
					}
				}

				// Final validation to ensure we have a string
				if (typeof qrCodeImage !== 'string') {
					logger.error('QR code image is still not a string after conversion', {
						txid,
						final_type: typeof qrCodeImage,
					});
					qrCodeImage = '';
				}
			} catch (e) {
				logger.error('Exception during QR code image processing', {
					txid,
					error: e instanceof Error ? e.message : 'Unknown error',
				});
				qrCodeImage = '';
			}

			logger.debug('Final QR code image preparation', {
				txid,
				final_type: typeof qrCodeImage,
				length: qrCodeImage.length,
				is_empty: qrCodeImage === '',
			});

			try {
				// Store the QR code in the database
				logger.debug('Attempting to store QR code in database', {
					txid,
					user_id,
					amount,
					qr_code_text_length: response.qrcode ? response.qrcode.length : 0,
				});

				// Ensure qrcode is a string before storing
				const safeQrCodeText =
					typeof response.qrcode === 'string' ? response.qrcode : '';

				if (!safeQrCodeText) {
					logger.warn('Flow2Pay returned empty or invalid qrcode text', {
						txid,
					});
				}

				// Log the exact values being inserted
				logger.debug('Database insertion values', {
					txid,
					user_id_type: typeof user_id,
					txid_type: typeof txid,
					amount_type: typeof amount,
					description_type: typeof description,
					expiration_time_type: typeof expiration_time,
					qr_code_text_type: typeof safeQrCodeText,
					partner_id_type: typeof user.partnerId,
					qr_code_text_length: safeQrCodeText.length,
				});

				// Use Drizzle operations to insert the QR code
				await operations.qrCodeOperations.create({
					userId: String(user_id),
					txid: txid,
					amount: amountInCents,
					description: description || '',
					expirationTime: expiration_time,
					qrCodeImage: qrCodeImage,
					qrCodeText:
						typeof response.qrcode === 'string' ? response.qrcode : '',
					status: 'pending',
					partnerId: user.partnerId || 'flow2pay',
				});

				logger.debug('Successfully stored QR code in database', { txid });
			} catch (dbError) {
				// Log detailed database error information
				logger.error('Failed to store QR code in database', {
					error: dbError instanceof Error ? dbError.message : 'Unknown error',
					txid,
				});

				// Try one more time with absolutely minimal data
				try {
					logger.info(
						'Attempting fallback database insertion with minimal data',
						{ txid }
					);

					// Use Drizzle operations for fallback insertion
					await operations.qrCodeOperations.create({
						userId: String(user_id),
						txid: txid,
						amount: amountInCents,
						description: description || '',
						expirationTime: expiration_time,
						qrCodeImage: qrCodeImage,
						qrCodeText:
							typeof response.qrcode === 'string' ? response.qrcode : '',
						status: 'pending',
						partnerId: user.partnerId || 'flow2pay',
					});

					logger.info('Fallback database insertion successful', { txid });
				} catch (fallbackError) {
					logger.error('Fallback database insertion also failed', {
						error:
							fallbackError instanceof Error
								? fallbackError.message
								: 'Unknown error',
						txid,
					});

					// Throw a more specific error
					throw APIError.internal(
						'Failed to store QR code in database: ' +
							(dbError instanceof Error ? dbError.message : 'Unknown error')
					);
				}
			}

			// Calculate execution time for performance monitoring
			const executionTime = performance.now() - startTime;
			logger.info('QR code generated successfully', {
				txid,
				execution_time_ms: Math.round(executionTime),
			});

			// Return the QR code information
			// Note: We return the original amount value, not the cents value stored in the database
			return {
				txid,
				qr_code_text: response.qrcode,
				qr_code_image: qrCodeImage,
				expiration_time,
				amount, // Keep the original amount value for API consistency
				description,
				created_at: new Date().toISOString(),
			};
		} catch (error) {
			// Handle specific Flow2Pay errors
			if (error instanceof Error) {
				logger.error('QR code generation failed', {
					error: error.message,
					user_id,
				});

				if (error.message.includes('txId')) {
					throw APIError.invalidArgument(
						'Invalid transaction ID format. Must be 25-35 alphanumeric characters [a-zA-Z0-9].'
					);
				}
			}

			// Re-throw the error
			throw error;
		}
	}
);

/**
 * Get QR code details by txid
 */
export const getQRCode = api(
	{ method: 'GET', path: '/qrcode/:txid', expose: true },
	async (params: {
		api_key: Header<'X-API-Key'>;
		txid: string;
	}): Promise<QRCodeResponse> => {
		const logger = log.with({ action: 'get_qr_code' });

		// Start tracking execution time for performance monitoring
		const startTime = performance.now();

		const { api_key, txid } = params;

		logger.info('Getting QR code details', { txid });

		try {
			// Test database connection first
			const isDbConnected = await testDatabaseConnection();
			if (!isDbConnected) {
				logger.error('Database connection failed during QR code retrieval');
				throw APIError.unavailable(
					'Database connection error. Please try again later.'
				);
			}

			// Validate txid format
			if (!isValidTxId(txid)) {
				logger.warn('Invalid txid format', { txid });
				throw APIError.invalidArgument(
					'Invalid transaction ID format. Must be 25-35 alphanumeric characters [a-zA-Z0-9].'
				);
			}

			// Validate API key with timeout handling
			let authResult;
			try {
				const authPromise = validateAPIKey(
					api_key,
					API_PERMISSIONS.GENERATE,
					'/qrcode/:txid'
				);

				// Set a timeout for the API key validation
				const timeoutPromise = new Promise<never>((_, reject) => {
					setTimeout(() => {
						reject(new Error('API key validation timed out after 5 seconds'));
					}, 5000);
				});

				authResult = await Promise.race([authPromise, timeoutPromise]);
				logger.debug('API key validated successfully', {
					user_id: authResult.user_id,
				});
			} catch (authError) {
				logger.error('API key validation error', {
					error:
						authError instanceof Error ? authError.message : 'Unknown error',
					is_timeout:
						authError instanceof Error &&
						authError.message.includes('timed out'),
				});
				throw APIError.internal(
					'Authentication service unavailable. Please try again later.'
				);
			}

			// Get QR code from database with timeout handling
			let qrCode;
			try {
				const qrCodePromise = operations.qrCodeOperations.getByTxid(txid);

				// Set a timeout for the database query
				const timeoutPromise = new Promise<never>((_, reject) => {
					setTimeout(() => {
						reject(new Error('Database query timed out after 5 seconds'));
					}, 5000);
				});

				qrCode = await Promise.race([qrCodePromise, timeoutPromise]);

				if (qrCode) {
					logger.debug('QR code found in database', {
						txid,
						qr_code_id: qrCode.id,
						user_id: qrCode.userId,
					});
				} else {
					logger.warn('QR code not found in database', { txid });
				}
			} catch (dbError) {
				logger.error('Database error during QR code retrieval', {
					error: dbError instanceof Error ? dbError.message : 'Unknown error',
					is_timeout:
						dbError instanceof Error && dbError.message.includes('timed out'),
					txid,
				});
				throw APIError.internal('Database error. Please try again later.');
			}

			if (!qrCode) {
				logger.warn(`QR code with txid ${txid} not found`);
				throw APIError.notFound(`QR code with txid ${txid} not found`);
			}

			// Check if the user has access to this QR code
			if (qrCode.userId !== String(authResult.user_id)) {
				logger.warn('User does not have access to this QR code', {
					qr_code_user_id: qrCode.userId,
					requesting_user_id: authResult.user_id,
				});
				throw APIError.permissionDenied(
					'You do not have access to this QR code'
				);
			}

			// Ensure the QR code image is properly formatted
			let qrCodeImage = qrCode.qrCodeImage;

			// If it looks like a JSON string, try to parse it
			if (
				qrCodeImage &&
				qrCodeImage.startsWith('{') &&
				qrCodeImage.endsWith('}')
			) {
				try {
					// Just validate it's valid JSON, but keep it as a string
					JSON.parse(qrCodeImage);
					logger.debug('QR code image is valid JSON', { txid });
				} catch (e) {
					// If it's not valid JSON, just use it as is
					logger.warn('Failed to parse QR code image as JSON', {
						txid,
						error: e instanceof Error ? e.message : 'Unknown error',
					});
				}
			}

			// Calculate execution time for performance monitoring
			const executionTime = performance.now() - startTime;
			logger.info('QR code retrieved successfully', {
				txid,
				execution_time_ms: Math.round(executionTime),
			});

			// Return the QR code information
			// Convert amount back from cents to original value
			return {
				txid: qrCode.txid,
				qr_code_text: qrCode.qrCodeText || '',
				qr_code_image: qrCodeImage || '',
				expiration_time: qrCode.expirationTime,
				amount: qrCode.amount / 100, // Convert from cents back to original value
				description: qrCode.description || '',
				created_at: qrCode.createdAt.toISOString(),
			};
		} catch (error) {
			// Log the error
			if (error instanceof Error) {
				logger.error('QR code retrieval failed', {
					error: error.message,
					txid,
					status: error instanceof APIError ? error.code : 'unknown',
				});
			}

			// Re-throw the error
			throw error;
		}
	}
);

/**
 * Transfer PIX to another account
 */
export const transferPix = api(
	{ method: 'POST', path: '/transfer', expose: true },
	async (params: {
		api_key: Header<'X-API-Key'>;
		user_id?: string;
		pix_key: string;
		amount: number;
		description?: string;
		ip_address?: string;
		user_agent?: string;
	}): Promise<TransferResponse> => {
		const logger = log.with({ action: 'transfer_pix' });

		// Start tracking execution time for performance monitoring
		const startTime = performance.now();

		let {
			api_key,
			user_id,
			pix_key,
			amount,
			description = '',
			ip_address,
			user_agent,
		} = params;

		try {
			// Test database connection first with retries
			const isDbConnected = await testDatabaseConnection(2, 1000); // 2 retries with 1s initial delay
			if (!isDbConnected) {
				logger.error(
					'Database connection failed during PIX transfer after multiple attempts'
				);
				throw APIError.unavailable(
					'Database connection error. Please try again later. Our team has been notified.'
				);
			}

			logger.info(
				'Database connection successful, proceeding with PIX transfer'
			);

			// Validate API key and get the user_id if not provided
			// Add timeout handling for API key validation
			let authResult;
			try {
				const authPromise = validateAPIKey(
					api_key,
					API_PERMISSIONS.TRANSFER,
					'/transfer',
					ip_address,
					user_agent
				);

				// Set a timeout for the API key validation
				const timeoutPromise = new Promise<never>((_, reject) => {
					setTimeout(() => {
						reject(new Error('API key validation timed out after 5 seconds'));
					}, 5000);
				});

				authResult = await Promise.race([authPromise, timeoutPromise]);
			} catch (authError) {
				logger.error('API key validation error', {
					error:
						authError instanceof Error ? authError.message : 'Unknown error',
					is_timeout:
						authError instanceof Error &&
						authError.message.includes('timed out'),
				});
				throw APIError.internal(
					'Authentication service unavailable. Please try again later.'
				);
			}

			// If user_id wasn't provided, use the one from the API key
			if (!user_id) {
				user_id = authResult.user_id;
				logger.info('Using user_id from API key', {
					user_id: authResult.user_id,
				});
			}
			// If user_id is provided as a number, convert to string for database operations
			else if (typeof user_id === 'number') {
				user_id = String(user_id);
				logger.debug('Converted numeric user_id to string', {
					original: user_id,
				});
			}

			// Get user's payment partner information with timeout handling
			let user;
			try {
				// Ensure user_id is string for database operations
				const userIdStr =
					typeof user_id === 'number' ? String(user_id) : user_id;
				const userPromise = operations.userOperations.getById(userIdStr);

				// Set a timeout for the user lookup
				const timeoutPromise = new Promise<never>((_, reject) => {
					setTimeout(() => {
						reject(new Error('User lookup timed out after 5 seconds'));
					}, 5000);
				});

				user = await Promise.race([userPromise, timeoutPromise]);
			} catch (userError) {
				logger.error('User lookup error', {
					error:
						userError instanceof Error ? userError.message : 'Unknown error',
					is_timeout:
						userError instanceof Error &&
						userError.message.includes('timed out'),
					user_id,
				});
				throw APIError.internal(
					'User service unavailable. Please try again later.'
				);
			}

			if (!user) {
				throw APIError.notFound('User not found');
			}

			// Validate amount
			if (amount <= 0) {
				throw APIError.invalidArgument('Amount must be greater than 0');
			}

			// Validate PIX key
			if (!pix_key) {
				throw APIError.invalidArgument('PIX key is required');
			}

			// Generate a unique ID for this transfer
			const idEnvio = generateTxId();

			// Validate the generated idEnvio
			if (!isValidTxId(idEnvio)) {
				logger.error('Generated invalid idEnvio', { idEnvio });
				throw APIError.internal('Failed to generate a valid transaction ID');
			}

			// Convert amount to centavos (cents)
			const amountInCents = Math.round(amount * 100);

			// Prepare the request body for Flow2Pay
			const requestBody = {
				idEnvio,
				valor: amountInCents,
				chavePixDestino: pix_key,
				descricao: description || 'PIX transfer',
			};

			logger.info('Initiating PIX transfer', {
				user_id,
				amount: amountInCents,
				id_envio: idEnvio,
				pix_key,
				endpoint: '/pix/v1/transferir',
			});

			// Call Flow2Pay API to initiate transfer with enhanced error handling
			let response;
			try {
				// Set a timeout for the Flow2Pay API call
				const apiCallPromise = callFlow2Pay<{
					idEnvio: string;
					status: string;
					mensagem: string;
				}>('/pix/v1/transferir', 'POST', requestBody, 3); // Use 3 retries

				// Set a timeout for the entire API call
				const timeoutPromise = new Promise<never>((_, reject) => {
					setTimeout(() => {
						reject(new Error('Flow2Pay API call timed out after 20 seconds'));
					}, 20000);
				});

				response = await Promise.race([apiCallPromise, timeoutPromise]);

				logger.info('Flow2Pay transfer API call successful', {
					id_envio: idEnvio,
					response_status: response.status,
				});
			} catch (apiError) {
				logger.error('Flow2Pay transfer API call failed', {
					error: apiError instanceof Error ? apiError.message : 'Unknown error',
					is_timeout:
						apiError instanceof Error && apiError.message.includes('timed out'),
					id_envio: idEnvio,
					user_id,
				});

				// Handle specific Flow2Pay errors
				if (apiError instanceof Error) {
					if (apiError.message.includes('Conta sem saldo')) {
						throw APIError.failedPrecondition(
							'Insufficient funds for this transfer'
						);
					}

					if (apiError.message.includes('Chave PIX não cadastrada')) {
						throw APIError.invalidArgument('Invalid PIX key');
					}

					// Handle 404 errors specifically
					if (
						(apiError instanceof APIError &&
							apiError.message.includes('404')) ||
						apiError.message.toLowerCase().includes('not found')
					) {
						throw APIError.internal(
							'PIX transfer endpoint not found. Please contact support.'
						);
					}
				}

				throw APIError.unavailable(
					'Payment provider service unavailable. Please try again later.'
				);
			}

			// Store the transaction in the database with enhanced error handling
			let transaction;
			let dbSuccess = false;
			let dbErrorMessage = '';

			// Function to create transaction record with timeout
			const createTransactionWithTimeout = async (isRetry = false) => {
				// Set a timeout for the database operation
				const dbOperationPromise = operations.transactionOperations.create({
					userId: String(user_id),
					type: 'pix_out',
					amount: amountInCents, // Store in cents for consistency
					status: 'pending',
					description: isRetry
						? 'PIX transfer to ' + pix_key
						: description || 'PIX transfer to ' + pix_key,
					flow2payIdEnvio: idEnvio,
				});

				const timeoutPromise = new Promise<never>((_, reject) => {
					setTimeout(() => {
						reject(new Error('Database operation timed out after 10 seconds'));
					}, 10000);
				});

				// Race the database operation against the timeout
				return await Promise.race([dbOperationPromise, timeoutPromise]);
			};

			try {
				// First attempt to store the transaction
				transaction = await createTransactionWithTimeout();
				dbSuccess = true;

				logger.info('PIX transfer initiated and stored in database', {
					id_envio: idEnvio,
					transaction_id: transaction?.id,
				});
			} catch (dbError) {
				// Log detailed database error information
				const error =
					dbError instanceof Error ? dbError : new Error(String(dbError));
				dbErrorMessage = error.message;

				logger.error('Failed to store PIX transfer in database', {
					error: error.message,
					stack: error.stack,
					id_envio: idEnvio,
				});

				// Check if this is a connection error that might be temporary
				const isConnectionError =
					error.message.includes('connection') ||
					error.message.includes('terminated') ||
					error.message.includes('timeout');

				if (isConnectionError) {
					// Test database connection again
					logger.info('Testing database connection after error...');
					const isDbConnected = await testDatabaseConnection(1, 500);

					if (!isDbConnected) {
						logger.error('Database connection lost during PIX transfer');
					} else {
						logger.info(
							'Database connection restored, attempting fallback insertion'
						);

						// Try one more time with minimal data
						try {
							logger.info(
								'Attempting fallback database insertion with minimal data',
								{
									id_envio: idEnvio,
								}
							);

							transaction = await createTransactionWithTimeout(true);
							dbSuccess = true;

							logger.info('Fallback database insertion successful', {
								id_envio: idEnvio,
								transaction_id: transaction?.id,
							});
						} catch (fallbackError) {
							const fbError =
								fallbackError instanceof Error
									? fallbackError
									: new Error(String(fallbackError));

							logger.error('Fallback database insertion also failed', {
								error: fbError.message,
								stack: fbError.stack,
								id_envio: idEnvio,
							});
						}
					}
				}
			}

			// Calculate execution time for performance monitoring
			const executionTime = performance.now() - startTime;
			logger.info('PIX transfer completed', {
				id_envio: idEnvio,
				execution_time_ms: Math.round(executionTime),
				db_success: dbSuccess,
			});

			// Return the transfer information
			if (dbSuccess) {
				return {
					id_envio: idEnvio,
					status: 'pending',
					message: 'PIX transfer initiated',
					transaction_id: transaction?.id || undefined,
				};
			} else {
				// The transfer was initiated with Flow2Pay but we couldn't store it
				return {
					id_envio: idEnvio,
					status: 'pending',
					message: `PIX transfer initiated but failed to store in database: ${dbErrorMessage}`,
					transaction_id: undefined,
				};
			}
		} catch (error) {
			// Handle specific Flow2Pay errors
			if (error instanceof Error) {
				logger.error('PIX transfer failed', {
					error: error.message,
					user_id,
					pix_key,
					endpoint: '/pix/v1/transferir',
					status: error instanceof APIError ? error.code : 'unknown',
				});
			}

			// Re-throw the error
			throw error;
		}
	}
);

/**
 * Get account balance
 */
export const getAccountBalance = api(
	{ method: 'GET', path: '/balance', expose: true },
	async (params: {
		api_key: Header<'X-API-Key'>;
		user_id?: string;
	}): Promise<BalanceResponse> => {
		const logger = log.with({ action: 'get_balance' });

		let { api_key, user_id } = params;

		try {
			// Validate API key and get the user_id if not provided
			const authResult = await validateAPIKey(
				api_key,
				API_PERMISSIONS.BALANCE,
				'/balance'
			);

			// If user_id wasn't provided, use the one from the API key
			if (!user_id) {
				user_id = authResult.user_id;
			}

			// Get user's payment partner information using Drizzle operations
			const user = await operations.userOperations.getById(String(user_id));

			if (!user) {
				throw APIError.notFound('User not found');
			}

			logger.info('Getting account balance', { user_id });

			// Call Flow2Pay API to get balance
			const response = await callFlow2Pay<{
				saldo: number;
				moeda: string;
			}>('/saldo', 'GET');

			logger.info('Balance retrieved successfully', { user_id });

			// Return the balance information
			return {
				balance: response.saldo / 100, // Convert from centavos to BRL
				currency: response.moeda || 'BRL',
				updated_at: new Date().toISOString(),
			};
		} catch (error) {
			logger.error('Failed to get balance', {
				error: error instanceof Error ? error.message : 'Unknown error',
				user_id,
			});

			// Re-throw the error
			throw error;
		}
	}
);

/**
 * Get transaction history
 */
export const getTransactionHistory = api(
	{ method: 'GET', path: '/transactions', expose: true },
	async (params: {
		api_key: Header<'X-API-Key'>;
		user_id?: string;
		start_date?: string;
		end_date?: string;
		type?: string;
		page?: number;
		page_size?: number;
	}): Promise<TransactionHistoryResponse> => {
		const logger = log.with({ action: 'get_transaction_history' });

		let {
			api_key,
			user_id,
			start_date = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
				.toISOString()
				.split('T')[0], // Default to 30 days ago
			end_date = new Date().toISOString().split('T')[0], // Default to today
			type,
			page = 1,
			page_size = 20,
		} = params;

		try {
			// Validate API key and get the user_id if not provided
			const authResult = await validateAPIKey(
				api_key,
				API_PERMISSIONS.HISTORY,
				'/transactions'
			);

			// If user_id wasn't provided, use the one from the API key
			if (!user_id) {
				user_id = authResult.user_id;
			}

			// Get user's payment partner information using Drizzle operations
			const user = await operations.userOperations.getById(String(user_id));

			if (!user) {
				throw APIError.notFound('User not found');
			}

			// Validate page and page_size
			if (page < 1) {
				throw APIError.invalidArgument('Page must be greater than 0');
			}

			if (page_size < 1 || page_size > 100) {
				throw APIError.invalidArgument('Page size must be between 1 and 100');
			}

			// Parse dates
			const startDate = new Date(start_date);
			const endDate = new Date(end_date);

			// Use Drizzle operations to get transaction history
			const result = await operations.transactionOperations.list({
				userId: String(user_id),
				type: type,
				startDate: startDate,
				endDate: endDate,
				page: page,
				pageSize: page_size,
			});

			// Format transactions for response
			const transactions = result.transactions.map((tx) => ({
				id: Number(tx.id), // Convert string ID to number for API compatibility
				type: tx.type,
				amount: tx.amount,
				status: tx.status,
				description: tx.description || undefined,
				created_at: tx.createdAt.toISOString(),
				txid: tx.flow2payTxid || undefined,
				end_to_end_id: tx.flow2payEndToEndId || undefined,
				id_envio: tx.flow2payIdEnvio || undefined,
			}));

			const totalCount = result.totalCount;
			const totalPages = Math.ceil(totalCount / page_size);

			logger.info('Transaction history retrieved', {
				user_id,
				count: transactions.length,
				total: totalCount,
			});

			// Return the transaction history
			return {
				transactions,
				pagination: {
					total_count: totalCount,
					page,
					page_size,
					total_pages: totalPages,
				},
			};
		} catch (error) {
			logger.error('Failed to get transaction history', {
				error: error instanceof Error ? error.message : 'Unknown error',
				user_id,
			});

			// Re-throw the error
			throw error;
		}
	}
);

// Note: The qrcodePassthrough endpoint has been moved to diagnostics.ts to avoid duplication

/**
 * Create webhook configuration
 */
export const createWebhookConfig = api(
	{ method: 'POST', path: '/webhook-config', expose: true },
	async (params: {
		api_key: Header<'X-API-Key'>;
		user_id?: string;
		url: string;
		events: string[];
		description?: string;
	}): Promise<{
		id: string;
		url: string;
		events: string[];
		description?: string;
		secret_key: string;
		created_at: string;
	}> => {
		const logger = log.with({ action: 'create_webhook_config' });

		let { api_key, user_id, url, events, description } = params;

		try {
			// Test database connection first
			const isDbConnected = await testDatabaseConnection();
			if (!isDbConnected) {
				logger.error(
					'Database connection failed during webhook configuration creation'
				);
				throw APIError.unavailable(
					'Database connection error. Please try again later.'
				);
			}

			// Validate API key and get the user_id if not provided
			// Add timeout handling for API key validation
			let authResult;
			try {
				const authPromise = validateAPIKey(
					api_key,
					API_PERMISSIONS.ALL,
					'/webhook-config'
				);

				// Set a timeout for the API key validation
				const timeoutPromise = new Promise<never>((_, reject) => {
					setTimeout(() => {
						reject(new Error('API key validation timed out after 5 seconds'));
					}, 5000);
				});

				authResult = await Promise.race([authPromise, timeoutPromise]);
			} catch (authError) {
				logger.error('API key validation error', {
					error:
						authError instanceof Error ? authError.message : 'Unknown error',
					is_timeout:
						authError instanceof Error &&
						authError.message.includes('timed out'),
				});
				throw APIError.internal(
					'Authentication service unavailable. Please try again later.'
				);
			}

			// If user_id wasn't provided, use the one from the API key
			if (!user_id) {
				user_id = authResult.user_id;
				logger.info('Using user_id from API key', {
					user_id: authResult.user_id,
				});
			}
			// If user_id is provided as a number, convert to string for database operations
			else if (typeof user_id === 'number') {
				user_id = String(user_id);
				logger.debug('Converted numeric user_id to string', {
					original: user_id,
				});
			}

			// Validate URL
			if (!url || !url.startsWith('http')) {
				throw APIError.invalidArgument('Invalid URL format');
			}

			// Validate events
			if (!events || !Array.isArray(events) || events.length === 0) {
				throw APIError.invalidArgument('At least one event type is required');
			}

			// Generate a secret key for webhook signatures
			const secretKey = crypto.randomBytes(32).toString('hex');

			try {
				// Import the schema
				const { webhookConfigs } = await import('./db/schema');

				// Insert the webhook config directly using the schema
				const result = await db
					.insert(webhookConfigs)
					.values({
						userId: String(user_id),
						url: url,
						secretKey: secretKey,
						events: events,
						description: description || null,
						isActive: true,
					})
					.returning();

				if (!result || result.length === 0) {
					throw new Error('Failed to create webhook configuration');
				}

				const webhookConfig = result[0];

				logger.info('Webhook configuration created', {
					webhook_id: webhookConfig.id,
					user_id,
				});

				// Return the webhook configuration with proper type handling
				return {
					id: webhookConfig.id,
					url: webhookConfig.url,
					events: Array.isArray(webhookConfig.events)
						? webhookConfig.events
						: typeof webhookConfig.events === 'string'
						? JSON.parse(webhookConfig.events)
						: [],
					description: webhookConfig.description || undefined,
					secret_key: webhookConfig.secretKey,
					created_at: webhookConfig.createdAt.toISOString(),
				};
			} catch (dbError) {
				logger.error('Failed to create webhook configuration in database', {
					error: dbError instanceof Error ? dbError.message : 'Unknown error',
					user_id,
				});
				throw APIError.internal(
					'Failed to create webhook configuration: ' +
						(dbError instanceof Error ? dbError.message : 'Unknown error')
				);
			}
		} catch (error) {
			logger.error('Failed to create webhook configuration', {
				error: error instanceof Error ? error.message : 'Unknown error',
				user_id,
			});

			// Re-throw the error
			throw error;
		}
	}
);
