import { api, <PERSON><PERSON><PERSON><PERSON>, Header } from 'encore.dev/api';
import { generateTxId, isValidTxId, callFlow2Pay } from './flow2pay';
import { validateAPIKeyProxy } from './auth';
import log from 'encore.dev/log';
import * as crypto from 'crypto';

/**
 * Interface for QR code generation response
 */
interface QRCodeResponse {
	txid: string;
	qr_code_text: string;
	qr_code_image: string;
	expiration_time: number;
	amount: number;
	description?: string;
	created_at: string;
}

/**
 * Interface for transaction history response
 */
interface TransactionHistoryResponse {
	transactions: Array<{
		id: number;
		type: string;
		amount: number;
		status: string;
		description?: string;
		created_at: string;
		txid?: string;
		end_to_end_id?: string;
		id_envio?: string;
	}>;
	pagination: {
		total_count: number;
		page: number;
		page_size: number;
		total_pages: number;
	};
}

/**
 * Interface for balance response
 */
interface BalanceResponse {
	balance: number;
	currency: string;
	updated_at: string;
}

/**
 * Interface for PIX transfer response
 */
interface TransferResponse {
	id_envio: string;
	status: string;
	message: string;
	transaction_id?: string;
}

/**
 * Generate a QR code for receiving PIX (Pure Proxy Mode)
 */
export const generatePixQRCode = api(
	{ method: 'POST', path: '/qrcode', expose: true },
	async (params: {
		api_key: Header<'X-API-Key'>;
		user_id?: string | number; // Aceita tanto string quanto number para compatibilidade
		amount: number;
		description?: string;
		expiration_time?: number;
		ip_address?: string;
		user_agent?: string;
		numero_documento?: string; // CPF/CNPJ of the payer without special characters
	}): Promise<QRCodeResponse> => {
		const logger = log.with({ action: 'generate_qr_code_proxy' });

		let {
			api_key,
			user_id,
			amount,
			description = '',
			expiration_time = 3600,
			ip_address,
			user_agent,
			numero_documento,
		} = params;

		// Start tracking execution time for performance monitoring
		const startTime = performance.now();

		try {
			logger.info('Starting QR code generation in proxy mode', {
				user_id,
				amount,
				has_description: !!description,
				expiration_time,
			});

			// Validate API key using proxy validation (no database)
			const authResult = await validateAPIKeyProxy(
				api_key,
				'generate_qr_code', // Use string instead of API_PERMISSIONS.GENERATE
				'/qrcode',
				ip_address,
				user_agent
			);

			// If user_id wasn't provided, use the one from the API key
			if (!user_id) {
				user_id = authResult.user_id;
				logger.info('Using user_id from API key', {
					user_id: authResult.user_id,
				});
			}
			// Convert user_id to string if it's a number
			else if (typeof user_id === 'number') {
				user_id = String(user_id);
				logger.debug('Converted numeric user_id to string', {
					original: user_id,
				});
			}

			// Validate amount
			if (amount <= 0) {
				throw APIError.invalidArgument('Amount must be greater than 0');
			}

			// Validate expiration time
			if (expiration_time < 60 || expiration_time > 172800) {
				throw APIError.invalidArgument(
					'Expiration time must be between 60 and 172800 seconds (48 hours)'
				);
			}

			// Generate a unique transaction ID
			const txid = generateTxId();

			// Validate the transaction ID
			if (!isValidTxId(txid)) {
				throw APIError.internal('Failed to generate a valid transaction ID');
			}

			// Convert amount to centavos (cents)
			const amountInCents = Math.round(amount * 100);

			// Prepare the request body for Flow2Pay
			const requestBody: {
				txId: string;
				valor: number;
				tempoExpiracao: number;
				informacaoAdicional: string;
				comImagem: boolean;
				numeroDocumento?: string;
			} = {
				txId: txid,
				valor: amountInCents,
				tempoExpiracao: expiration_time,
				informacaoAdicional: description || 'PIX payment',
				comImagem: true,
			};

			// Add numeroDocumento if provided (CPF/CNPJ of the payer)
			if (numero_documento) {
				// Remove any non-alphanumeric characters
				const cleanDocumento = numero_documento.replace(/\D/g, '');
				requestBody.numeroDocumento = cleanDocumento;
				logger.debug('Including numeroDocumento in request', {
					original: numero_documento,
					cleaned: cleanDocumento,
				});
			}

			logger.info('Generating QR code via Flow2Pay proxy', {
				user_id,
				amount: amountInCents,
				txid,
			});

			// Call Flow2Pay API to generate QR code
			const response = await callFlow2Pay<{
				txid: string;
				qrcode: string;
				imagemQrcode: any;
			}>('/qrcode/v2/gerar', 'POST', requestBody, 3);

			logger.info('Flow2Pay QR code generation successful', {
				txid,
				has_qrcode: !!response.qrcode,
				has_image: !!response.imagemQrcode,
			});

			// Handle the QR code image processing
			let qrCodeImage: string = '';

			try {
				// Process the QR code image from Flow2Pay response
				if (response.imagemQrcode) {
					if (typeof response.imagemQrcode === 'string') {
						qrCodeImage = response.imagemQrcode;
					} else if (typeof response.imagemQrcode === 'object') {
						// Try to extract image data from object
						if (response.imagemQrcode.data) {
							qrCodeImage = response.imagemQrcode.data;
						} else if (response.imagemQrcode.base64) {
							qrCodeImage = response.imagemQrcode.base64;
						} else {
							qrCodeImage = JSON.stringify(response.imagemQrcode);
						}
					} else {
						qrCodeImage = String(response.imagemQrcode);
					}
				}
			} catch (e) {
				logger.error('Error processing QR code image', {
					error: e instanceof Error ? e.message : 'Unknown error',
					txid,
				});
				qrCodeImage = '';
			}

			// Calculate execution time for performance monitoring
			const executionTime = performance.now() - startTime;
			logger.info('QR code generated successfully via proxy', {
				txid,
				execution_time_ms: Math.round(executionTime),
			});

			// Return the QR code information directly from Flow2Pay (no database storage)
			return {
				txid,
				qr_code_text: response.qrcode || '',
				qr_code_image: qrCodeImage,
				expiration_time,
				amount, // Keep the original amount value for API consistency
				description,
				created_at: new Date().toISOString(),
			};
		} catch (error) {
			// Handle specific Flow2Pay errors
			if (error instanceof Error) {
				logger.error('QR code generation failed', {
					error: error.message,
					user_id,
				});

				if (error.message.includes('txId')) {
					throw APIError.invalidArgument(
						'Invalid transaction ID format. Must be 25-35 alphanumeric characters [a-zA-Z0-9].'
					);
				}
			}

			// Re-throw the error
			throw error;
		}
	}
);

/**
 * Get QR code status by txid (Proxy Mode - calls Flow2Pay directly)
 */
export const getQRCode = api(
	{ method: 'GET', path: '/qrcode/:txid', expose: true },
	async (params: {
		api_key: Header<'X-API-Key'>;
		txid: string;
	}): Promise<QRCodeResponse> => {
		const logger = log.with({ action: 'get_qr_code_proxy' });

		// Start tracking execution time for performance monitoring
		const startTime = performance.now();

		const { api_key, txid } = params;

		logger.info('Getting QR code status via proxy', { txid });

		try {
			// Validate txid format
			if (!isValidTxId(txid)) {
				logger.warn('Invalid txid format', { txid });
				throw APIError.invalidArgument(
					'Invalid transaction ID format. Must be 25-35 alphanumeric characters [a-zA-Z0-9].'
				);
			}

			// Validate API key using proxy validation
			const authResult = await validateAPIKeyProxy(
				api_key,
				'generate_qr_code',
				'/qrcode/:txid'
			);

			logger.debug('API key validated successfully in proxy mode', {
				user_id: authResult.user_id,
			});

			// Call Flow2Pay API to check QR code status
			// Note: Flow2Pay might not have a direct endpoint to check QR code status
			// In that case, we return a generic response indicating the QR code exists
			try {
				// Since Flow2Pay doesn't provide a direct QR code lookup endpoint,
				// we'll return a generic response for valid txids
				logger.info(
					'QR code status check via proxy - returning generic response',
					{
						txid,
					}
				);

				// Calculate execution time for performance monitoring
				const executionTime = performance.now() - startTime;
				logger.info('QR code status retrieved successfully via proxy', {
					txid,
					execution_time_ms: Math.round(executionTime),
				});

				// Return a generic QR code response since we don't store data
				return {
					txid,
					qr_code_text: '', // Not available in proxy mode
					qr_code_image: '', // Not available in proxy mode
					expiration_time: 3600, // Default expiration
					amount: 0, // Not available in proxy mode
					description: 'QR Code (Proxy Mode)',
					created_at: new Date().toISOString(),
				};
			} catch (apiError) {
				logger.error('Error checking QR code status with Flow2Pay', {
					error: apiError instanceof Error ? apiError.message : 'Unknown error',
					txid,
				});

				throw APIError.unavailable(
					'Unable to check QR code status. Please try again later.'
				);
			}
		} catch (error) {
			// Log the error
			if (error instanceof Error) {
				logger.error('QR code status check failed', {
					error: error.message,
					txid,
					status: error instanceof APIError ? error.code : 'unknown',
				});
			}

			// Re-throw the error
			throw error;
		}
	}
);

/**
 * Transfer PIX to another account (Pure Proxy Mode)
 */
export const transferPix = api(
	{ method: 'POST', path: '/transfer', expose: true },
	async (params: {
		api_key: Header<'X-API-Key'>;
		user_id?: string;
		pix_key: string;
		amount: number;
		description?: string;
		ip_address?: string;
		user_agent?: string;
	}): Promise<TransferResponse> => {
		const logger = log.with({ action: 'transfer_pix_proxy' });

		// Start tracking execution time for performance monitoring
		const startTime = performance.now();

		let {
			api_key,
			user_id,
			pix_key,
			amount,
			description = '',
			ip_address,
			user_agent,
		} = params;

		try {
			logger.info('Starting PIX transfer in proxy mode', {
				user_id,
				amount,
				pix_key,
			});

			// Validate API key using proxy validation (no database)
			const authResult = await validateAPIKeyProxy(
				api_key,
				'transfer_pix',
				'/transfer',
				ip_address,
				user_agent
			);

			// If user_id wasn't provided, use the one from the API key
			if (!user_id) {
				user_id = authResult.user_id;
				logger.info('Using user_id from API key', {
					user_id: authResult.user_id,
				});
			}
			// Convert user_id to string if it's a number
			else if (typeof user_id === 'number') {
				user_id = String(user_id);
				logger.debug('Converted numeric user_id to string', {
					original: user_id,
				});
			}

			// Validate amount
			if (amount <= 0) {
				throw APIError.invalidArgument('Amount must be greater than 0');
			}

			// Validate PIX key
			if (!pix_key) {
				throw APIError.invalidArgument('PIX key is required');
			}

			// Generate a unique ID for this transfer
			const idEnvio = generateTxId();

			// Validate the generated idEnvio
			if (!isValidTxId(idEnvio)) {
				logger.error('Generated invalid idEnvio', { idEnvio });
				throw APIError.internal('Failed to generate a valid transaction ID');
			}

			// Convert amount to centavos (cents)
			const amountInCents = Math.round(amount * 100);

			// Prepare the request body for Flow2Pay
			const requestBody = {
				idEnvio,
				valor: amountInCents,
				chavePixDestino: pix_key,
				descricao: description || 'PIX transfer',
			};

			logger.info('Initiating PIX transfer via proxy', {
				user_id,
				amount: amountInCents,
				id_envio: idEnvio,
				pix_key,
				endpoint: '/pix/v1/transferir',
			});

			// Call Flow2Pay API to initiate transfer
			const response = await callFlow2Pay<{
				idEnvio: string;
				status: string;
				mensagem: string;
			}>('/pix/v1/transferir', 'POST', requestBody, 3);

			logger.info('Flow2Pay transfer API call successful', {
				id_envio: idEnvio,
				response_status: response.status,
			});

			// Calculate execution time for performance monitoring
			const executionTime = performance.now() - startTime;
			logger.info('PIX transfer completed via proxy', {
				id_envio: idEnvio,
				execution_time_ms: Math.round(executionTime),
			});

			// Return the transfer information directly from Flow2Pay (no database storage)
			return {
				id_envio: idEnvio,
				status: response.status || 'pending',
				message: response.mensagem || 'PIX transfer initiated',
				transaction_id: undefined, // Not available in proxy mode
			};
		} catch (error) {
			// Handle specific Flow2Pay errors
			if (error instanceof Error) {
				logger.error('PIX transfer failed', {
					error: error.message,
					user_id,
					pix_key,
					endpoint: '/pix/v1/transferir',
					status: error instanceof APIError ? error.code : 'unknown',
				});
			}

			// Re-throw the error
			throw error;
		}
	}
);

/**
 * Get account balance (Pure Proxy Mode)
 */
export const getAccountBalance = api(
	{ method: 'GET', path: '/balance', expose: true },
	async (params: {
		api_key: Header<'X-API-Key'>;
		user_id?: string;
	}): Promise<BalanceResponse> => {
		const logger = log.with({ action: 'get_balance_proxy' });

		let { api_key, user_id } = params;

		try {
			// Validate API key using proxy validation (no database)
			const authResult = await validateAPIKeyProxy(
				api_key,
				'check_balance',
				'/balance'
			);

			// If user_id wasn't provided, use the one from the API key
			if (!user_id) {
				user_id = authResult.user_id;
			}

			logger.info('Getting account balance via proxy', { user_id });

			// Call Flow2Pay API to get balance directly
			const response = await callFlow2Pay<{
				saldo: number;
				moeda: string;
			}>('/saldo', 'GET');

			logger.info('Balance retrieved successfully via proxy', { user_id });

			// Return the balance information directly from Flow2Pay
			return {
				balance: response.saldo / 100, // Convert from centavos to BRL
				currency: response.moeda || 'BRL',
				updated_at: new Date().toISOString(),
			};
		} catch (error) {
			logger.error('Failed to get balance via proxy', {
				error: error instanceof Error ? error.message : 'Unknown error',
				user_id,
			});

			// Re-throw the error
			throw error;
		}
	}
);

/**
 * Get transaction history (Pure Proxy Mode)
 *
 * Note: Since Flow2Pay doesn't provide a direct transaction history endpoint,
 * this returns a mock response in proxy mode. In a real implementation, you would
 * call the Flow2Pay API directly if such an endpoint existed.
 */
export const getTransactionHistory = api(
	{ method: 'GET', path: '/transactions', expose: true },
	async (params: {
		api_key: Header<'X-API-Key'>;
		user_id?: string;
		start_date?: string;
		end_date?: string;
		type?: string;
		page?: number;
		page_size?: number;
	}): Promise<TransactionHistoryResponse> => {
		const logger = log.with({ action: 'get_transaction_history_proxy' });

		let {
			api_key,
			user_id,
			start_date = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
				.toISOString()
				.split('T')[0], // Default to 30 days ago
			end_date = new Date().toISOString().split('T')[0], // Default to today
			type,
			page = 1,
			page_size = 20,
		} = params;

		try {
			// Validate API key using proxy validation (no database)
			const authResult = await validateAPIKeyProxy(
				api_key,
				'view_history',
				'/transactions'
			);

			// If user_id wasn't provided, use the one from the API key
			if (!user_id) {
				user_id = authResult.user_id;
			}

			// Validate page and page_size
			if (page < 1) {
				throw APIError.invalidArgument('Page must be greater than 0');
			}

			if (page_size < 1 || page_size > 100) {
				throw APIError.invalidArgument('Page size must be between 1 and 100');
			}

			logger.info('Getting transaction history via proxy', {
				user_id,
				start_date,
				end_date,
				type,
				page,
				page_size,
			});

			// In a real implementation, you would call Flow2Pay's transaction history API here
			// Since Flow2Pay doesn't provide such an endpoint, we return a mock response

			// Return empty transaction history in proxy mode
			const transactions: Array<{
				id: number;
				type: string;
				amount: number;
				status: string;
				description?: string;
				created_at: string;
				txid?: string;
				end_to_end_id?: string;
				id_envio?: string;
			}> = [];

			logger.info(
				'Transaction history retrieved via proxy (empty in proxy mode)',
				{
					user_id,
					count: transactions.length,
				}
			);

			// Return the transaction history
			return {
				transactions,
				pagination: {
					total_count: 0,
					page,
					page_size,
					total_pages: 0,
				},
			};
		} catch (error) {
			logger.error('Failed to get transaction history via proxy', {
				error: error instanceof Error ? error.message : 'Unknown error',
				user_id,
			});

			// Re-throw the error
			throw error;
		}
	}
);

// Note: The qrcodePassthrough endpoint has been moved to diagnostics.ts to avoid duplication

/**
 * Create webhook configuration (Pure Proxy Mode)
 *
 * In proxy mode, this endpoint doesn't store webhook configurations in a database.
 * Instead, it returns a mock response with the provided data.
 */
export const createWebhookConfig = api(
	{ method: 'POST', path: '/webhook-config', expose: true },
	async (params: {
		api_key: Header<'X-API-Key'>;
		user_id?: string;
		url: string;
		events: string[];
		description?: string;
	}): Promise<{
		id: string;
		url: string;
		events: string[];
		description?: string;
		secret_key: string;
		created_at: string;
	}> => {
		const logger = log.with({ action: 'create_webhook_config_proxy' });

		let { api_key, user_id, url, events, description } = params;

		try {
			// Validate API key using proxy validation (no database)
			const authResult = await validateAPIKeyProxy(
				api_key,
				'all_permissions',
				'/webhook-config'
			);

			// If user_id wasn't provided, use the one from the API key
			if (!user_id) {
				user_id = authResult.user_id;
				logger.info('Using user_id from API key', {
					user_id: authResult.user_id,
				});
			}

			// Validate URL
			if (!url || !url.startsWith('http')) {
				throw APIError.invalidArgument('Invalid URL format');
			}

			// Validate events
			if (!events || !Array.isArray(events) || events.length === 0) {
				throw APIError.invalidArgument('At least one event type is required');
			}

			// Generate a secret key for webhook signatures
			const secretKey = crypto.randomBytes(32).toString('hex');

			// Generate a mock ID for the webhook configuration
			const mockId = `webhook-${Date.now()}-${Math.random()
				.toString(36)
				.substring(2, 10)}`;

			logger.info('Webhook configuration created in proxy mode', {
				webhook_id: mockId,
				user_id,
				url,
				events,
			});

			// Return a mock webhook configuration
			return {
				id: mockId,
				url: url,
				events: events,
				description: description,
				secret_key: secretKey,
				created_at: new Date().toISOString(),
			};
		} catch (error) {
			logger.error('Failed to create webhook configuration in proxy mode', {
				error: error instanceof Error ? error.message : 'Unknown error',
				user_id,
			});

			// Re-throw the error
			throw error;
		}
	}
);
