import {
	pgTable,
	text,
	timestamp,
	boolean,
	jsonb,
	integer,
	pgSchema,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { generateId } from '../utils/id';

// Create pixapi schema
export const pixapiSchema = pgSchema('pixapi');

// Users table
export const users = pgTable('users', {
	id: text('id')
		.primaryKey()
		.notNull()
		.$defaultFn(() => generateId()),
	email: text('email').notNull().unique(),
	name: text('name').notNull(),
	partnerId: text('partner_id').notNull(), // 'flow2pay' or other future payment partners
	createdAt: timestamp('created_at', { withTimezone: true })
		.notNull()
		.defaultNow(),
	updatedAt: timestamp('updated_at', { withTimezone: true })
		.notNull()
		.defaultNow(),
});

// QR codes table
export const qrCodes = pgTable('qr_codes', {
	id: text('id')
		.primaryKey()
		.notNull()
		.$defaultFn(() => generateId()),
	userId: text('user_id').references(() => users.id),
	txid: text('txid').notNull().unique(),
	amount: integer('amount').notNull(), // Amount in cents
	description: text('description'),
	expirationTime: integer('expiration_time').notNull(), // Expiration time in seconds
	qrCodeImage: text('qr_code_image'), // Base64 encoded QR code image
	qrCodeText: text('qr_code_text').notNull(), // PIX code string (can be copied by user)
	status: text('status').notNull(), // 'pending', 'completed', 'expired', 'canceled'
	partnerId: text('partner_id'), // Which payment partner was used
	externalId: text('external_id'), // Additional ID for reference if needed
	createdAt: timestamp('created_at', { withTimezone: true })
		.notNull()
		.defaultNow(),
	updatedAt: timestamp('updated_at', { withTimezone: true })
		.notNull()
		.defaultNow(),
});

// Transactions table
export const transactions = pgTable('transactions', {
	id: text('id')
		.primaryKey()
		.notNull()
		.$defaultFn(() => generateId()),
	userId: text('user_id')
		.notNull()
		.references(() => users.id),
	qrCodeId: text('qr_code_id').references(() => qrCodes.id), // Optional, only for PIX received via QR code
	type: text('type').notNull(), // 'pix_in', 'pix_out', 'pix_in_reversal', 'pix_out_reversal'
	amount: integer('amount').notNull(), // Amount in cents
	currency: text('currency').notNull().default('BRL'),
	status: text('status').notNull(), // 'pending', 'completed', 'failed', 'reversed'
	description: text('description'),
	flow2payTxid: text('flow2pay_txid'), // For QR code payments (PIX in)
	flow2payEndToEndId: text('flow2pay_end_to_end_id'), // Unique PIX identifier
	flow2payIdEnvio: text('flow2pay_id_envio'), // For direct transfers (PIX out)
	webhookPayload: jsonb('webhook_payload'), // Store the raw webhook data for auditing
	createdAt: timestamp('created_at', { withTimezone: true })
		.notNull()
		.defaultNow(),
	updatedAt: timestamp('updated_at', { withTimezone: true })
		.notNull()
		.defaultNow(),
});

// API keys table
export const apiKeys = pgTable('api_keys', {
	id: text('id')
		.primaryKey()
		.notNull()
		.$defaultFn(() => generateId()),
	userId: text('user_id')
		.notNull()
		.references(() => users.id),
	apiKey: text('api_key').notNull().unique(),
	name: text('name').notNull(), // A name to identify this key
	permissions: jsonb('permissions').notNull(), // Store permissions as JSON
	description: text('description'),
	isActive: boolean('is_active').notNull().default(true),
	lastUsedAt: timestamp('last_used_at', { withTimezone: true }),
	createdAt: timestamp('created_at', { withTimezone: true })
		.notNull()
		.defaultNow(),
	updatedAt: timestamp('updated_at', { withTimezone: true })
		.notNull()
		.defaultNow(),
	expiresAt: timestamp('expires_at', { withTimezone: true }), // NULL means no expiration
	rateLimit: integer('rate_limit'),
	rateLimitWindow: integer('rate_limit_window'),
	usageCount: integer('usage_count').default(0),
});

// API key usage table
export const apiKeyUsage = pgTable('api_key_usage', {
	id: text('id')
		.primaryKey()
		.notNull()
		.$defaultFn(() => generateId()),
	apiKeyId: text('api_key_id')
		.notNull()
		.references(() => apiKeys.id, { onDelete: 'cascade' }),
	endpoint: text('endpoint').notNull(),
	ipAddress: text('ip_address'),
	userAgent: text('user_agent'),
	createdAt: timestamp('created_at', { withTimezone: true })
		.notNull()
		.defaultNow(),
});

// Webhook configs table
export const webhookConfigs = pgTable('webhook_configs', {
	id: text('id')
		.primaryKey()
		.notNull()
		.$defaultFn(() => generateId()),
	userId: text('user_id')
		.notNull()
		.references(() => users.id),
	url: text('url').notNull(),
	secretKey: text('secret_key').notNull(),
	events: jsonb('events').notNull(), // Array of event types to receive: ["pix_in", "pix_out", etc.]
	flow2payEvents: jsonb('flow2pay_events')
		.notNull()
		.default('["PixIn", "PixInReversal", "PixOut", "PixOutReversalExternal"]'),
	description: text('description'),
	isActive: boolean('is_active').notNull().default(true),
	createdAt: timestamp('created_at', { withTimezone: true })
		.notNull()
		.defaultNow(),
	updatedAt: timestamp('updated_at', { withTimezone: true })
		.notNull()
		.defaultNow(),
	lastCalledAt: timestamp('last_called_at', { withTimezone: true }),
	usageCount: integer('usage_count'),
});

// Webhook delivery logs table
export const webhookDeliveryLogs = pgTable('webhook_delivery_logs', {
	id: text('id')
		.primaryKey()
		.notNull()
		.$defaultFn(() => generateId()),
	webhookConfigId: text('webhook_config_id')
		.notNull()
		.references(() => webhookConfigs.id),
	eventType: text('event_type').notNull(),
	payload: jsonb('payload').notNull(),
	status: text('status').notNull(), // 'success', 'failed', 'pending_retry'
	statusCode: integer('status_code'), // HTTP status code from the response
	responseBody: text('response_body'), // Response from the client webhook endpoint
	attemptCount: integer('attempt_count').notNull().default(1),
	nextRetryAt: timestamp('next_retry_at', { withTimezone: true }),
	createdAt: timestamp('created_at', { withTimezone: true })
		.notNull()
		.defaultNow(),
	updatedAt: timestamp('updated_at', { withTimezone: true })
		.notNull()
		.defaultNow(),
});

// Auth tests table
export const authTests = pgTable('auth_tests', {
	id: text('id')
		.primaryKey()
		.notNull()
		.$defaultFn(() => generateId()),
	partnerId: text('partner_id').notNull(),
	success: boolean('success').notNull(),
	token: text('token'),
	errorMessage: text('error_message'),
	createdAt: timestamp('created_at', { withTimezone: true })
		.notNull()
		.defaultNow(),
});

// Webhooks table
export const webhooks = pgTable('webhooks', {
	id: text('id')
		.primaryKey()
		.notNull()
		.$defaultFn(() => generateId()),
	source: text('source').notNull(),
	eventType: text('event_type').notNull(),
	payload: jsonb('payload').notNull(),
	processed: boolean('processed').notNull().default(false),
	errorMessage: text('error_message'),
	receivedAt: timestamp('received_at', { withTimezone: true })
		.notNull()
		.defaultNow(),
	processedAt: timestamp('processed_at', { withTimezone: true }),
});

// Flow2Pay webhooks table
export const flow2payWebhooks = pgTable('flow2pay_webhooks', {
	id: text('id')
		.primaryKey()
		.notNull()
		.$defaultFn(() => generateId()),
	eventType: text('event_type').notNull(), // 'PixIn', 'PixOut', 'PixInReversal', 'PixOutReversalExternal'
	txid: text('txid'), // For PixIn events
	idEnvio: text('id_envio'), // For PixOut events
	endToEndId: text('end_to_end_id'), // For all events
	valor: integer('valor').notNull(), // Amount in cents
	status: text('status').notNull(), // 'Sucesso', 'Falha', 'Em processamento', etc.
	payload: jsonb('payload').notNull(), // Full webhook payload
	processed: boolean('processed').notNull().default(false),
	processedAt: timestamp('processed_at', { withTimezone: true }),
	// Svix-related fields
	publishedToSvix: boolean('published_to_svix').default(false),
	svixMessageId: text('svix_message_id'), // Svix message ID for tracking
	svixEventType: text('svix_event_type'), // Mapped Svix event type
	publishedAt: timestamp('published_at', { withTimezone: true }),
	createdAt: timestamp('created_at', { withTimezone: true })
		.notNull()
		.defaultNow(),
});

// Audit logs table
export const auditLogs = pgTable('audit_logs', {
	id: text('id')
		.primaryKey()
		.notNull()
		.$defaultFn(() => generateId()),
	userId: text('user_id').references(() => users.id),
	action: text('action').notNull(),
	entityType: text('entity_type').notNull(),
	entityId: text('entity_id'),
	ipAddress: text('ip_address'),
	userAgent: text('user_agent'),
	details: jsonb('details'),
	createdAt: timestamp('created_at', { withTimezone: true })
		.notNull()
		.defaultNow(),
});

// Define relations
export const usersRelations = relations(users, ({ many }) => ({
	qrCodes: many(qrCodes),
	transactions: many(transactions),
	apiKeys: many(apiKeys),
	webhookConfigs: many(webhookConfigs),
}));

export const qrCodesRelations = relations(qrCodes, ({ one, many }) => ({
	user: one(users, {
		fields: [qrCodes.userId],
		references: [users.id],
	}),
	transactions: many(transactions),
}));

export const transactionsRelations = relations(transactions, ({ one }) => ({
	user: one(users, {
		fields: [transactions.userId],
		references: [users.id],
	}),
	qrCode: one(qrCodes, {
		fields: [transactions.qrCodeId],
		references: [qrCodes.id],
	}),
}));

export const apiKeysRelations = relations(apiKeys, ({ one, many }) => ({
	user: one(users, {
		fields: [apiKeys.userId],
		references: [users.id],
	}),
	usages: many(apiKeyUsage),
}));

export const apiKeyUsageRelations = relations(apiKeyUsage, ({ one }) => ({
	apiKey: one(apiKeys, {
		fields: [apiKeyUsage.apiKeyId],
		references: [apiKeys.id],
	}),
}));

export const webhookConfigsRelations = relations(
	webhookConfigs,
	({ one, many }) => ({
		user: one(users, {
			fields: [webhookConfigs.userId],
			references: [users.id],
		}),
		deliveryLogs: many(webhookDeliveryLogs),
	})
);

export const webhookDeliveryLogsRelations = relations(
	webhookDeliveryLogs,
	({ one }) => ({
		webhookConfig: one(webhookConfigs, {
			fields: [webhookDeliveryLogs.webhookConfigId],
			references: [webhookConfigs.id],
		}),
	})
);

export const auditLogsRelations = relations(auditLogs, ({ one }) => ({
	user: one(users, {
		fields: [auditLogs.userId],
		references: [users.id],
	}),
}));
