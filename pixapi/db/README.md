# Drizzle ORM Database Implementation

This directory contains the Drizzle ORM implementation for the PIX API database.

## Overview

The database implementation uses Drizzle ORM with PostgreSQL to provide a type-safe and efficient database access layer. The implementation includes:

- Schema definitions with proper relations
- Migration management
- CUID2 for ID generation
- Proper schema organization using the `pixapi` schema

## Files

- `index.ts`: Main database configuration and connection setup
- `schema.ts`: Database schema definitions with tables, relations, and types

## Usage

### Querying Data

```typescript
import { db, users, transactions } from '../db';

// Select all users
const allUsers = await db.select().from(users);

// Select with conditions
const user = await db.select().from(users).where(users.email === '<EMAIL>');

// Join with relations
const userWithTransactions = await db
  .select()
  .from(users)
  .leftJoin(transactions, transactions.userId === users.id)
  .where(users.id === userId);
```

### Inserting Data

```typescript
import { db, users } from '../db';
import { generateId } from '../utils/id';

// Insert a new user
const newUser = await db.insert(users)
  .values({
    id: generateId(),
    email: '<EMAIL>',
    name: 'User Name',
    partnerId: 'flow2pay',
  })
  .returning();
```

### Updating Data

```typescript
import { db, users } from '../db';

// Update a user
await db.update(users)
  .set({ name: 'New Name' })
  .where(users.id === userId);
```

### Deleting Data

```typescript
import { db, users } from '../db';

// Delete a user
await db.delete(users)
  .where(users.id === userId);
```

## Migrations

Migrations are managed using Drizzle Kit. The following npm scripts are available:

### First-time Setup

For first-time setup or when initializing a new database, use:

```bash
npm run db:init
```

This will:
1. Create the necessary directory structure
2. Initialize the Drizzle migration tracking system
3. Create the `pixapi` schema in the database
4. Generate initial migration files based on your schema

### Regular Migration Workflow

After the initial setup, use these commands for your regular migration workflow:

- `npm run db:generate`: Generate new migrations based on schema changes
- `npm run db:migrate`: Apply pending migrations to the database
- `npm run db:studio`: Open Drizzle Studio to view and manage database data
- `npm run db:push`: Push schema changes directly to the database (development only)

The `db:migrate` script has been updated to automatically handle the case when the migration tracking system hasn't been initialized yet.

## ID Generation

All tables use CUID2 for ID generation instead of numeric IDs or UUIDs. The `generateId()` function from `../utils/id` should be used when creating new records.

## Schema Organization

All tables are created in the `pixapi` schema instead of the default `public` schema. This is enforced in the schema definitions and migration scripts.

## Admin User Creation

To create an admin user, use the following command:

```bash
npm run admin:create -- --email <EMAIL> --name "Admin User" --partner-id flow2pay
```

This will create a new admin user and generate an API key for them.

## Docker Build and Deployment

To build and deploy the Docker image, use the following command:

```bash
npm run build:docker
```

This will:
1. Apply any pending Drizzle migrations
2. Build the Docker image with an automatically incremented tag
3. Push the image to Docker Hub
4. Update the docker-compose.prod.yaml file with the new tag
