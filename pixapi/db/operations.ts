import { db } from '../database';
import {
	eq,
	and,
	desc,
	asc,
	gte,
	lte,
	like,
	isNull,
	isNotNull,
	sql,
} from 'drizzle-orm';
import * as schema from './schema';
import log from 'encore.dev/log';

// User operations
export const userOperations = {
	// Get user by ID
	async getById(id: string) {
		return await db.query.users.findFirst({
			where: eq(schema.users.id, id),
		});
	},

	// Get user by email
	async getByEmail(email: string) {
		return await db.query.users.findFirst({
			where: eq(schema.users.email, email),
		});
	},

	// Create a new user
	async create(data: { email: string; name: string; partnerId: string }) {
		const result = await db
			.insert(schema.users)
			.values({
				email: data.email,
				name: data.name,
				partnerId: data.partnerId,
			})
			.returning({ id: schema.users.id });

		return result[0];
	},

	// List users with pagination
	async list(page: number = 1, limit: number = 50) {
		const offset = (page - 1) * limit;

		const users = await db.query.users.findMany({
			limit,
			offset,
			orderBy: [desc(schema.users.createdAt)],
		});

		const countResult = await db
			.select({ count: sql<number>`count(*)` })
			.from(schema.users);

		const total = countResult[0]?.count || 0;

		return { users, total, page, limit };
	},
};

// QR code operations
export const qrCodeOperations = {
	// Get QR code by ID
	async getById(id: string) {
		return await db.query.qrCodes.findFirst({
			where: eq(schema.qrCodes.id, id),
		});
	},

	// Get QR code by txid
	async getByTxid(txid: string) {
		return await db.query.qrCodes.findFirst({
			where: eq(schema.qrCodes.txid, txid),
		});
	},

	// Create a new QR code
	async create(data: {
		userId: string | null;
		txid: string;
		amount: number;
		description?: string;
		expirationTime: number;
		qrCodeImage?: string;
		qrCodeText: string;
		status: string;
		partnerId?: string;
	}) {
		const result = await db
			.insert(schema.qrCodes)
			.values({
				userId: data.userId,
				txid: data.txid,
				amount: data.amount,
				description: data.description,
				expirationTime: data.expirationTime,
				qrCodeImage: data.qrCodeImage,
				qrCodeText: data.qrCodeText,
				status: data.status,
				partnerId: data.partnerId,
			})
			.returning({ id: schema.qrCodes.id });

		return result[0];
	},

	// Update QR code status
	async updateStatus(id: string, status: string) {
		await db
			.update(schema.qrCodes)
			.set({
				status,
				updatedAt: new Date(),
			})
			.where(eq(schema.qrCodes.id, id));
	},

	// Find expired QR codes
	async findExpired() {
		const now = new Date();

		return await db.query.qrCodes.findMany({
			where: and(
				eq(schema.qrCodes.status, 'pending'),
				lte(
					sql`${schema.qrCodes.createdAt} + (${schema.qrCodes.expirationTime} * interval '1 second')`,
					now
				)
			),
		});
	},
};

// Transaction operations
export const transactionOperations = {
	// Get transaction by ID
	async getById(id: string) {
		return await db.query.transactions.findFirst({
			where: eq(schema.transactions.id, id),
		});
	},

	// Get transaction by Flow2Pay txid
	async getByTxid(txid: string) {
		return await db.query.transactions.findFirst({
			where: eq(schema.transactions.flow2payTxid, txid),
		});
	},

	// Get transaction by Flow2Pay idEnvio
	async getByIdEnvio(idEnvio: string) {
		return await db.query.transactions.findFirst({
			where: eq(schema.transactions.flow2payIdEnvio, idEnvio),
		});
	},

	// Get transaction by Flow2Pay endToEndId
	async getByEndToEndId(endToEndId: string) {
		return await db.query.transactions.findFirst({
			where: eq(schema.transactions.flow2payEndToEndId, endToEndId),
		});
	},

	// Update transaction status
	async updateStatus(id: string, status: string) {
		await db
			.update(schema.transactions)
			.set({
				status,
				updatedAt: new Date(),
			})
			.where(eq(schema.transactions.id, id));
	},

	// Update transaction webhook data
	async updateWebhookData(
		id: string,
		endToEndId?: string,
		webhookPayload?: any
	) {
		const updateData: any = {
			updatedAt: new Date(),
		};

		if (endToEndId) {
			updateData.flow2payEndToEndId = endToEndId;
		}

		if (webhookPayload) {
			updateData.webhookPayload = webhookPayload;
		}

		await db
			.update(schema.transactions)
			.set(updateData)
			.where(eq(schema.transactions.id, id));
	},

	// Create a new transaction
	async create(data: {
		userId: string;
		qrCodeId?: string;
		type: string;
		amount: number;
		status: string;
		description?: string;
		flow2payTxid?: string;
		flow2payEndToEndId?: string;
		flow2payIdEnvio?: string;
		webhookPayload?: any;
		createdAt?: Date;
	}) {
		const result = await db
			.insert(schema.transactions)
			.values({
				userId: data.userId,
				qrCodeId: data.qrCodeId,
				type: data.type,
				amount: data.amount,
				status: data.status,
				description: data.description,
				flow2payTxid: data.flow2payTxid,
				flow2payEndToEndId: data.flow2payEndToEndId,
				flow2payIdEnvio: data.flow2payIdEnvio,
				webhookPayload: data.webhookPayload,
				createdAt: data.createdAt || new Date(),
			})
			.returning({ id: schema.transactions.id });

		return result[0];
	},

	// List transactions with filtering and pagination
	async list(params: {
		userId: string;
		type?: string;
		status?: string;
		startDate?: Date;
		endDate?: Date;
		page?: number;
		pageSize?: number;
	}) {
		const {
			userId,
			type,
			status,
			startDate,
			endDate,
			page = 1,
			pageSize = 50,
		} = params;
		const offset = (page - 1) * pageSize;

		// Build where conditions
		const whereConditions = [eq(schema.transactions.userId, userId)];

		if (type) {
			whereConditions.push(eq(schema.transactions.type, type));
		}

		if (status) {
			whereConditions.push(eq(schema.transactions.status, status));
		}

		if (startDate) {
			whereConditions.push(gte(schema.transactions.createdAt, startDate));
		}

		if (endDate) {
			whereConditions.push(lte(schema.transactions.createdAt, endDate));
		}

		// Get transactions
		const transactions = await db.query.transactions.findMany({
			where: and(...whereConditions),
			orderBy: [desc(schema.transactions.createdAt)],
			limit: pageSize,
			offset,
		});

		// Get total count
		const countResult = await db
			.select({ count: sql<number>`count(*)` })
			.from(schema.transactions)
			.where(and(...whereConditions));

		const totalCount = countResult[0]?.count || 0;

		return { transactions, totalCount, page, pageSize };
	},
};

// API key operations
export const apiKeyOperations = {
	// Get API key by key
	async getByKey(apiKey: string) {
		return await db.query.apiKeys.findFirst({
			where: eq(schema.apiKeys.apiKey, apiKey),
		});
	},

	// Create a new API key
	async create(data: {
		userId: string;
		apiKey: string;
		name: string;
		permissions: any;
		description?: string;
		expiresAt?: Date;
	}) {
		const result = await db
			.insert(schema.apiKeys)
			.values({
				userId: data.userId,
				apiKey: data.apiKey,
				name: data.name,
				permissions: data.permissions,
				description: data.description,
				expiresAt: data.expiresAt,
			})
			.returning();

		return result[0];
	},

	// Record API key usage
	async recordUsage(
		apiKeyId: string,
		endpoint: string,
		ipAddress?: string,
		userAgent?: string
	) {
		// Insert usage record
		await db.insert(schema.apiKeyUsage).values({
			apiKeyId: apiKeyId,
			endpoint,
			ipAddress,
			userAgent,
		});

		// Update last used timestamp and usage count
		await db
			.update(schema.apiKeys)
			.set({
				lastUsedAt: new Date(),
				usageCount: sql`${schema.apiKeys.usageCount} + 1`,
			})
			.where(eq(schema.apiKeys.id, apiKeyId));
	},

	// List API keys for a user
	async listForUser(userId: string) {
		return await db.query.apiKeys.findMany({
			where: eq(schema.apiKeys.userId, userId),
			orderBy: [desc(schema.apiKeys.createdAt)],
		});
	},

	// Revoke (deactivate) an API key
	async revokeKey(apiKeyId: string, userId: string): Promise<boolean> {
		try {
			const result = await db
				.update(schema.apiKeys)
				.set({
					isActive: false,
					updatedAt: new Date(),
				})
				.where(
					and(
						eq(schema.apiKeys.id, apiKeyId),
						eq(schema.apiKeys.userId, userId)
					)
				)
				.returning({ id: schema.apiKeys.id });

			return result.length > 0;
		} catch (error) {
			log.error('Error revoking API key', {
				error: error instanceof Error ? error.message : 'Unknown error',
				apiKeyId,
				userId,
			});
			return false;
		}
	},
};

// Webhook operations
export const webhookOperations = {
	// Store Flow2Pay webhook
	async storeFlow2PayWebhook(data: {
		eventType: string;
		txid?: string;
		idEnvio?: string;
		endToEndId?: string;
		valor: number;
		status: string;
		payload: any;
	}) {
		const result = await db
			.insert(schema.flow2payWebhooks)
			.values({
				eventType: data.eventType,
				txid: data.txid,
				idEnvio: data.idEnvio,
				endToEndId: data.endToEndId,
				valor: data.valor,
				status: data.status,
				payload: data.payload,
			})
			.returning({ id: schema.flow2payWebhooks.id });

		return result[0];
	},

	// Mark Flow2Pay webhook as processed
	async markFlow2PayWebhookProcessed(conditions: {
		eventType: string;
		txid?: string;
		idEnvio?: string;
		endToEndId?: string;
	}) {
		const { eventType, txid, idEnvio, endToEndId } = conditions;
		const whereConditions = [eq(schema.flow2payWebhooks.eventType, eventType)];

		if (txid) {
			whereConditions.push(eq(schema.flow2payWebhooks.txid, txid));
		}

		if (idEnvio) {
			whereConditions.push(eq(schema.flow2payWebhooks.idEnvio, idEnvio));
		}

		if (endToEndId) {
			whereConditions.push(eq(schema.flow2payWebhooks.endToEndId, endToEndId));
		}

		await db
			.update(schema.flow2payWebhooks)
			.set({
				processed: true,
				processedAt: new Date(),
			})
			.where(
				and(...whereConditions, eq(schema.flow2payWebhooks.processed, false))
			);
	},

	// Update webhook publication status for Svix
	async updateWebhookPublicationStatus(data: {
		eventType: string;
		idField?: string;
		endToEndId?: string;
		messageId: string;
	}) {
		const { eventType, idField, endToEndId, messageId } = data;
		const whereConditions = [eq(schema.flow2payWebhooks.eventType, eventType)];

		if (idField) {
			// Check if idField is a txid or idEnvio
			whereConditions.push(
				sql`(${schema.flow2payWebhooks.txid} = ${idField} OR ${schema.flow2payWebhooks.idEnvio} = ${idField})`
			);
		}

		if (endToEndId) {
			whereConditions.push(eq(schema.flow2payWebhooks.endToEndId, endToEndId));
		}

		await db
			.update(schema.flow2payWebhooks)
			.set({
				publishedToSvix: true,
				svixMessageId: messageId,
				publishedAt: new Date(),
			})
			.where(and(...whereConditions));
	},
};

// Audit log operations
export const auditLogOperations = {
	// Create an audit log entry
	async create(data: {
		userId?: string;
		action: string;
		entityType: string;
		entityId?: string;
		ipAddress?: string;
		userAgent?: string;
		details?: any;
	}) {
		await db.insert(schema.auditLogs).values({
			userId: data.userId,
			action: data.action,
			entityType: data.entityType,
			entityId: data.entityId,
			ipAddress: data.ipAddress,
			userAgent: data.userAgent,
			details: data.details,
		});
	},
};
