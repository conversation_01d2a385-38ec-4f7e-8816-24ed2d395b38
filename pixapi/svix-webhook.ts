import { api } from 'encore.dev/api';
import log from 'encore.dev/log';
import { secret } from 'encore.dev/config';
import { verifySvixSignature } from './svix';
import { operations } from './db';
import { processWebhook, Flow2PayWebhookPayload } from './webhooks';
import { forwardToPluggou, trackEventProcessing } from './webhook-forwarder';

// Svix webhook secret for verification
const SVIX_WEBHOOK_SECRET = secret('SVIX_WEBHOOK_SECRET');

/**
 * Svix webhook handler
 * This endpoint receives webhooks from Svix and processes them
 */
export const handleSvixWebhook = api.raw(
	{ method: 'POST', path: '/svix-webhook', expose: true },
	async (req, resp) => {
		const logger = log.with({ service: 'svix-webhook' });
		logger.info('Received Svix webhook   ============>>>>>');

		try {
			// Get Svix signature headers
			const svixId = req.headers['svix-id'] as string;
			const svixTimestamp = req.headers['svix-timestamp'] as string;
			const svixSignature = req.headers['svix-signature'] as string;

			console.log('Svix headers ===========>>>>>>>:', {
				svixId,
				svixTimestamp,
				svixSignature,
			});

			// Validate required headers
			if (!svixId || !svixTimestamp || !svixSignature) {
				logger.error('Missing Svix signature headers', {
					svixId,
					svixTimestamp,
					svixSignature,
				});
				resp.writeHead(400, { 'Content-Type': 'application/json' });
				resp.end(
					JSON.stringify({
						success: false,
						error: 'Missing Svix signature headers',
					})
				);
				return;
			}

			// Parse the request body
			let body = '';
			for await (const chunk of req) {
				body += chunk.toString();
			}

			if (!body) {
				logger.warn('Empty webhook payload received');
				resp.writeHead(400, { 'Content-Type': 'application/json' });
				resp.end(JSON.stringify({ success: false, error: 'Empty payload' }));
				return;
			}

			// Verify the Svix signature
			const isValid = verifySvixSignature(
				body,
				svixSignature,
				svixTimestamp,
				SVIX_WEBHOOK_SECRET(),
				svixId
			);

			if (!isValid) {
				logger.error('Invalid Svix signature');
				resp.writeHead(401, { 'Content-Type': 'application/json' });
				resp.end(
					JSON.stringify({
						success: false,
						error: 'Invalid signature',
					})
				);
				return;
			}

			// Parse the JSON payload
			let payload;
			try {
				payload = JSON.parse(body);
			} catch (error) {
				logger.error('Invalid JSON in webhook payload', { body });
				resp.writeHead(400, { 'Content-Type': 'application/json' });
				resp.end(
					JSON.stringify({ success: false, error: 'Invalid JSON payload' })
				);
				return;
			}

			// Extract the Flow2Pay webhook payload from the Svix message
			const flow2payPayload = payload.payload?.webhook_payload;

			if (!flow2payPayload) {
				logger.error('Missing Flow2Pay payload in Svix webhook', { payload });
				resp.writeHead(400, { 'Content-Type': 'application/json' });
				resp.end(
					JSON.stringify({
						success: false,
						error: 'Missing Flow2Pay payload',
					})
				);
				return;
			}

			// Process the webhook asynchronously
			Promise.all([
				// Store in database
				storeWebhook(flow2payPayload, payload.payload?.user_id),

				// Process the webhook (update transaction records)
				processWebhook(flow2payPayload).catch((err) => {
					logger.error('Error processing webhook from Svix', {
						error: err.message,
						event_type: flow2payPayload.evento,
					});
				}),

				// Forward to Pluggou (if needed)
				forwardToPluggou({
					event_type: flow2payPayload.evento,
					user_id: payload.payload?.user_id || '0',
					amount: Math.abs(flow2payPayload.valor),
					flow2pay_id: flow2payPayload.txid || flow2payPayload.idEnvio,
					end_to_end_id: flow2payPayload.endToEndId,
					webhook_payload: flow2payPayload,
					timestamp: flow2payPayload.horario,
					status: flow2payPayload.status || 'Sucesso',
					event_id: payload.payload?.event_id || `svix-${Date.now()}`,
					published_at: new Date().toISOString(),
				}).catch((err) => {
					logger.error('Error forwarding webhook to app.pluggou.io', {
						error: err.message,
						event_type: flow2payPayload.evento,
					});
				}),
			])
				.then(() => {
					logger.info('Webhook processing completed successfully', {
						event_type: flow2payPayload.evento,
					});
				})
				.catch((err) => {
					logger.error('Error in webhook processing', {
						error: err.message,
					});
				});

			// Return success immediately
			resp.writeHead(200, { 'Content-Type': 'application/json' });
			resp.end(JSON.stringify({ success: true }));
		} catch (error) {
			logger.error('Error handling Svix webhook', {
				error: error instanceof Error ? error.message : 'Unknown error',
			});
			resp.writeHead(500, { 'Content-Type': 'application/json' });
			resp.end(
				JSON.stringify({
					success: false,
					error: error instanceof Error ? error.message : 'Unknown error',
				})
			);
		}
	}
);

/**
 * Store a Flow2Pay webhook in the database
 */
async function storeWebhook(
	payload: Flow2PayWebhookPayload,
	userId?: string
): Promise<void> {
	const logger = log.with({
		action: 'store_webhook',
		event_type: payload.evento,
		user_id: userId
	});

	try {
		await operations.webhookOperations.storeFlow2PayWebhook({
			eventType: payload.evento,
			txid: payload.evento === 'PixIn' ? payload.txid : undefined,
			idEnvio: payload.evento === 'PixOut' ? payload.idEnvio : undefined,
			endToEndId: payload.endToEndId,
			valor: payload.valor,
			status: payload.status || 'Sucesso',
			payload,
		});
		logger.info('Svix webhook stored in database successfully');
	} catch (dbError) {
		logger.error('Error storing Svix webhook in database', {
			error: dbError instanceof Error ? dbError.message : 'Unknown error',
			event_type: payload.evento,
		});
		throw dbError;
	}
}
