import log from 'encore.dev/log';

// Note: Event tracking has been removed in proxy mode since we don't use database operations

/**
 * Default webhook destination for app.pluggou.io
 */
const DEFAULT_WEBHOOK_URL = 'https://app.pluggou.io/api/webhooks/pluggou-pix';

// Flow2Pay webhook payload interface for proxy mode
interface Flow2PayWebhookPayload {
	evento: string;
	status?: string;
	valor: number;
	horario: string;
	txid?: string;
	idEnvio?: string;
	endToEndId?: string;
	token?: string;
	pagador?: any;
	recebedor?: any;
	dataHora?: string;
	erro?: string;
}

/**
 * Forward a Flow2Pay webhook payload to app.pluggou.io (Proxy Mode)
 */
export async function forwardToPluggou(
	payload: Flow2PayWebhookPayload,
	webhookUrl: string
): Promise<void> {
	const logger = log.with({
		action: 'forward_to_pluggou_proxy',
		event_type: payload.evento,
		flow2pay_id: payload.txid || payload.idEnvio,
	});

	try {
		// Map status to Pluggou status (including the 'Em processamento' -> 'processing' mapping)
		let pluggouStatus = 'failed';

		// Check for different status formats
		if (payload.status === 'Sucesso') {
			pluggouStatus = 'completed';
		} else if (payload.status === 'Em processamento') {
			// This is the key mapping mentioned in the conversation history
			pluggouStatus = 'processing';
		} else if (payload.status === 'Falha' || payload.status === 'Erro') {
			pluggouStatus = 'failed';
		} else {
			// Default to processing for unknown statuses that aren't explicitly failures
			pluggouStatus =
				payload.status?.toLowerCase().includes('falha') ||
				payload.status?.toLowerCase().includes('erro') ||
				payload.status?.toLowerCase().includes('fail')
					? 'failed'
					: 'processing';
		}

		// Log the status mapping for debugging
		logger.info('Mapping status for webhook in proxy mode', {
			original_status: payload.status,
			mapped_status: pluggouStatus,
			event_type: payload.evento,
		});

		// Create the webhook payload for Pluggou
		const webhookPayload = {
			event: mapEventTypeForWebhooks(payload.evento),
			user_id: 'proxy_user', // Default user in proxy mode
			amount: Math.abs(payload.valor),
			flow2pay_id: payload.txid || payload.idEnvio,
			end_to_end_id: payload.endToEndId,
			timestamp: payload.horario || new Date().toISOString(),
			status: pluggouStatus, // Use mapped status
			data: payload, // Include the original Flow2Pay payload
		};

		logger.info('Forwarding to app.pluggou.io in proxy mode', {
			event_type: payload.evento,
			amount: Math.abs(payload.valor),
			status: pluggouStatus,
			destination: webhookUrl,
		});

		// Send the webhook
		const response = await fetch(webhookUrl, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'X-Source': 'pix-api-proxy',
				'X-Event-Type': payload.evento,
			},
			body: JSON.stringify(webhookPayload),
		});

		// Check if the response is OK
		if (response.ok) {
			// Log successful delivery
			logger.info(
				'Webhook delivered to app.pluggou.io successfully in proxy mode',
				{
					status_code: response.status,
					event_type: payload.evento,
				}
			);
		} else {
			// Handle failed delivery
			const responseText = await response.text();
			logger.error(
				'Failed to deliver webhook to app.pluggou.io in proxy mode',
				{
					status_code: response.status,
					response: responseText,
					event_type: payload.evento,
				}
			);
		}
	} catch (error) {
		// Log the overall error
		logger.error('Error in forwardToPluggou proxy mode', {
			error: error instanceof Error ? error.message : 'Unknown error',
			event_type: payload.evento,
		});
	}
}

/**
 * Map Flow2Pay event types to internal event types for better compatibility
 * @param eventType Flow2Pay event type
 * @returns Mapped event type for webhooks
 */
export function mapEventTypeForWebhooks(eventType: string): string {
	switch (eventType) {
		case 'PixIn':
			return 'pix_in';
		case 'PixOut':
			return 'pix_out';
		case 'PixInReversal':
			return 'pix_in_reversal';
		case 'PixOutReversalExternal':
			return 'pix_out_reversal';
		default:
			return eventType.toLowerCase();
	}
}
