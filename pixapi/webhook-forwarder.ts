import log from 'encore.dev/log';
import { db } from './database';
import { PixEvent, TRANSACTION_STATUS } from './events';
import { eq } from 'drizzle-orm';
import { flow2payWebhooks } from './db/schema';

/**
 * Track event processing using the flow2payWebhooks table
 *
 * The flow2payWebhooks table has the following structure:
 * - id: Primary key (CUID)
 * - eventType: Event type ('PixIn', 'PixOut', etc.)
 * - txid: Transaction ID for PixIn events
 * - idEnvio: ID for PixOut events
 * - endToEndId: End-to-end ID for all events
 * - valor: Amount in cents
 * - status: Status ('Sucesso', 'Falha', etc.)
 * - payload: Full webhook payload
 * - processed: Whether the webhook has been processed
 * - processedAt: Timestamp when the webhook was processed
 * - errorMessage: Optional error message if processing failed
 */

/**
 * Track event processing in the database
 * @param event The event being processed
 * @param status The processing status
 * @param error Optional error message
 */
export async function trackEventProcessing(
	eventType: string,
	idField: string | undefined, // txid or idEnvio
	endToEndId: string | undefined,
	_status: string, // Status from the event, not used directly but kept for API compatibility
	success: boolean,
	errorMessage?: string
): Promise<void> {
	const logger = log.with({
		action: 'track_event_processing',
		event_type: eventType,
		id_field: idField,
		end_to_end_id: endToEndId,
		success,
	});

	try {
		// We need to find webhook records with matching identifiers
		let webhooks;

		if (idField) {
			// For PixIn events
			if (eventType === 'PixIn') {
				webhooks = await db
					.select()
					.from(flow2payWebhooks)
					.where(eq(flow2payWebhooks.txid, idField))
					.execute();
			}
			// For PixOut events
			else if (eventType === 'PixOut') {
				webhooks = await db
					.select()
					.from(flow2payWebhooks)
					.where(eq(flow2payWebhooks.idEnvio, idField))
					.execute();
			}
		} else if (endToEndId) {
			// If we only have endToEndId
			webhooks = await db
				.select()
				.from(flow2payWebhooks)
				.where(eq(flow2payWebhooks.endToEndId, endToEndId))
				.execute();
		} else {
			// We don't have any identifier to track with
			logger.warn('No identifier to track event processing');
			return;
		}

		// If a webhook with the given ID(s) exists, update its status
		if (webhooks && webhooks.length > 0) {
			const webhookId = webhooks[0].id;

			// Update the webhook status
			const updateData: any = {
				processed: true,
				processedAt: new Date(),
				status: success ? 'completed' : 'failed',
			};

			// Only add error message if it exists
			if (errorMessage) {
				// Store error message in a field that exists in the schema
				// Assuming we have a 'payload' field that can store JSON
				const existingPayload = webhooks[0].payload || {};
				updateData.payload = {
					...(typeof existingPayload === 'object' ? existingPayload : {}),
					error: errorMessage,
				};
			}

			await db
				.update(flow2payWebhooks)
				.set(updateData)
				.where(eq(flow2payWebhooks.id, webhookId));

			logger.info('Event tracking updated successfully', {
				webhook_id: webhookId,
				success,
			});
		} else {
			logger.warn('No webhook found to track event processing', {
				event_type: eventType,
				id_field: idField,
				end_to_end_id: endToEndId,
			});
		}
	} catch (error) {
		logger.error('Failed to track event processing', {
			error: error instanceof Error ? error.message : 'Unknown error',
			event_type: eventType,
			id_field: idField,
			end_to_end_id: endToEndId,
		});
	}
}

/**
 * Default webhook destination for app.pluggou.io
 */
const DEFAULT_WEBHOOK_URL = 'https://app.pluggou.io/api/webhooks/pluggou-pix';

/**
 * Forward a PixEvent to app.pluggou.io
 */
export async function forwardToPluggou(event: PixEvent): Promise<void> {
	const logger = log.with({
		action: 'forward_to_pluggou',
		event_type: event.event_type,
		user_id: event.user_id,
		flow2pay_id: event.flow2pay_id,
	});

	try {
		// Map status to Pluggou status
		let pluggouStatus = 'failed';

		// Check for different status formats
		if (
			event.status === 'Sucesso' ||
			event.status === 'completed' ||
			event.status === TRANSACTION_STATUS.COMPLETED
		) {
			pluggouStatus = 'completed';
		} else if (
			event.status === 'Em processamento' ||
			event.status === 'pending' ||
			event.status === TRANSACTION_STATUS.PENDING
		) {
			pluggouStatus = 'processing';
		}

		// Log the status mapping for debugging
		logger.info('Mapping status for webhook', {
			original_status: event.status,
			mapped_status: pluggouStatus,
			event_type: event.event_type,
		});

		// Create the webhook payload
		const payload = {
			event: mapEventTypeForWebhooks(event.event_type),
			user_id: event.user_id, // Ensure user_id is included
			amount: event.amount,
			flow2pay_id: event.flow2pay_id,
			end_to_end_id: event.end_to_end_id,
			timestamp: event.timestamp || new Date().toISOString(),
			status: pluggouStatus, // Use mapped status
			data: event.webhook_payload, // Include the original Flow2Pay payload
		};

		logger.info('Forwarding to app.pluggou.io', {
			event_type: event.event_type,
			amount: event.amount,
			user_id: event.user_id,
			status: pluggouStatus,
		});

		// Log a unique identifier for this webhook delivery for tracing
		const traceId = `${event.event_type}-${Date.now()}-${Math.random()
			.toString(36)
			.slice(2, 9)}`;
		logger.debug('Generated trace ID for webhook delivery', {
			trace_id: traceId,
		});

		try {
			// Send the webhook directly without logging to avoid foreign key issues
			const response = await fetch(DEFAULT_WEBHOOK_URL, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(payload),
			});

			// Check if the response is OK
			if (response.ok) {
				// Log successful delivery
				logger.info('Webhook delivered to app.pluggou.io successfully', {
					status_code: response.status,
					user_id: event.user_id,
				});

				// Track successful processing
				await trackEventProcessing(
					event.event_type,
					event.flow2pay_id,
					event.end_to_end_id,
					event.status || 'Sucesso',
					true
				);
			} else {
				// Handle failed delivery
				const responseText = await response.text();
				const errorMessage = `HTTP ${response.status}: ${responseText}`;

				logger.error('Failed to deliver webhook to app.pluggou.io', {
					status_code: response.status,
					response: responseText,
					user_id: event.user_id,
				});

				// Track failed processing
				await trackEventProcessing(
					event.event_type,
					event.flow2pay_id,
					event.end_to_end_id,
					event.status || 'Falha',
					false,
					errorMessage
				);
			}
		} catch (fetchError) {
			// Handle network errors
			const errorMessage =
				fetchError instanceof Error
					? fetchError.message
					: 'Unknown network error';

			logger.error('Network error delivering webhook to app.pluggou.io', {
				error: errorMessage,
				user_id: event.user_id,
			});

			// Track failed processing
			await trackEventProcessing(
				event.event_type,
				event.flow2pay_id,
				event.end_to_end_id,
				event.status || 'Falha',
				false,
				errorMessage
			);
		}
	} catch (error) {
		// Log the overall error
		logger.error('Error in forwardToPluggou', {
			error: error instanceof Error ? error.message : 'Unknown error',
			user_id: event.user_id,
			event_type: event.event_type,
		});

		// Try to track the error in event processing
		try {
			await trackEventProcessing(
				event.event_type,
				event.flow2pay_id,
				event.end_to_end_id,
				event.status || 'Falha',
				false,
				error instanceof Error ? error.message : 'Unknown error'
			);
		} catch (trackingError) {
			// Just log and continue if tracking itself fails
			logger.error('Error tracking webhook failure', {
				error:
					trackingError instanceof Error
						? trackingError.message
						: 'Unknown error',
			});
		}
	}
}

/**
 * Map Flow2Pay event types to internal event types for better compatibility
 * @param eventType Flow2Pay event type
 * @returns Mapped event type for webhooks
 */
export function mapEventTypeForWebhooks(eventType: string): string {
	switch (eventType) {
		case 'PixIn':
			return 'pix_in';
		case 'PixOut':
			return 'pix_out';
		case 'PixInReversal':
			return 'pix_in_reversal';
		case 'PixOutReversalExternal':
			return 'pix_out_reversal';
		default:
			return eventType.toLowerCase();
	}
}
