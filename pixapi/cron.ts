import { <PERSON>ronJob } from 'encore.dev/cron';
import { api } from 'encore.dev/api';
import { db, operations } from './db';
import log from 'encore.dev/log';
import { TRANSACTION_STATUS } from './events';
import { sendSvixMessage } from './svix';

/**
 * Handle expired QR codes
 * This job runs every 5 minutes to check for expired QR codes
 */
export const handleExpiredQRCodes = api(
	{},
	async (): Promise<{ processed: number }> => {
		const logger = log.with({ action: 'handle_expired_qr_codes' });
		logger.info('Starting expired QR codes job');

		try {
			// Find QR codes that have expired but are still pending
			const now = new Date();
			const expiredQRCodes = await operations.qrCodeOperations.findExpired();

			let processedCount = 0;
			for (const qrCode of expiredQRCodes) {
				// Update the QR code status to expired
				await operations.qrCodeOperations.updateStatus(
					qrCode.id,
					TRANSACTION_STATUS.EXPIRED
				);

				// Publish an event for the expired QR code using Svix
				await sendSvixMessage(
					'QrCode.Expired',
					{
						event_type: 'QrCodeExpired',
						user_id: qrCode?.userId || '0',
						amount: qrCode.amount,
						flow2pay_id: qrCode.txid,
						timestamp: now.toISOString(),
					},
					qrCode?.userId || '0'
				);

				processedCount++;
				logger.info('Expired QR code processed', {
					qr_code_id: qrCode.id,
					txid: qrCode.txid,
					user_id: qrCode.userId,
				});
			}

			logger.info('Expired QR codes job completed', {
				processed_count: processedCount,
			});

			return { processed: processedCount };
		} catch (error) {
			logger.error('Error processing expired QR codes', {
				error: error instanceof Error ? error.message : 'Unknown error',
			});
			throw error;
		}
	}
);

// Schedule the job to run every 5 minutes
const _ = new CronJob('expired-qr-codes', {
	title: 'Handle expired QR codes',
	schedule: '*/5 * * * *', // Every 5 minutes
	endpoint: handleExpiredQRCodes,
});
