import { api, APIError, Header } from 'encore.dev/api';
import { db, operations } from './db';
import { publishPixEvent, PIX_EVENT_TYPES, createPixEvent } from './events';
import log from 'encore.dev/log';
import { secret } from 'encore.dev/config';
import { validateAPIKey, API_PERMISSIONS } from './auth';
import crypto from 'crypto';
import { TRANSACTION_STATUS, TRANSACTION_TYPES } from './events';
import { sendSvixMessage, mapToSvixEventType } from './svix';
import { eq } from 'drizzle-orm';
import {
	flow2payWebhooks,
	webhookConfigs,
	webhookDeliveryLogs,
} from './db/schema';
import { forwardToPluggou, trackEventProcessing } from './webhook-forwarder';

// Flow2Pay event token from environment variables
const FLOW2PAY_EVENT_TOKEN = secret('FLOW2PAY_EVENT_TOKEN');

// Default webhook destination for app.pluggou.io
const DEFAULT_WEBHOOK_URL = 'https://app.pluggou.io/api/webhooks/pluggou-pix';

/**
 * Flow2Pay webhook payload interface
 */
export interface Flow2PayWebhookPayload {
	evento: 'PixIn' | 'PixOut' | 'PixInReversal' | 'PixOutReversalExternal';
	token?: string;
	txid?: string;
	idEnvio?: string;
	endToEndId?: string;
	codigoTransacao?: string;
	status?: string; // Made optional to handle PixIn events without status
	chavePix?: string;
	valor: number;
	horario: string;
	dataHora?: string; // For backward compatibility
	infoPagador?: any; // Additional field seen in the webhook
	recebedor?: {
		nome?: string;
		codigoBanco?: string;
		cpf_cnpj?: string;
	};
	pagador?: {
		nome?: string;
		chave?: string;
		tipoConta?: string;
		cpf?: string; // Additional field seen in the webhook
		codigoBanco?: string; // Additional field seen in the webhook
		cpf_cnpj?: string;
	};
	erro?: {
		origem?: string;
		motivo?: string;
		mensagem?: string;
	};
}

/**
 * Flow2Pay webhook handler for PIX events
 * This endpoint receives webhooks from Flow2Pay and processes them
 */
export const handleBaasWebhook = api.raw(
	{ method: 'POST', path: '/baas', expose: true },
	async (req, resp) => {
		const logger = log.with({ service: 'flow2pay-webhook' });
		logger.info('Received Flow2Pay webhook');

		try {
			// Parse the request body
			let body = '';
			for await (const chunk of req) {
				body += chunk.toString();
			}

			if (!body) {
				logger.warn('Empty webhook payload received');
				resp.writeHead(400, { 'Content-Type': 'application/json' });
				resp.end(JSON.stringify({ success: false, error: 'Empty payload' }));
				return;
			}

			// Log the raw webhook payload for debugging
			console.log('Raw Flow2Pay webhook payload:', body);

			// Parse the JSON payload
			let payload: Flow2PayWebhookPayload;
			try {
				payload = JSON.parse(body);
			} catch (error) {
				logger.error('Invalid JSON in webhook payload', { body });
				resp.writeHead(400, { 'Content-Type': 'application/json' });
				resp.end(
					JSON.stringify({ success: false, error: 'Invalid JSON payload' })
				);
				return;
			}

			// Validate the token if present
			if (payload.token && payload.token !== FLOW2PAY_EVENT_TOKEN()) {
				logger.error('Invalid token in webhook payload');
				resp.writeHead(401, { 'Content-Type': 'application/json' });
				resp.end(
					JSON.stringify({
						success: false,
						error: 'Invalid authentication token',
					})
				);
				return;
			}

			// Validate required fields
			if (!payload.evento) {
				logger.error('Missing evento field in webhook payload');
				resp.writeHead(400, { 'Content-Type': 'application/json' });
				resp.end(
					JSON.stringify({
						success: false,
						error: 'Missing evento field in webhook payload',
					})
				);
				return;
			}

			// Validate event type
			if (
				![
					'PixIn',
					'PixOut',
					'PixInReversal',
					'PixOutReversalExternal',
				].includes(payload.evento)
			) {
				logger.error('Invalid evento type in webhook payload', {
					evento: payload.evento,
				});
				resp.writeHead(400, { 'Content-Type': 'application/json' });
				resp.end(
					JSON.stringify({
						success: false,
						error: 'Invalid evento type in webhook payload',
					})
				);
				return;
			}

			// For PixIn events without status field, assume it's a successful payment
			if (!payload.status && payload.evento === 'PixIn') {
				logger.info('PixIn webhook without status field, assuming success');
				payload.status = 'Sucesso'; // Default to success for PixIn events
			}
			// For other event types, status is still required
			else if (!payload.status && payload.evento !== 'PixIn') {
				logger.error('Missing status field in webhook payload');
				resp.writeHead(400, { 'Content-Type': 'application/json' });
				resp.end(
					JSON.stringify({
						success: false,
						error: 'Missing status field in webhook payload',
					})
				);
				return;
			}

			// Normalize timestamp field (some payloads use dataHora, others use horario)
			if (!payload.horario && payload.dataHora) {
				payload.horario = payload.dataHora;
			}

			// Normalize ID fields (some payloads use txid, others use idEnvio)
			const idField = payload.idEnvio || payload.txid;

			// Log the received webhook
			logger.info('Received Flow2Pay webhook', {
				event_type: payload.evento,
				status: payload.status,
				id_envio: idField,
				end_to_end_id: payload.endToEndId,
				valor: payload.valor,
			});

			// Try to store the raw webhook in the database, but continue even if it fails
			try {
				await operations.webhookOperations.storeFlow2PayWebhook({
					eventType: payload.evento,
					txid: payload.evento === 'PixIn' ? idField : undefined,
					idEnvio: payload.evento === 'PixOut' ? idField : undefined,
					endToEndId: payload.endToEndId,
					valor: payload.valor,
					status: payload.status || 'Sucesso',
					payload: payload,
				});
				logger.info('Webhook stored in database successfully');
			} catch (dbError) {
				// Log the error but continue processing
				logger.error('Error storing webhook in database', {
					error: dbError instanceof Error ? dbError.message : 'Unknown error',
					event_type: payload.evento,
				});
				// If this is a table doesn't exist error, log a more helpful message
				if (
					dbError instanceof Error &&
					dbError.message.includes(
						'relation "flow2pay_webhooks" does not exist'
					)
				) {
					logger.error(
						'The flow2pay_webhooks table does not exist. Please run migrations to create it.'
					);
				}
			}

			// Send the webhook to Svix for distribution based on event type
			// This is the most important part - even if database operations fail, we still want to send to Svix
			try {
				// Determine the user_id if possible from the transaction or QR code
				let userId = '0'; // Default value as string for CUID compatibility

				// For PixIn events, try to find the QR code owner
				if (payload.evento === 'PixIn' && payload.txid) {
					const qrCode = await operations.qrCodeOperations.getByTxid(
						payload.txid
					);
					if (qrCode) {
						userId = qrCode.userId;
						logger.info('Found user_id from QR code', {
							user_id: userId,
							txid: payload.txid,
						});
					} else {
						logger.warn('QR code not found for PixIn event', {
							txid: payload.txid,
						});
					}
				}

				// For PixOut events, try to find the transaction owner
				if (payload.evento === 'PixOut' && payload.idEnvio) {
					const transaction =
						await operations.transactionOperations.getByIdEnvio(
							payload.idEnvio
						);
					if (transaction) {
						userId = transaction.userId;
						logger.info('Found user_id from transaction', {
							user_id: userId,
							id_envio: payload.idEnvio,
						});
					} else {
						logger.warn('Transaction not found for PixOut event', {
							id_envio: payload.idEnvio,
						});
					}
				}

				// For reversal events, try to find the original transaction owner
				if (
					(payload.evento === 'PixInReversal' ||
						payload.evento === 'PixOutReversalExternal') &&
					payload.endToEndId
				) {
					const originalTransaction =
						await operations.transactionOperations.getByEndToEndId(
							payload.endToEndId
						);
					if (originalTransaction) {
						userId = originalTransaction.userId;
						logger.info('Found user_id from original transaction', {
							user_id: userId,
							end_to_end_id: payload.endToEndId,
						});
					} else {
						logger.warn('Original transaction not found for reversal event', {
							end_to_end_id: payload.endToEndId,
						});
					}
				}

				// Map Flow2Pay event type to Svix event type
				const svixEventType = mapToSvixEventType(
					payload.evento,
					payload.status
				);

				// Prepare the payload for Svix
				const svixPayload = {
					event_type: payload.evento,
					flow2pay_event_type: payload.evento,
					svix_event_type: svixEventType,
					user_id: userId,
					amount: Math.abs(payload.valor),
					flow2pay_id: idField,
					end_to_end_id: payload.endToEndId,
					webhook_payload: payload,
					timestamp: payload.horario,
					status: payload.status || 'Sucesso',
					event_id: `${payload.evento}-${
						idField || payload.endToEndId || Date.now()
					}`,
				};

				// Send the message to Svix
				const messageId = await sendSvixMessage(
					svixEventType,
					svixPayload,
					userId
				);

				// Store the event publication in the database for tracking
				try {
					await operations.webhookOperations.updateWebhookPublicationStatus({
						eventType: payload.evento,
						idField,
						endToEndId: payload.endToEndId,
						messageId,
					});
				} catch (dbError) {
					// Log the error but don't fail the webhook processing
					logger.error('Error updating webhook publication status', {
						error: dbError instanceof Error ? dbError.message : 'Unknown error',
						event_type: payload.evento,
						id_field: idField,
					});
				}

				logger.info('Webhook sent to Svix successfully', {
					event_type: payload.evento,
					svix_event_type: svixEventType,
					user_id: userId,
					id_field: idField,
					message_id: messageId,
				});
			} catch (svixError) {
				logger.error('Error sending webhook to Svix', {
					error:
						svixError instanceof Error ? svixError.message : 'Unknown error',
					event_type: payload.evento,
				});
				// Log the error but continue processing - we're temporarily bypassing Svix
				logger.warn(
					'Continuing webhook processing despite Svix error (temporary bypass)'
				);
				// Don't re-throw the error since we're temporarily bypassing Svix

				// Since we're bypassing Svix, we'll let the processWebhook function handle the forwarding
				// This ensures the status is properly processed before forwarding
				logger.info(
					'Svix bypassed, webhook will be processed and forwarded after status determination',
					{
						event_type: payload.evento,
						id_field: idField,
						original_status: payload.status,
					}
				);
			}

			// Original comment: Não chamamos forwardWebhookToPluggou diretamente aqui
			// O encaminhamento será feito pelo subscriber 'webhook-forwarding-handler'
			// Updated: Now we're forwarding directly when Svix is bypassed

			// Process the webhook asynchronously
			processWebhook(payload).catch((err) => {
				logger.error('Error processing webhook', {
					error: err.message,
					event_type: payload.evento,
				});
			});

			// Return success immediately
			resp.writeHead(200, { 'Content-Type': 'application/json' });
			resp.end(JSON.stringify({ success: true }));
		} catch (error) {
			logger.error('Error handling webhook', {
				error: error instanceof Error ? error.message : 'Unknown error',
			});
			resp.writeHead(500, { 'Content-Type': 'application/json' });
			resp.end(
				JSON.stringify({
					success: false,
					error: error instanceof Error ? error.message : 'Unknown error',
				})
			);
		}
	}
);

/**
 * Process a webhook payload from Flow2Pay
 * @param payload Flow2Pay webhook payload
 */
export async function processWebhook(
	payload: Flow2PayWebhookPayload
): Promise<void> {
	// Extract common fields for logging
	const endToEndId = payload.endToEndId;
	const txid = payload.txid;
	const idEnvio = payload.idEnvio;
	const value = payload.valor;
	const status = payload.status || 'Sucesso'; // Default to success if not provided
	const evento = payload.evento;

	// Use either txid or idEnvio as the ID field for logging
	const idField = txid || idEnvio || '';

	const logger = log.with({
		end_to_end_id: endToEndId,
		event_type: evento,
		id_field: idField,
		status,
	});

	logger.info('Processing Flow2Pay webhook');

	try {
		// Handle different event types
		switch (evento) {
			case 'PixIn':
				await handlePixIn(payload);
				break;
			case 'PixOut':
				await handlePixOut(payload);
				break;
			case 'PixInReversal':
				await handlePixInReversal(payload);
				break;
			case 'PixOutReversalExternal':
				await handlePixOutReversalExternal(payload);
				break;
			default:
				logger.warn('Unknown event type received', { tipo: evento });
				throw new Error(`Unknown event type: ${evento}`);
		}
	} catch (error) {
		logger.error('Error processing webhook', {
			error: error instanceof Error ? error.message : 'Unknown error',
			event_type: evento,
			id_field: idField,
		});
	}
}

/**
 * Handle PixIn event (received payment)
 * @param payload Flow2Pay webhook payload
 */
async function handlePixIn(payload: Flow2PayWebhookPayload): Promise<void> {
	const idField = payload.idEnvio || payload.txid;
	const logger = log.with({ event_type: 'PixIn', id_field: idField });

	if (!idField) {
		logger.error('Missing ID field in PixIn event');
		throw new Error('Missing ID field in PixIn event');
	}

	// Find the QR code based on txid using Drizzle operations
	const qrCode = await operations.qrCodeOperations.getByTxid(idField);

	if (!qrCode) {
		logger.warn('QR code not found for PixIn event', { txid: idField });
		// We'll still record the transaction for reconciliation
		await recordUnknownPixIn(payload);
		return;
	}

	// For PixIn events, if status is missing, assume success
	if (!payload.status) {
		payload.status = 'Sucesso';
		logger.info('Assuming success for PixIn event without status field', {
			txid: idField,
		});
	}

	// Map Flow2Pay status to internal status
	let newStatus: string;
	switch (payload.status) {
		case 'Sucesso':
			newStatus = TRANSACTION_STATUS.COMPLETED;
			break;
		case 'Em processamento':
			newStatus = TRANSACTION_STATUS.PENDING;
			break;
		case 'Falha':
		case 'Erro':
			newStatus = TRANSACTION_STATUS.FAILED;
			break;
		default:
			// For unknown statuses, default to pending if it's not explicitly a failure
			newStatus =
				payload.status?.toLowerCase().includes('falha') ||
				payload.status?.toLowerCase().includes('erro') ||
				payload.status?.toLowerCase().includes('fail')
					? TRANSACTION_STATUS.FAILED
					: TRANSACTION_STATUS.PENDING;
			break;
	}

	const isSuccess = newStatus === TRANSACTION_STATUS.COMPLETED;

	// Update the QR code status using Drizzle operations
	await operations.qrCodeOperations.updateStatus(qrCode.id, newStatus);

	// Create a transaction record using Drizzle operations
	const transaction = await operations.transactionOperations.create({
		userId: qrCode.userId,
		qrCodeId: qrCode.id,
		type: TRANSACTION_TYPES.PIX_IN,
		amount: Math.abs(payload.valor),
		status: newStatus,
		description: payload.pagador?.nome
			? `PIX recebido de ${payload.pagador.nome}`
			: 'PIX recebido',
		flow2payTxid: idField,
		flow2payEndToEndId: payload.endToEndId,
		webhookPayload: payload,
		createdAt: new Date(payload.horario),
	});

	// Não publicamos novamente no NSQ aqui, pois já foi publicado na função handleBaasWebhook
	// Apenas registramos que o processamento foi concluído
	logger.info('PixIn transaction recorded', {
		transaction_id: transaction?.id,
		user_id: qrCode.userId,
		amount: Math.abs(payload.valor),
		flow2pay_id: idField,
	});

	// Send webhooks to client endpoints
	if (isSuccess) {
		await sendClientWebhooks(qrCode.userId, TRANSACTION_TYPES.PIX_IN, {
			id: transaction?.id,
			type: TRANSACTION_TYPES.PIX_IN,
			amount: Math.abs(payload.valor),
			txid: idField,
			end_to_end_id: payload.endToEndId,
			status: newStatus,
			created_at: payload.horario,
		});
	}

	logger.info('PixIn event processed successfully', {
		transaction_id: transaction?.id,
		status: newStatus,
	});
}

/**
 * Handle PixOut event (sent payment)
 * @param payload Flow2Pay webhook payload
 */
async function handlePixOut(payload: Flow2PayWebhookPayload): Promise<void> {
	const idField = payload.idEnvio || payload.txid;
	const logger = log.with({ event_type: 'PixOut', id_field: idField });

	if (!idField) {
		logger.error('Missing ID field in PixOut event');
		throw new Error('Missing ID field in PixOut event');
	}

	// Find the transaction based on idEnvio using Drizzle operations
	const transaction = await operations.transactionOperations.getByIdEnvio(
		idField
	);

	// Map Flow2Pay status to internal status
	let newStatus: string;
	switch (payload.status) {
		case 'Sucesso':
			newStatus = TRANSACTION_STATUS.COMPLETED;
			break;
		case 'Em processamento':
			newStatus = TRANSACTION_STATUS.PENDING;
			break;
		case 'Falha':
		case 'Erro':
			newStatus = TRANSACTION_STATUS.FAILED;
			break;
		default:
			// For unknown statuses, default to pending if it's not explicitly a failure
			newStatus =
				payload.status?.toLowerCase().includes('falha') ||
				payload.status?.toLowerCase().includes('erro') ||
				payload.status?.toLowerCase().includes('fail')
					? TRANSACTION_STATUS.FAILED
					: TRANSACTION_STATUS.PENDING;
			break;
	}

	const isSuccess = newStatus === TRANSACTION_STATUS.COMPLETED;

	// Log the status determination
	logger.info('Determining transaction status', {
		flow2pay_status: payload.status,
		is_success: isSuccess,
		new_status: newStatus,
	});

	if (transaction) {
		// Update the existing transaction using Drizzle operations
		await operations.transactionOperations.updateStatus(
			transaction.id,
			newStatus
		);

		// Update additional fields
		await operations.transactionOperations.updateWebhookData(
			transaction.id,
			payload.endToEndId,
			payload
		);

		// Não publicamos novamente no NSQ aqui, pois já foi publicado na função handleBaasWebhook
		// Apenas registramos que o processamento foi concluído
		logger.info('PixOut transaction updated', {
			transaction_id: transaction.id,
			user_id: transaction.userId,
			amount: Math.abs(payload.valor),
			flow2pay_id: idField,
			status: newStatus,
		});

		// Create a copy of the payload with the correct status for webhooks
		// This ensures the status in the webhook matches our database status
		const webhookPayload = {
			id: transaction.id,
			type: TRANSACTION_TYPES.PIX_OUT,
			amount: Math.abs(payload.valor),
			id_envio: idField,
			end_to_end_id: payload.endToEndId,
			status: newStatus, // Use our mapped status
			created_at: payload.horario,
			error: payload.erro,
			// Include a reference to the original Flow2Pay status for debugging
			original_status: payload.status,
		};

		// Send webhooks to client endpoints
		await sendClientWebhooks(
			transaction.userId,
			TRANSACTION_TYPES.PIX_OUT,
			webhookPayload
		);

		logger.info('PixOut event processed successfully', {
			transaction_id: transaction.id,
			status: newStatus,
		});
	} else {
		// This should not happen in normal operation, but we'll handle it gracefully
		logger.warn('Transaction not found for PixOut event', {
			id_envio: idField,
		});
		await recordUnknownPixOut(payload);
	}
}

/**
 * Handle PixInReversal event (refund of received payment)
 * @param payload Flow2Pay webhook payload
 */
async function handlePixInReversal(
	payload: Flow2PayWebhookPayload
): Promise<void> {
	const idField = payload.idEnvio || payload.txid;
	const logger = log.with({
		event_type: 'PixInReversal',
		end_to_end_id: payload.endToEndId,
		id_field: idField,
	});

	if (!payload.endToEndId) {
		logger.error('Missing endToEndId in PixInReversal event');
		throw new Error('Missing endToEndId in PixInReversal event');
	}

	// Find the original transaction based on endToEnd ID
	const originalTransaction =
		await operations.transactionOperations.getByEndToEndId(payload.endToEndId);

	if (
		!originalTransaction ||
		originalTransaction.type !== TRANSACTION_TYPES.PIX_IN
	) {
		logger.warn('Original transaction not found for PixInReversal event', {
			end_to_end_id: payload.endToEndId,
		});
		// We'll still record the reversal for reconciliation
		await recordUnknownReversal(payload);
		return;
	}

	const isSuccess = payload.status === 'Sucesso';
	const newStatus = isSuccess
		? TRANSACTION_STATUS.COMPLETED
		: TRANSACTION_STATUS.FAILED;

	// Create a reversal transaction record using Drizzle operations
	const reversalId = await operations.transactionOperations.create({
		userId: originalTransaction.userId,
		type: TRANSACTION_TYPES.PIX_IN_REVERSAL,
		amount: Math.abs(payload.valor),
		status: newStatus,
		description: 'Estorno de PIX recebido',
		flow2payIdEnvio: idField,
		flow2payEndToEndId: payload.endToEndId,
		webhookPayload: payload,
		createdAt: new Date(payload.horario),
	});

	// Update the original transaction status if the reversal is successful
	if (isSuccess) {
		await operations.transactionOperations.updateStatus(
			originalTransaction.id,
			TRANSACTION_STATUS.REVERSED
		);
	}

	// Não publicamos novamente no NSQ aqui, pois já foi publicado na função handleBaasWebhook
	// Apenas registramos que o processamento foi concluído
	logger.info('PixInReversal transaction recorded', {
		transaction_id: reversalId?.id,
		original_transaction_id: originalTransaction.id,
		user_id: originalTransaction.userId,
		amount: Math.abs(payload.valor),
		flow2pay_id: idField,
	});

	// Send webhooks to client endpoints
	await sendClientWebhooks(
		originalTransaction.userId,
		TRANSACTION_TYPES.PIX_IN_REVERSAL,
		{
			id: reversalId?.id,
			original_transaction_id: originalTransaction.id,
			type: TRANSACTION_TYPES.PIX_IN_REVERSAL,
			amount: Math.abs(payload.valor),
			end_to_end_id: payload.endToEndId,
			status: newStatus,
			created_at: payload.horario,
		}
	);

	logger.info('PixInReversal event processed successfully', {
		transaction_id: reversalId?.id,
		original_transaction_id: originalTransaction.id,
		status: newStatus,
	});
}

/**
 * Handle PixOutReversalExternal event (refund of sent payment)
 * @param payload Flow2Pay webhook payload
 */
async function handlePixOutReversalExternal(
	payload: Flow2PayWebhookPayload
): Promise<void> {
	const idField = payload.idEnvio || payload.txid;
	const logger = log.with({
		event_type: 'PixOutReversalExternal',
		end_to_end_id: payload.endToEndId,
		id_field: idField,
	});

	if (!payload.endToEndId) {
		logger.error('Missing endToEndId in PixOutReversalExternal event');
		throw new Error('Missing endToEndId in PixOutReversalExternal event');
	}

	// Find the original transaction based on endToEnd ID
	const originalTransaction =
		await operations.transactionOperations.getByEndToEndId(payload.endToEndId);

	if (
		!originalTransaction ||
		originalTransaction.type !== TRANSACTION_TYPES.PIX_OUT
	) {
		logger.warn(
			'Original transaction not found for PixOutReversalExternal event',
			{
				end_to_end_id: payload.endToEndId,
			}
		);
		// We'll still record the reversal for reconciliation
		await recordUnknownReversal(payload);
		return;
	}

	const isSuccess = payload.status === 'Sucesso';
	const newStatus = isSuccess
		? TRANSACTION_STATUS.COMPLETED
		: TRANSACTION_STATUS.FAILED;

	// Create a reversal transaction record using Drizzle operations
	const reversalId = await operations.transactionOperations.create({
		userId: originalTransaction.userId,
		type: TRANSACTION_TYPES.PIX_OUT_REVERSAL,
		amount: Math.abs(payload.valor),
		status: newStatus,
		description: 'Estorno de PIX enviado',
		flow2payIdEnvio: idField,
		flow2payEndToEndId: payload.endToEndId,
		webhookPayload: payload,
		createdAt: new Date(payload.horario),
	});

	// Update the original transaction status if the reversal is successful
	if (isSuccess) {
		await operations.transactionOperations.updateStatus(
			originalTransaction.id,
			TRANSACTION_STATUS.REVERSED
		);
	}

	// Não publicamos novamente no NSQ aqui, pois já foi publicado na função handleBaasWebhook
	// Apenas registramos que o processamento foi concluído
	logger.info('PixOutReversal transaction recorded', {
		transaction_id: reversalId?.id,
		original_transaction_id: originalTransaction.id,
		user_id: originalTransaction.userId,
		amount: Math.abs(payload.valor),
		flow2pay_id: idField,
	});

	// Send webhooks to client endpoints
	await sendClientWebhooks(
		originalTransaction.userId,
		TRANSACTION_TYPES.PIX_OUT_REVERSAL,
		{
			id: reversalId?.id,
			original_transaction_id: originalTransaction.id,
			type: TRANSACTION_TYPES.PIX_OUT_REVERSAL,
			amount: Math.abs(payload.valor),
			end_to_end_id: payload.endToEndId,
			status: newStatus,
			created_at: payload.horario,
		}
	);

	logger.info('PixOutReversalExternal event processed successfully', {
		transaction_id: reversalId?.id,
		original_transaction_id: originalTransaction.id,
		status: newStatus,
	});
}

/**
 * Record an unknown PIX In transaction
 * @param payload Flow2Pay webhook payload
 */
async function recordUnknownPixIn(
	payload: Flow2PayWebhookPayload
): Promise<void> {
	const idField = payload.idEnvio || payload.txid;
	const logger = log.with({
		action: 'record_unknown_pix_in',
		id_field: idField,
	});

	try {
		// Try to find a user to associate with this transaction
		const users = await db.query.users.findMany({
			limit: 1,
		});

		let userId = '';
		if (users.length > 0) {
			userId = users[0].id || '';
		}

		if (!userId) {
			logger.error('No users found to associate with unknown PIX In');
			return;
		}

		const isSuccess = payload.status === 'Sucesso';
		const newStatus = isSuccess
			? TRANSACTION_STATUS.COMPLETED
			: TRANSACTION_STATUS.FAILED;

		// Create a transaction record using Drizzle operations
		await operations.transactionOperations.create({
			userId: userId,
			type: TRANSACTION_TYPES.PIX_IN_UNKNOWN,
			amount: Math.abs(payload.valor),
			status: newStatus,
			description: 'PIX recebido sem QR code correspondente',
			flow2payTxid: idField,
			flow2payEndToEndId: payload.endToEndId || undefined,
			webhookPayload: payload,
			createdAt: new Date(payload.horario),
		});

		logger.info('Recorded unknown PIX In transaction', {
			user_id: userId,
			id_field: idField,
		});
	} catch (error) {
		logger.error('Failed to record unknown PIX In', {
			error: error instanceof Error ? error.message : 'Unknown error',
		});
	}
}

/**
 * Record an unknown PIX Out transaction
 * @param payload Flow2Pay webhook payload
 */
async function recordUnknownPixOut(
	payload: Flow2PayWebhookPayload
): Promise<void> {
	const idField = payload.idEnvio || payload.txid;
	const logger = log.with({
		action: 'record_unknown_pix_out',
		id_field: idField,
	});

	try {
		// Try to find a user to associate with this transaction
		const users = await db.query.users.findMany({
			limit: 1,
		});

		let userId = '';
		if (users.length > 0) {
			userId = users[0].id || '';
		}

		if (!userId) {
			logger.error('No users found to associate with unknown PIX Out');
			return;
		}

		const isSuccess = payload.status === 'Sucesso';
		const newStatus = isSuccess
			? TRANSACTION_STATUS.COMPLETED
			: TRANSACTION_STATUS.FAILED;

		// Create a transaction record using Drizzle operations
		await operations.transactionOperations.create({
			userId: userId,
			type: TRANSACTION_TYPES.PIX_OUT_UNKNOWN,
			amount: Math.abs(payload.valor),
			status: newStatus,
			description: 'PIX enviado sem registro de origem',
			flow2payIdEnvio: idField,
			flow2payEndToEndId: payload.endToEndId || undefined,
			webhookPayload: payload,
			createdAt: new Date(payload.horario),
		});

		logger.info('Recorded unknown PIX Out transaction', {
			user_id: userId,
			id_field: idField,
		});
	} catch (error) {
		logger.error('Failed to record unknown PIX Out', {
			error: error instanceof Error ? error.message : 'Unknown error',
		});
	}
}

/**
 * Record an unknown reversal transaction
 * @param payload Flow2Pay webhook payload
 */
async function recordUnknownReversal(
	payload: Flow2PayWebhookPayload
): Promise<void> {
	const idField = payload.idEnvio || payload.txid;
	const logger = log.with({
		action: 'record_unknown_reversal',
		end_to_end_id: payload.endToEndId,
		id_field: idField,
	});

	try {
		// Try to find a user to associate with this transaction
		const users = await db.query.users.findMany({
			limit: 1,
		});

		let userId = '';
		if (users.length > 0) {
			userId = users[0].id || '';
		}

		if (!userId) {
			logger.error('No users found to associate with unknown reversal');
			return;
		}

		const isSuccess = payload.status === 'Sucesso';
		const newStatus = isSuccess
			? TRANSACTION_STATUS.COMPLETED
			: TRANSACTION_STATUS.PENDING;
		const type =
			payload.evento === 'PixInReversal'
				? TRANSACTION_TYPES.PIX_IN_REVERSAL_UNKNOWN
				: TRANSACTION_TYPES.PIX_OUT_REVERSAL_UNKNOWN;

		// Create a transaction record using Drizzle operations
		await operations.transactionOperations.create({
			userId: userId,
			type: type,
			amount: Math.abs(payload.valor),
			status: newStatus,
			description: 'Estorno sem transação original correspondente',
			flow2payIdEnvio: idField,
			flow2payEndToEndId: payload.endToEndId,
			webhookPayload: payload,
			createdAt: new Date(payload.horario),
		});

		logger.info('Recorded unknown reversal transaction', {
			user_id: userId,
			end_to_end_id: payload.endToEndId,
			id_field: idField,
		});
	} catch (error) {
		logger.error('Failed to record unknown reversal', {
			error: error instanceof Error ? error.message : 'Unknown error',
		});
	}
}

/**
 * Map internal event type to Flow2Pay event type
 * @param eventType Internal event type
 * @returns Flow2Pay event type
 */
function mapToFlow2PayEvent(eventType: string): string {
	switch (eventType) {
		case TRANSACTION_TYPES.PIX_IN:
			return PIX_EVENT_TYPES.PIX_IN;
		case TRANSACTION_TYPES.PIX_OUT:
			return PIX_EVENT_TYPES.PIX_OUT;
		case TRANSACTION_TYPES.PIX_IN_REVERSAL:
			return PIX_EVENT_TYPES.PIX_IN_REVERSAL;
		case TRANSACTION_TYPES.PIX_OUT_REVERSAL:
			return PIX_EVENT_TYPES.PIX_OUT_REVERSAL;
		default:
			return eventType;
	}
}

/**
 * Send a webhook to Svix
 * @param eventType Type of event
 * @param payload Webhook payload
 * @param userId Optional user ID
 * @param endToEndId End-to-end ID from Flow2Pay (used for tracking)
 * @param webhookId ID of the webhook in our database (used for tracking)
 */
async function sendWebhookToSvix(
	eventType: string,
	payload: any,
	userId?: string,
	endToEndId?: string,
	webhookId?: string
): Promise<string> {
	const logger = log.with({
		action: 'send_webhook_to_svix',
		event_type: eventType,
		user_id: userId,
		end_to_end_id: endToEndId,
	});

	// Map Flow2Pay event types to Svix event types
	const svixEventType = eventType.includes('.')
		? eventType
		: mapToSvixEventType(eventType, payload.status);

	try {
		const messageId = await sendSvixMessage(svixEventType, payload, userId);

		logger.info('Webhook sent to Svix successfully', {
			svix_event_type: svixEventType,
			message_id: messageId,
		});

		// Update the webhook record with Svix message ID if provided
		if (webhookId && endToEndId) {
			try {
				await updateWebhookProcessingStatus(
					endToEndId,
					eventType,
					webhookId,
					messageId
				);
			} catch (dbError) {
				logger.warn('Failed to update webhook processing status', {
					error: dbError instanceof Error ? dbError.message : 'Unknown error',
				});
			}
		}

		return messageId;
	} catch (error) {
		logger.error('Error sending webhook to Svix', {
			error: error instanceof Error ? error.message : 'Unknown error',
		});
		// Return a placeholder message ID in case of error
		return `error-${Date.now()}`;
	}
}

/**
 * Send client webhooks based on user ID and event type
 */
async function sendClientWebhooks(
	userId: string | number | null,
	eventType: string,
	data: any
): Promise<void> {
	const logger = log.with({
		action: 'send_client_webhooks',
		user_id: userId,
		event_type: eventType,
	});

	if (!userId) {
		logger.warn('Cannot send client webhooks: missing user ID');
		return;
	}

	try {
		// Query the webhook configs for the user
		const userWebhooks = await db.query.webhookConfigs.findMany({
			where: eq(webhookConfigs.userId, String(userId)),
		});

		// Get webhook configs that include this event type
		const matchingWebhooks = userWebhooks.filter((config) => {
			const events = Array.isArray(config.events)
				? config.events
				: typeof config.events === 'string'
				? JSON.parse(config.events)
				: [];
			return (
				config.isActive &&
				(events.includes(eventType) ||
					events.includes('all') ||
					events.includes('*'))
			);
		});

		// Log how many webhook configs were found
		logger.info('Found webhook configs', {
			total: userWebhooks.length,
			matching: matchingWebhooks.length,
		});

		// No webhook configs found, try to forward to Pluggou as fallback
		if (matchingWebhooks.length === 0) {
			try {
				await forwardWebhookToPluggou(eventType, data);
			} catch (pluggouError) {
				logger.error('Error forwarding webhook to Pluggou', {
					error:
						pluggouError instanceof Error
							? pluggouError.message
							: 'Unknown error',
				});
			}
			return;
		}

		// Send webhooks to each matching config
		for (const config of matchingWebhooks) {
			const webhookUrl = config.url;
			const secretKey = config.secretKey;

			try {
				// Generate a signature if we have a secret key
				const signature = secretKey
					? generateWebhookSignature(data, secretKey)
					: '';

				// Prepare headers
				const headers: Record<string, string> = {
					'Content-Type': 'application/json',
					'X-Event-Type': eventType,
				};

				// Add signature header if available
				if (signature) {
					headers['X-Signature'] = signature;
				}

				// Send webhook
				const response = await fetch(webhookUrl, {
					method: 'POST',
					headers,
					body: JSON.stringify(data),
				});

				// Process response
				const responseText = await response.text();

				// Log result
				logger.info('Webhook sent', {
					webhook_id: config.id,
					url: webhookUrl,
					status: response.status,
					success: response.ok,
				});

				// Store delivery in the database
				try {
					await db.insert(webhookDeliveryLogs).values({
						webhookConfigId: config.id,
						eventType,
						payload: data,
						status: response.ok ? 'success' : 'failed',
						statusCode: response.status,
						responseBody: responseText,
					});
				} catch (logError) {
					logger.warn('Failed to log webhook delivery', {
						error:
							logError instanceof Error ? logError.message : 'Unknown error',
					});
				}
			} catch (sendError) {
				logger.error('Error sending webhook', {
					error:
						sendError instanceof Error ? sendError.message : 'Unknown error',
					webhook_id: config.id,
					url: webhookUrl,
				});

				// Store failed delivery in the database
				try {
					await db.insert(webhookDeliveryLogs).values({
						webhookConfigId: config.id,
						eventType,
						payload: data,
						status: 'failed',
						responseBody:
							sendError instanceof Error ? sendError.message : 'Unknown error',
					});
				} catch (logError) {
					logger.warn('Failed to log failed webhook delivery', {
						error:
							logError instanceof Error ? logError.message : 'Unknown error',
					});
				}
			}
		}
	} catch (error) {
		logger.error('Error sending webhooks', {
			error: error instanceof Error ? error.message : 'Unknown error',
			user_id: userId,
			event_type: eventType,
		});

		// Try to forward to Pluggou as fallback
		try {
			await forwardWebhookToPluggou(eventType, data);
		} catch (pluggouError) {
			logger.error(
				'Error forwarding webhook to Pluggou after webhook failure',
				{
					error:
						pluggouError instanceof Error
							? pluggouError.message
							: 'Unknown error',
				}
			);
		}
	}
}

/**
 * Generate a signature for webhook payloads
 * @param payload Webhook payload
 * @param secretKey Secret key for signing
 * @returns Signature
 */
function generateWebhookSignature(payload: any, secretKey: string): string {
	return crypto
		.createHmac('sha256', secretKey)
		.update(JSON.stringify(payload))
		.digest('hex');
}

/**
 * Forward webhook to app.pluggou.io
 * This function sends the webhook to the default destination (app.pluggou.io)
 * @param eventType Event type
 * @param data Event data
 */
async function forwardWebhookToPluggou(
	eventType: string,
	data: any
): Promise<void> {
	const logger = log.with({
		action: 'forward_webhook_to_pluggou',
		event_type: eventType,
	});

	try {
		// Prepare the webhook payload
		const payload = {
			event: eventType,
			timestamp: new Date().toISOString(),
			data,
		};

		logger.info('Forwarding webhook to app.pluggou.io', {
			event_type: eventType,
			destination: DEFAULT_WEBHOOK_URL,
		});

		// Send the webhook
		const response = await fetch(DEFAULT_WEBHOOK_URL, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'X-Source': 'pix-api',
				'X-Event-Type': eventType,
			},
			body: JSON.stringify(payload),
		});

		// Get response text
		const responseText = await response.text();

		// Log the webhook delivery using default webhook config
		try {
			await db.insert(webhookDeliveryLogs).values({
				webhookConfigId: 'default',
				eventType: eventType,
				payload: payload,
				status: response.ok ? 'success' : 'failed',
				statusCode: response.status,
				responseBody: responseText,
			});
		} catch (logError) {
			// If logging fails, just log the error but don't fail the webhook
			logger.warn('Failed to log webhook delivery to database', {
				error: logError instanceof Error ? logError.message : 'Unknown error',
				event_type: eventType,
				status: response.ok ? 'success' : 'failed',
				status_code: response.status,
			});
		}

		logger.info('Webhook forwarded to app.pluggou.io', {
			status: response.status,
			success: response.ok,
		});
	} catch (error) {
		logger.error('Error forwarding webhook to app.pluggou.io', {
			error: error instanceof Error ? error.message : 'Unknown error',
		});

		// Log the failed delivery using default webhook config
		try {
			await db.insert(webhookDeliveryLogs).values({
				webhookConfigId: 'default',
				eventType: eventType,
				payload: data,
				status: 'failed',
				responseBody: error instanceof Error ? error.message : 'Unknown error',
			});
		} catch (logError) {
			// If logging fails, just log the error but don't fail the webhook
			logger.warn('Failed to log failed webhook delivery to database', {
				error: logError instanceof Error ? logError.message : 'Unknown error',
				event_type: eventType,
				status: 'failed',
			});
		}
	}
}

/**
 * Test endpoint for Flow2Pay webhook validation
 * This endpoint can be used to test the webhook handling functionality
 */
export const testWebhook = api(
	{ method: 'POST', path: '/test-webhook-flow2pay', expose: true },
	async (params: {
		event_type: 'PixIn' | 'PixOut' | 'PixInReversal' | 'PixOutReversalExternal';
		status?: string;
		valor?: number;
		txid?: string;
		idEnvio?: string;
		endToEndId?: string;
	}): Promise<{ success: boolean; message: string }> => {
		const logger = log.with({ action: 'test_webhook' });

		try {
			// Generate a sample webhook payload
			const payload: Flow2PayWebhookPayload = {
				evento: params.event_type,
				status: params.status || 'Sucesso',
				valor: params.valor || Math.floor(Math.random() * 10000) + 100, // Random amount between 1 and 100 reais
				horario: new Date().toISOString(),
				txid:
					params.txid ||
					(params.event_type === 'PixIn' ? generateRandomId() : undefined),
				idEnvio:
					params.idEnvio ||
					(params.event_type === 'PixOut' ? generateRandomId() : undefined),
				endToEndId:
					params.endToEndId || `E${Math.floor(Math.random() * 1000000000000)}`,
				token: FLOW2PAY_EVENT_TOKEN(),
			};

			// Add additional fields based on event type
			if (params.event_type === 'PixIn') {
				payload.pagador = {
					nome: 'Usuário de Teste',
					chave: '<EMAIL>',
					tipoConta: 'CACC',
				};
			} else if (params.event_type === 'PixOut') {
				payload.recebedor = {
					nome: 'Destinatário de Teste',
					codigoBanco: '260',
					cpf_cnpj: '12345678901',
				};
			}

			logger.info('Generated test webhook payload', {
				event_type: params.event_type,
				txid: payload.txid,
				idEnvio: payload.idEnvio,
				endToEndId: payload.endToEndId,
			});

			// Simular o fluxo completo de processamento de webhook
			const idField = payload.idEnvio || payload.txid;

			// Map Flow2Pay event type to Svix event type
			const svixEventType = mapToSvixEventType(payload.evento, payload.status);

			// Prepare the payload for Svix
			const svixPayload = {
				event_type: payload.evento,
				flow2pay_event_type: payload.evento,
				svix_event_type: svixEventType,
				user_id: '0', // Default user ID
				amount: Math.abs(payload.valor),
				flow2pay_id: idField,
				end_to_end_id: payload.endToEndId,
				webhook_payload: payload,
				timestamp: payload.horario,
				status: payload.status || 'Sucesso',
				event_id: `test-${payload.evento}-${
					idField || payload.endToEndId || Date.now()
				}`,
			};

			// Send the message to Svix
			await sendSvixMessage(svixEventType, svixPayload);

			// Processar o webhook para atualizar o banco de dados
			await processWebhook(payload);

			logger.info('Test webhook processed and published to NSQ', {
				event_type: payload.evento,
				id_field: idField,
			});

			return {
				success: true,
				message: `Test webhook for ${params.event_type} processed successfully`,
			};
		} catch (error) {
			logger.error('Error processing test webhook', {
				error: error instanceof Error ? error.message : 'Unknown error',
			});

			throw error;
		}
	}
);

/**
 * Test endpoint for simulating a payment confirmation webhook
 * This endpoint can be used to test the webhook forwarding functionality
 */
export const testPaymentConfirmation = api(
	{ method: 'POST', path: '/test-payment-confirmation', expose: true },
	async (params: {
		api_key: Header<'X-API-Key'>;
		txid?: string;
		amount?: number;
		status?: string;
		user_id?: string;
		description?: string;
	}): Promise<{ success: boolean; message: string; details: any }> => {
		const logger = log.with({ action: 'test_payment_confirmation' });

		try {
			// Validate API key
			await validateAPIKey(
				params.api_key,
				API_PERMISSIONS.ALL, // Use ALL permission since ADMIN doesn't exist
				'/test-payment-confirmation'
			);

			// Default values
			const txid = params.txid || generateRandomId();
			const amount = params.amount || Math.floor(Math.random() * 10000) + 100; // Random amount between 1 and 100 reais
			const status = params.status || 'Sucesso';
			const user_id = params.user_id || '1'; // Default to first user (using string for CUID compatibility)
			// Note: description parameter is available but not used in this implementation

			// Find the QR code if txid is provided
			let qrCode = null;
			if (params.txid) {
				qrCode = await operations.qrCodeOperations.getByTxid(params.txid);
			}

			// Generate a Flow2Pay webhook payload
			const payload: Flow2PayWebhookPayload = {
				evento: 'PixIn',
				status: status,
				valor: amount,
				horario: new Date().toISOString(),
				txid: txid,
				endToEndId: `E${Math.floor(Math.random() * 1000000000000)}`,
				token: FLOW2PAY_EVENT_TOKEN(),
				pagador: {
					nome: 'Cliente de Teste',
					chave: '<EMAIL>',
					tipoConta: 'CACC',
				},
			};

			logger.info('Generated test payment confirmation payload', {
				txid: payload.txid,
				amount: payload.valor,
				status: payload.status,
				user_id: qrCode?.userId || user_id,
			});

			// Simular o fluxo completo de processamento de webhook
			const idField = payload.idEnvio || payload.txid;

			// Map Flow2Pay event type to Svix event type
			const svixEventType = mapToSvixEventType(payload.evento, payload.status);

			// Prepare the payload for Svix
			const svixPayload = {
				event_type: payload.evento,
				flow2pay_event_type: payload.evento,
				svix_event_type: svixEventType,
				user_id: qrCode?.userId || user_id,
				amount: Math.abs(payload.valor),
				flow2pay_id: idField,
				end_to_end_id: payload.endToEndId,
				webhook_payload: payload,
				timestamp: payload.horario,
				status: payload.status || 'Sucesso',
				event_id: `test-payment-${payload.evento}-${
					idField || payload.endToEndId || Date.now()
				}`,
			};

			// Send the message to Svix
			await sendSvixMessage(
				svixEventType,
				svixPayload,
				qrCode?.userId || user_id
			);

			// Processar o webhook para atualizar o banco de dados
			await processWebhook(payload);

			logger.info('Test payment confirmation processed and published to NSQ', {
				event_type: payload.evento,
				id_field: idField,
				user_id: qrCode?.userId || user_id,
			});

			// Return success with details
			return {
				success: true,
				message: `Test payment confirmation processed successfully`,
				details: {
					txid: payload.txid,
					amount: payload.valor,
					status: payload.status,
					end_to_end_id: payload.endToEndId,
					qr_code_found: qrCode !== null,
					user_id: qrCode?.userId || user_id,
				},
			};
		} catch (error) {
			logger.error('Error processing test payment confirmation', {
				error: error instanceof Error ? error.message : 'Unknown error',
			});

			throw error;
		}
	}
);

/**
 * Generate a random ID for test webhooks
 * @returns Random ID string
 */
function generateRandomId(): string {
	const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
	let result = '';
	// Generate a random ID between 26 and 35 characters (Flow2Pay requirement)
	const length = Math.floor(Math.random() * 10) + 26;
	for (let i = 0; i < length; i++) {
		result += chars.charAt(Math.floor(Math.random() * chars.length));
	}
	return result;
}

/**
 * Record Svix message ID in the database
 * @param endToEndId End-to-end ID for the PIX event
 * @param eventType Event type
 * @param webhookId ID of the webhook record in the database
 * @param messageId Svix message ID
 */
async function updateWebhookProcessingStatus(
	endToEndId: string,
	eventType: string,
	webhookId: string,
	messageId: string
): Promise<void> {
	const logger = log.with({
		action: 'update_webhook_processing_status',
		end_to_end_id: endToEndId,
		event_type: eventType,
		webhook_id: webhookId,
	});

	try {
		// Update the webhook record with Svix message ID
		await db
			.update(flow2payWebhooks)
			.set({
				svixMessageId: messageId,
				processed: true,
				processedAt: new Date(),
			})
			.where(eq(flow2payWebhooks.id, webhookId));

		logger.info('Webhook processing status updated successfully');
	} catch (error) {
		logger.error('Error updating webhook processing status', {
			error: error instanceof Error ? error.message : 'Unknown error',
			end_to_end_id: endToEndId,
			event_type: eventType,
			id_envio: webhookId,
			status: 'failed',
		});
	}
}

/**
 * Mark a webhook as processed in the database
 */
async function markWebhookAsProcessed(
	endToEndId: string,
	eventType: string,
	webhookId: string,
	status: 'completed' | 'failed' = 'completed',
	errorMessage?: string
): Promise<void> {
	const logger = log.with({
		action: 'mark_webhook_as_processed',
		end_to_end_id: endToEndId,
		event_type: eventType,
		id_field: webhookId,
	});

	try {
		// Update the webhook record with processing status
		await db
			.update(flow2payWebhooks)
			.set({
				processed: true,
				processedAt: new Date(),
				status: status,
			})
			.where(eq(flow2payWebhooks.id, webhookId));

		logger.info('Webhook marked as processed successfully');
	} catch (error) {
		logger.error('Error marking webhook as processed', {
			error: error instanceof Error ? error.message : 'Unknown error',
			end_to_end_id: endToEndId,
			event_type: eventType,
			id_envio: webhookId,
			id_field: webhookId,
			status: status,
		});
	}
}
