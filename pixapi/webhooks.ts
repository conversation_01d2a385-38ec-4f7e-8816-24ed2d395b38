import { api, APIError, Header } from 'encore.dev/api';
import log from 'encore.dev/log';
import { secret } from 'encore.dev/config';
import { sendSvixMessage, mapToSvixEventType } from './svix';
import { forwardToPluggou } from './webhook-forwarder';

// Flow2Pay event token from environment variables
const FLOW2PAY_EVENT_TOKEN = secret('FLOW2PAY_EVENT_TOKEN');

// Default webhook URL for Pluggou
const DEFAULT_WEBHOOK_URL = 'https://app.pluggou.io/api/webhooks/pluggou-pix';

export interface Flow2PayWebhookPayload {
	evento: 'PixIn' | 'PixOut' | 'PixInReversal' | 'PixOutReversalExternal';
	token?: string;
	txid?: string;
	idEnvio?: string;
	endToEndId?: string;
	codigoTransacao?: string;
	status?: string; // Made optional to handle PixIn events without status
	chavePix?: string;
	valor: number;
	horario: string;
	dataHora?: string; // For backward compatibility
	infoPagador?: any; // Additional field seen in the webhook
	recebedor?: {
		nome?: string;
		codigoBanco?: string;
		cpf_cnpj?: string;
	};
	pagador?: {
		nome?: string;
		chave?: string;
		tipoConta?: string;
		cpf?: string; // Additional field seen in the webhook
		codigoBanco?: string; // Additional field seen in the webhook
		cpf_cnpj?: string;
	};
	erro?: {
		origem?: string;
		motivo?: string;
		mensagem?: string;
	};
}

/**
 * Flow2Pay webhook handler for PIX events (Pure Proxy Mode)
 * This endpoint receives webhooks from Flow2Pay and forwards them directly to SVIX and Pluggou
 */
export const handleBaasWebhook = api.raw(
	{ method: 'POST', path: '/baas', expose: true },
	async (req, resp) => {
		const logger = log.with({ service: 'flow2pay-webhook-proxy' });
		logger.info('Received Flow2Pay webhook in proxy mode');

		try {
			// Parse the request body
			let body = '';
			for await (const chunk of req) {
				body += chunk.toString();
			}

			if (!body) {
				logger.warn('Empty webhook payload received');
				resp.writeHead(400, { 'Content-Type': 'application/json' });
				resp.end(JSON.stringify({ success: false, error: 'Empty payload' }));
				return;
			}

			// Log the raw webhook payload for debugging
			console.log('Raw Flow2Pay webhook payload:', body);

			// Parse the JSON payload
			let payload: Flow2PayWebhookPayload;
			try {
				payload = JSON.parse(body);
			} catch (error) {
				logger.error('Invalid JSON in webhook payload', { body });
				resp.writeHead(400, { 'Content-Type': 'application/json' });
				resp.end(
					JSON.stringify({ success: false, error: 'Invalid JSON payload' })
				);
				return;
			}

			// Validate the token if present
			if (payload.token && payload.token !== FLOW2PAY_EVENT_TOKEN()) {
				logger.error('Invalid token in webhook payload');
				resp.writeHead(401, { 'Content-Type': 'application/json' });
				resp.end(
					JSON.stringify({
						success: false,
						error: 'Invalid authentication token',
					})
				);
				return;
			}

			// Validate required fields
			if (!payload.evento) {
				logger.error('Missing evento field in webhook payload');
				resp.writeHead(400, { 'Content-Type': 'application/json' });
				resp.end(
					JSON.stringify({
						success: false,
						error: 'Missing evento field in webhook payload',
					})
				);
				return;
			}

			// Validate event type
			if (
				![
					'PixIn',
					'PixOut',
					'PixInReversal',
					'PixOutReversalExternal',
				].includes(payload.evento)
			) {
				logger.error('Invalid evento type in webhook payload', {
					evento: payload.evento,
				});
				resp.writeHead(400, { 'Content-Type': 'application/json' });
				resp.end(
					JSON.stringify({
						success: false,
						error: 'Invalid evento type in webhook payload',
					})
				);
				return;
			}

			// For PixIn events without status field, assume it's a successful payment
			if (!payload.status && payload.evento === 'PixIn') {
				logger.info('PixIn webhook without status field, assuming success');
				payload.status = 'Sucesso'; // Default to success for PixIn events
			}
			// For other event types, status is still required
			else if (!payload.status && payload.evento !== 'PixIn') {
				logger.error('Missing status field in webhook payload');
				resp.writeHead(400, { 'Content-Type': 'application/json' });
				resp.end(
					JSON.stringify({
						success: false,
						error: 'Missing status field in webhook payload',
					})
				);
				return;
			}

			// Normalize timestamp field (some payloads use dataHora, others use horario)
			if (!payload.horario && payload.dataHora) {
				payload.horario = payload.dataHora;
			}

			// Normalize ID fields (some payloads use txid, others use idEnvio)
			const idField = payload.idEnvio || payload.txid;

			// Log the received webhook
			logger.info('Received Flow2Pay webhook in proxy mode', {
				event_type: payload.evento,
				status: payload.status,
				id_envio: idField,
				end_to_end_id: payload.endToEndId,
				valor: payload.valor,
			});

			// Skip database storage in proxy mode - log that we're skipping it
			logger.info('Skipping database storage in proxy mode', {
				event_type: payload.evento,
				id_field: idField,
			});

			// Send the webhook to Svix for distribution (proxy mode - no database lookups)
			try {
				// In proxy mode, use a default user_id since we don't have database access
				const userId = 'proxy_user';

				// Map Flow2Pay event type to Svix event type
				const svixEventType = mapToSvixEventType(
					payload.evento,
					payload.status
				);

				// Prepare the payload for Svix
				const svixPayload = {
					event_type: payload.evento,
					flow2pay_event_type: payload.evento,
					svix_event_type: svixEventType,
					user_id: userId,
					amount: Math.abs(payload.valor),
					flow2pay_id: idField,
					end_to_end_id: payload.endToEndId,
					webhook_payload: payload,
					timestamp: payload.horario,
					status: payload.status || 'Sucesso',
					event_id: `${payload.evento}-${
						idField || payload.endToEndId || Date.now()
					}`,
				};

				// Send the message to Svix
				const messageId = await sendSvixMessage(
					svixEventType,
					svixPayload,
					userId
				);

				logger.info('Webhook sent to Svix successfully in proxy mode', {
					event_type: payload.evento,
					svix_event_type: svixEventType,
					user_id: userId,
					id_field: idField,
					message_id: messageId,
				});
			} catch (svixError) {
				logger.error('Error sending webhook to Svix in proxy mode', {
					error:
						svixError instanceof Error ? svixError.message : 'Unknown error',
					event_type: payload.evento,
				});
				// Continue processing even if Svix fails
				logger.warn(
					'Continuing webhook processing despite Svix error in proxy mode'
				);
			}

			// Forward webhook directly to Pluggou in proxy mode
			try {
				await forwardToPluggou(payload, DEFAULT_WEBHOOK_URL);
				logger.info('Webhook forwarded to Pluggou successfully in proxy mode', {
					event_type: payload.evento,
					id_field: idField,
				});
			} catch (forwardError) {
				logger.error('Error forwarding webhook to Pluggou in proxy mode', {
					error:
						forwardError instanceof Error
							? forwardError.message
							: 'Unknown error',
					event_type: payload.evento,
				});
				// Continue processing even if forwarding fails
			}

			// Return success immediately
			resp.writeHead(200, { 'Content-Type': 'application/json' });
			resp.end(JSON.stringify({ success: true }));
		} catch (error) {
			logger.error('Error handling webhook in proxy mode', {
				error: error instanceof Error ? error.message : 'Unknown error',
			});
			resp.writeHead(500, { 'Content-Type': 'application/json' });
			resp.end(
				JSON.stringify({
					success: false,
					error: error instanceof Error ? error.message : 'Unknown error',
				})
			);
		}
	}
);

// Note: In pure proxy mode, we don't need the processWebhook function
// as all webhook processing is handled directly in the handleBaasWebhook endpoint
// by forwarding to SVIX and Pluggou without database operations.

// Note: All handler functions (handlePixIn, handlePixOut, etc.) have been removed
// in proxy mode since we don't perform database operations. All webhook processing
// is handled directly in the handleBaasWebhook endpoint by forwarding to SVIX and Pluggou.

// All remaining functions removed in proxy mode - no database operations needed

// End of webhooks.ts - Pure Proxy Mode
// All database-related functions have been removed since we're operating in proxy mode
// where webhooks are forwarded directly to SVIX and Pluggou without database operations.

// Note: Test endpoints have been removed in proxy mode since they relied on database operations
// that are not available in pure proxy mode.
