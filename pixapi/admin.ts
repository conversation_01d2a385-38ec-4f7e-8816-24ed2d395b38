import { api, APIError } from 'encore.dev/api';
import { db, operations } from './db';
import { generateAPIKey, API_PERMISSIONS } from './auth';
import { Header } from 'encore.dev/api';
import log from 'encore.dev/log';
import { secret } from 'encore.dev/config';

// Admin API key from environment variables
const ADMIN_API_KEY = secret('ADMIN_API_KEY');

/**
 * Create a user with Flow2Pay token (admin only)
 * This endpoint is secured and only accessible with the admin API key
 */
export const createUser = api(
	{ method: 'POST', path: '/admin/user', expose: true },
	async (params: {
		admin_key: Header<'X-Admin-Key'>;
		email: string;
		name: string;
		partner_id: string; // Which payment partner to use (e.g., 'flow2pay')
		ip_address?: string;
		user_agent?: string;
	}): Promise<{
		id: string;
		api_key?: string; // Optional: automatically generate an API key
	}> => {
		const { admin_key, email, name, partner_id, ip_address, user_agent } =
			params;

		const logger = log.with({
			action: 'create_user',
			email,
			partner_id,
			ip_address: ip_address || 'unknown',
			user_agent: user_agent || 'unknown',
		});

		// Validate admin key
		if (admin_key !== ADMIN_API_KEY()) {
			logger.warn('Invalid admin key used');
			throw APIError.unauthenticated('Invalid admin key');
		}

		// Validate inputs
		if (!email || !name || !partner_id) {
			logger.warn('Missing required fields');
			throw APIError.invalidArgument('Missing required fields');
		}

		try {
			// Insert the user into the database using Drizzle
			const result = await operations.userOperations.create({
				email,
				name,
				partnerId: partner_id,
			});

			if (!result) {
				logger.error('Failed to create user');
				throw APIError.internal('Failed to create user');
			}

			// Note: We no longer store partner tokens in the database
			// We use the global FLOW2PAY_EVENT_TOKEN from environment variables instead
			logger.info(
				'Using global FLOW2PAY_EVENT_TOKEN from environment variables'
			);

			// Generate an API key with all permissions
			const apiKey = await generateAPIKey(
				result.id,
				'Default API Key',
				[API_PERMISSIONS.ALL],
				undefined // No expiration
			);

			// Log the user creation
			await operations.auditLogOperations.create({
				userId: result.id,
				action: 'user_created',
				entityType: 'user',
				entityId: result.id.toString(),
				ipAddress: ip_address,
				userAgent: user_agent,
				details: { email, name, partner_id },
			});

			logger.info('User created successfully', {
				user_id: result.id,
			});

			return { id: result.id, api_key: apiKey.key };
		} catch (error) {
			// Check for duplicate email error
			if (
				error instanceof Error &&
				error.message.includes('unique constraint')
			) {
				logger.warn('User with this email already exists', { email });
				throw APIError.alreadyExists('User with this email already exists');
			}

			logger.error('Failed to create user', {
				error: error instanceof Error ? error.message : 'Unknown error',
			});
			throw APIError.internal(`Failed to create user: ${error}`);
		}
	}
);

/**
 * List all users (admin only)
 */
export const listUsers = api(
	{ method: 'GET', path: '/admin/users', expose: true },
	async (params: {
		admin_key: Header<'X-Admin-Key'>;
		page?: number;
		limit?: number;
	}): Promise<{
		users: Array<{
			id: string;
			email: string;
			name: string;
			partner_id: string;
			created_at: string;
		}>;
		total: number;
		page: number;
		limit: number;
	}> => {
		const { admin_key, page = 1, limit = 50 } = params;

		const logger = log.with({ action: 'list_users' });

		// Validate admin key
		if (admin_key !== ADMIN_API_KEY()) {
			logger.warn('Invalid admin key used');
			throw APIError.unauthenticated('Invalid admin key');
		}

		try {
			// Get users with pagination using Drizzle
			const result = await operations.userOperations.list(page, limit);

			// Transform the users to match the expected response format
			const users = result.users.map((user) => ({
				id: user.id,
				email: user.email,
				name: user.name,
				partner_id: user.partnerId,
				created_at: user.createdAt.toISOString(),
			}));

			logger.info('Users listed successfully', {
				count: users.length,
				total: result.total,
			});

			return {
				users,
				total: result.total,
				page: result.page,
				limit: result.limit,
			};
		} catch (error) {
			logger.error('Failed to list users', {
				error: error instanceof Error ? error.message : 'Unknown error',
			});
			throw APIError.internal(`Failed to list users: ${error}`);
		}
	}
);
