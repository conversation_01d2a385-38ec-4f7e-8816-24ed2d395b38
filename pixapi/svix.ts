import { Svix } from 'svix';
import log from 'encore.dev/log';
import { secret } from 'encore.dev/config';

// Svix API token from environment variables
const SVIX_TOKEN = secret('SVIX_TOKEN');

// Default Svix app ID for Flow2Pay events
const DEFAULT_APP_ID = 'app_2xJGtEh9B3sOptpW4thRObvtlh9';

// Default channel for Flow2Pay events
const DEFAULT_CHANNEL = 'flow2pay-events';

// Create a Svix client with the API token
function createSvixClient() {
	try {
		const token = SVIX_TOKEN;
		if (!token) {
			throw new Error('SVIX_TOKEN is not set in environment variables');
		}
		return new Svix(token);
	} catch (error) {
		log.error('Failed to create Svix client', {
			error: error instanceof Error ? error.message : 'Unknown error',
		});
		throw error;
	}
}

/**
 * Verify a Svix webhook signature
 * @param payload The raw payload as a string
 * @param signature The signature from the svix-signature header
 * @param timestamp The timestamp from the svix-timestamp header
 * @param secret The webhook secret
 * @param messageId The message ID from the svix-id header
 * @returns True if the signature is valid, false otherwise
 */
export function verifySvixSignature(
	payload: string,
	signature: string,
	timestamp: string,
	secret: string,
	messageId?: string // Make messageId optional
): boolean {
	try {
		const logger = log.with({ action: 'verify_svix_signature' });

		// Log the inputs for debugging
		logger.info('Verifying Svix signature', {
			signature_length: signature.length,
			timestamp_length: timestamp.length,
			secret_length: secret.length,
			payload_length: payload.length,
			message_id: messageId,
		});

		// Temporarily bypass signature verification
		logger.info('Svix signature validation temporarily bypassed');
		return true;

		/* Commented out validation code - to be re-enabled later
		// Use the imported Webhook class
		const wh = new Webhook(secret);

		// Create headers object with all required fields
		const headers: Record<string, string> = {
			'svix-timestamp': timestamp,
			'svix-signature': signature,
		};

		// Add messageId to headers if provided
		if (messageId) {
			headers['svix-id'] = messageId;
		}

		// Verify the signature
		wh.verify(payload, headers);

		// If verification doesn't throw an error, it's valid
		logger.info('Svix signature verified successfully');
		return true;
		*/
	} catch (error) {
		const logger = log.with({ action: 'verify_svix_signature' });
		logger.error('Failed to verify Svix signature', {
			error: error instanceof Error ? error.message : 'Unknown error',
		});
		return false;
	}
}

/**
 * Map Flow2Pay event types to Svix event types
 * @param eventType Flow2Pay event type
 * @param status Flow2Pay event status
 * @returns Svix event type
 */
export function mapToSvixEventType(eventType: string, status?: string): string {
	// Default mapping with status postfix
	if (status && status !== 'Sucesso') {
		return `${eventType}.${status}`;
	}

	// Default mapping for successful events
	switch (eventType) {
		case 'PixIn':
			return 'PixIn.Confirmation';
		case 'PixOut':
			return 'PixOut.Confirmation';
		case 'PixInReversal':
			return 'PixIn.Reversal';
		case 'PixOutReversalExternal':
			return 'PixOut.Reversal';
		default:
			return `${eventType}.Confirmation`;
	}
}

/**
 * Send a message to Svix
 *
 * @param eventType Event type
 * @param payload Message payload
 * @param userId Optional user ID for user-specific channels
 * @returns Message ID
 */
export async function sendSvixMessage(
	eventType: string,
	payload: any,
	userId?: string
): Promise<string> {
	const logger = log.with({
		action: 'send_svix_message',
		event_type: eventType,
		app_id: DEFAULT_APP_ID,
		user_id: userId,
	});

	try {
		// Create a Svix client with the token
		const svix = createSvixClient();

		// Determine channels
		const channels = [DEFAULT_CHANNEL];
		if (userId) {
			channels.push(`user-${userId}`);
		}

		logger.debug('Sending message to Svix', {
			channels,
			app_id: DEFAULT_APP_ID,
			event_channels: userId ? [`user-${userId}`] : [],
		});

		// Send the message to the app
		const response = await svix.message.create(DEFAULT_APP_ID, {
			eventType,
			payload,
			channels,
		});

		logger.info('Message sent to Svix successfully', {
			message_id: response.id,
			channels,
		});

		return response.id;
	} catch (error) {
		let errorMessage = 'Unknown error';
		let errorDetails = {};

		if (error instanceof Error) {
			errorMessage = error.message;
			// Try to extract more details from the error
			if ('response' in error && error.response) {
				const response = error.response as any;
				errorDetails = {
					status: response.status,
					statusText: response.statusText,
					data: response.data,
				};
			}
		}

		logger.error('Svix API error', {
			error: errorMessage,
			error_details: errorDetails,
			app_id: DEFAULT_APP_ID,
			event_type: eventType,
			user_id: userId,
		});

		// Generate a fake message ID for testing or when Svix is not accessible
		const fakeMessageId = `msg-${Date.now()}-${Math.random()
			.toString(36)
			.substring(2, 10)}`;
		logger.info('Using fallback message ID due to Svix error', {
			fake_message_id: fakeMessageId,
			error_type: errorMessage.includes('401')
				? 'authentication_error'
				: 'unknown_error',
		});

		return fakeMessageId;
	}
}

/**
 * Check if Svix is configured and available
 * @returns True if Svix is configured and available, false otherwise
 */
export async function isSvixAvailable(): Promise<{
	available: boolean;
	error?: string;
	app_id?: string;
	token_configured?: boolean;
	error_details?: any;
}> {
	const logger = log.with({ action: 'check_svix_status' });

	try {
		// Check if we have a token
		const token = SVIX_TOKEN();
		if (!token) {
			return {
				available: false,
				error: 'SVIX_TOKEN not configured',
				token_configured: false,
			};
		}

		logger.info('Svix token is configured, testing connection', {
			token_length: token.length,
			app_id: DEFAULT_APP_ID,
		});

		// Create a Svix client
		const svix = createSvixClient();

		// Try to get app information
		const appResponse = await svix.application.get(DEFAULT_APP_ID);

		if (appResponse) {
			logger.info('Svix connection successful', {
				app_id: appResponse.id,
				app_name: appResponse.name,
			});

			return {
				available: true,
				app_id: appResponse.id,
				token_configured: true,
			};
		}

		return {
			available: false,
			error: 'App not found',
			token_configured: true,
		};
	} catch (error) {
		const errorMessage =
			error instanceof Error ? error.message : 'Unknown error';
		let errorDetails = {};

		if (error instanceof Error && 'response' in error && error.response) {
			const response = error.response as any;
			errorDetails = {
				status: response.status,
				statusText: response.statusText,
				data: response.data,
			};
		}

		logger.error('Error checking Svix availability', {
			error: errorMessage,
			error_details: errorDetails,
			app_id: DEFAULT_APP_ID,
		});

		return {
			available: false,
			error: errorMessage,
			token_configured: true,
			error_details: errorDetails,
		};
	}
}
