version: '3.8'

services:
  # Serviço de inicialização do banco de dados Neon
  db-init:
    image: postgres:15-alpine
    container_name: pix-api-db-init
    restart: "no"
    environment:
      - DB_HOST=${DB_HOST:-ep-muddy-darkness-acfy40ru-pooler.sa-east-1.aws.neon.tech}
      - DB_USER=${DB_USER:-pixdb_owner}
      - DB_PASSWORD=${DB_PASSWORD:-npg_Ta9xPfFXGCv6}
      - DB_NAME=${DB_NAME:-pixdb}
      - DB_CONNECTION_STRING=${DB_CONNECTION_STRING:-postgresql://pixdb_owner:<EMAIL>/pixdb?sslmode=require}
      - PGCONNECT_TIMEOUT=10
      - TIMEOUT=120
      - PGSSLMODE=require
    volumes:
      - ./pixapi/migrations:/app/pixapi/migrations
      - ./scripts:/app/scripts
    working_dir: /app
    # Usar o script de migração para Neon
    command: >
      sh -c "
        echo '======== STARTING NEON DATABASE MIGRATION ========' &&

        # Instalar bash se necessário
        apk add --no-cache bash &&

        # Tornar o script executável
        chmod +x /app/scripts/apply-neon-migrations.sh &&

        # Executar o script de migração
        /app/scripts/apply-neon-migrations.sh
      "
    # Make this service optional so the deployment can continue even if it fails
    deploy:
      restart_policy:
        condition: none



  # Encore App (usando imagem pré-construída)
  pixapi:
    image: pluggou/pixapi:v0.1.6
    container_name: pixapi-app
    depends_on:
      db-init:
        condition: service_started  # Change from service_completed_successfully
    environment:
      - PORT=4000
      - API_BASE_URL=${API_BASE_URL:-https://apipix.cloud.pluggou.io}
      # Neon Database Connection
      - NEON_DB_PASSWORD=${DB_PASSWORD:-npg_Ta9xPfFXGCv6}
      - NEON_DB_CONNECTION_STRING=${DB_CONNECTION_STRING:-postgresql://pixdb_owner:<EMAIL>/pixdb?sslmode=require}
      - POSTGRES_PASSWORD=${DB_PASSWORD:-npg_Ta9xPfFXGCv6}
      - DB_NAME=pixdb
      - DATABASE_URL=${DB_CONNECTION_STRING:-postgresql://pixdb_owner:<EMAIL>/pixdb?sslmode=require}
      - ENCORE_FORCE_MIGRATIONS=false # Desativamos as migrações automáticas do Encore pois usamos nosso próprio serviço db-init

      # Variáveis Flow2Pay
      - FLOW2PAY_CLIENT_ID=${FLOW2PAY_CLIENT_ID:-ZDUwNDIyYWMtNzI4Mi00YTU0LTk0MzktMTFmZDI0OGU0MjU5}
      - FLOW2PAY_CLIENT_SECRET=${FLOW2PAY_CLIENT_SECRET:-m4T9DHJ2M9t_buV86Gvx7wPXarRhpPCUxUc0SwlIimQ}
      - FLOW2PAY_EVENT_TOKEN=${FLOW2PAY_EVENT_TOKEN:-YzVlODg1MDgtZDA1Zi00ZDc1LWE3YzAtNjU5NDM0ZmM4NDU0}
      # Admin API Key
      - ADMIN_API_KEY=${ADMIN_API_KEY:-19af7b34417f24a7f29d333dfc3dcb473d60e18ff91a712ffc07475a5631ea99}
      # Configuração do banco de dados
      - ENCORE_DB_NAME=pixdb
      # Svix Configuration
      - SVIX_TOKEN=${SVIX_TOKEN:-eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3NDc2NDg5MjMsImV4cCI6MjA2MzAwODkyMywibmJmIjoxNzQ3NjQ4OTIzLCJpc3MiOiJzdml4LXNlcnZlciIsInN1YiI6Im9yZ18yM3JiOFlkR3FNVDBxSXpwZ0d3ZFhmSGlyTXUifQ.JGLHBlZxiJQrytdENjWrvA3X80jnVxd_QX_l7X9vZf4}
      - SVIX_WEBHOOK_SECRET=${SVIX_WEBHOOK_SECRET:-whsec_MpqJz3kgTcn5JF+XkU+fHQ==}
    ports:
      - "4000:4000"
    restart: unless-stopped
    labels:
      - coolify.managed=true
      - traefik.enable=true
      - traefik.http.routers.pixapi-secure.rule=Host(`apipix.cloud.pluggou.io`)
      - traefik.http.services.pixapi.loadbalancer.server.port=4000
      - caddy_0=apipix.cloud.pluggou.io
      - caddy_0.handle_path=/*
      - caddy_0.handle_path.0_reverse_proxy={{upstreams 4000}}
      - caddy_0.encode=zstd gzip
      - caddy_0.header=-Server


