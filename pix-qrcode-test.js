#!/usr/bin/env node

/**
 * PIX QR Code Generation Test
 * 
 * This script tests the QR code generation functionality of the PIX API.
 * It sends requests to generate QR codes with various parameters and validates the responses.
 * 
 * @version 1.0.0
 * <AUTHOR> Team
 */

const fs = require('fs');
const path = require('path');

// Configuration - Override with environment variables if needed
const CONFIG = {
  // API endpoints
  apiUrl: process.env.API_URL || 'https://apipix.cloud.pluggou.io',
  apiKey: process.env.API_KEY || '19af7b34417f24a7f29d333dfc3dcb473d60e18ff91a712ffc07475a5631ea99',
  
  // Test parameters
  saveImages: true,
  outputDir: './test-results',
  verbose: process.env.VERBOSE === 'true'
};

/**
 * Ensures the output directory exists
 */
function ensureOutputDir() {
  if (!fs.existsSync(CONFIG.outputDir)) {
    fs.mkdirSync(CONFIG.outputDir, { recursive: true });
  }
}

/**
 * Saves a base64 image to a file
 * @param {string} base64Image - Base64 encoded image data
 * @param {string} prefix - Prefix for the filename
 * @returns {string} Path to the saved file
 */
function saveImage(base64Image, prefix) {
  if (!CONFIG.saveImages) return null;
  
  ensureOutputDir();
  const filename = path.join(CONFIG.outputDir, `${prefix}-${Date.now()}.png`);
  const base64Data = base64Image.replace(/^data:image\/png;base64,/, '');
  fs.writeFileSync(filename, base64Data, 'base64');
  return filename;
}

/**
 * Parses command line arguments
 * @returns {Object} Parsed arguments
 */
function parseArguments() {
  const args = process.argv.slice(2);
  const params = {
    amount: 1.0,
    description: 'PIX QR Code Test',
    expirationTime: 3600,
    outputFile: null,
    verbose: CONFIG.verbose,
    help: false
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];

    switch (arg) {
      case '--amount':
      case '-a':
        params.amount = parseFloat(args[++i]);
        break;
      case '--description':
      case '-d':
        params.description = args[++i];
        break;
      case '--expiration':
      case '-e':
        params.expirationTime = parseInt(args[++i], 10);
        break;
      case '--output':
      case '-o':
        params.outputFile = args[++i];
        break;
      case '--verbose':
      case '-v':
        params.verbose = true;
        break;
      case '--help':
      case '-h':
        params.help = true;
        break;
      default:
        console.error(`Unknown argument: ${arg}`);
        params.help = true;
    }
  }

  // Validate parameters
  if (isNaN(params.amount) || params.amount <= 0) {
    console.error('Error: Amount must be a positive number');
    params.help = true;
  }

  if (params.expirationTime <= 0 || params.expirationTime > 172000) {
    console.error('Error: Expiration time must be between 1 and 172000 seconds');
    params.help = true;
  }

  return params;
}

/**
 * Displays help information
 */
function showHelp() {
  const scriptName = path.basename(process.argv[1]);
  console.log(`
PIX QR Code Generation Test
===========================

Usage: node ${scriptName} [options]

Options:
  -a, --amount <value>         Amount in BRL (e.g., 10.50)
  -d, --description <text>     Payment description
  -e, --expiration <seconds>   Expiration time in seconds (default: 3600)
  -o, --output <file>          Output file for QR code image
  -v, --verbose                Enable verbose output
  -h, --help                   Show this help

Examples:
  node ${scriptName} --amount 10.50 --description "Test payment"
  node ${scriptName} -a 5.00 -e 86400 -v
  node ${scriptName} --amount 1.00 --output my-qrcode.png
  `);
}

/**
 * Generates a PIX QR code
 * @param {Object} params - QR code parameters
 * @returns {Promise<Object>} Generation result
 */
async function generateQRCode(params) {
  console.log('\n🧪 TEST: PIX QR Code Generation');
  console.log('=============================');
  
  if (params.verbose) {
    console.log('\n📡 Request details:');
    console.log(`URL: ${CONFIG.apiUrl}/qrcode`);
    console.log(`API Key: ${CONFIG.apiKey.substring(0, 8)}...${CONFIG.apiKey.substring(CONFIG.apiKey.length - 4)}`);
    console.log(`Amount: R$ ${params.amount.toFixed(2)}`);
    console.log(`Description: ${params.description}`);
    console.log(`Expiration time: ${params.expirationTime} seconds`);
  } else {
    console.log(`\n🔄 Generating PIX QR code for R$ ${params.amount.toFixed(2)} - "${params.description}"...`);
  }

  try {
    // Prepare request payload
    const payload = {
      amount: params.amount,
      description: params.description,
      expiration_time: params.expirationTime,
      ip_address: '127.0.0.1',
      user_agent: 'PIX QR Code Test Script'
    };

    // Send request to API
    const response = await fetch(`${CONFIG.apiUrl}/qrcode`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': CONFIG.apiKey
      },
      body: JSON.stringify(payload)
    });

    // Process response
    if (!response.ok) {
      let errorText;
      try {
        const errorJson = await response.json();
        errorText = JSON.stringify(errorJson, null, 2);
      } catch (e) {
        errorText = await response.text();
      }

      throw new Error(`API Error: ${response.status} ${response.statusText}\n${errorText}`);
    }

    // Parse response data
    const data = await response.json();
    
    // Display results
    console.log('\n✅ QR Code generated successfully!');
    console.log(`\n📋 Transaction ID: ${data.txid}`);
    console.log(`⏱️  Expires in: ${params.expirationTime} seconds`);

    // Display PIX code for copying
    console.log('\n📱 PIX code for copying:');
    console.log('----------------------------------');
    console.log(data.qr_code_text);
    console.log('----------------------------------');

    // Save QR code image
    if (data.qr_code_image) {
      const imagePath = saveImage(data.qr_code_image, params.outputFile || 'pix-qrcode');
      if (imagePath) {
        console.log(`\n💾 QR code image saved to: ${imagePath}`);
      }
    }

    // Display additional information in verbose mode
    if (params.verbose) {
      console.log('\n📊 Complete API response:');
      console.log(JSON.stringify(data, null, 2));
    }

    return { success: true, data };
  } catch (error) {
    console.error(`\n❌ ERROR: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * Tests QR code generation with different amounts
 * @returns {Promise<Object>} Test results
 */
async function testMultipleAmounts() {
  console.log('\n🧪 TEST: Multiple Amount Values');
  console.log('============================');
  
  const amounts = [1.00, 10.50, 100.00, 1000.00];
  const results = [];
  
  for (const amount of amounts) {
    console.log(`\n🔄 Testing amount: R$ ${amount.toFixed(2)}`);
    
    try {
      const result = await generateQRCode({
        amount,
        description: `Test amount: R$ ${amount.toFixed(2)}`,
        expirationTime: 3600,
        verbose: false
      });
      
      results.push({ amount, success: result.success });
      
      if (result.success) {
        console.log(`✅ Amount R$ ${amount.toFixed(2)}: SUCCESS`);
      } else {
        console.log(`❌ Amount R$ ${amount.toFixed(2)}: FAILED`);
      }
    } catch (error) {
      results.push({ amount, success: false, error: error.message });
      console.log(`❌ Amount R$ ${amount.toFixed(2)}: ERROR - ${error.message}`);
    }
  }
  
  return results;
}

/**
 * Main function
 */
async function main() {
  console.log('🔍 PIX QR Code Generation Test');
  console.log('============================');
  console.log(`API URL: ${CONFIG.apiUrl}`);
  console.log(`Output directory: ${CONFIG.outputDir}`);
  
  // Parse command line arguments
  const params = parseArguments();
  
  // Show help if requested or if there are validation errors
  if (params.help) {
    showHelp();
    process.exit(0);
  }
  
  try {
    // Generate QR code with specified parameters
    const result = await generateQRCode(params);
    
    if (result.success) {
      // If the basic test succeeds, run additional tests with different amounts
      await testMultipleAmounts();
      
      console.log('\n🎉 All tests completed successfully!');
    } else {
      console.error('\n❌ Basic QR code generation test failed. Skipping additional tests.');
    }
  } catch (error) {
    console.error(`\n❌ Fatal error: ${error.message}`);
    process.exit(1);
  }
}

// Run the main function
main().catch(error => {
  console.error('\n❌ Unhandled error:', error);
  process.exit(1);
});
