#!/bin/bash
set -e

# Cores para saída
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 Verificando o estado atual do banco de dados Neon...${NC}"

# Carregar variáveis do arquivo .env se existir
if [ -f ".env" ]; then
  echo -e "${BLUE}🔄 Carregando variáveis de ambiente do arquivo .env...${NC}"
  export $(grep -v '^#' .env | xargs)
  echo -e "${GREEN}✅ Variáveis de ambiente carregadas com sucesso!${NC}"
else
  echo -e "${YELLOW}⚠️ Arquivo .env não encontrado. Usando variáveis de ambiente do sistema.${NC}"
fi

# Verificar se as variáveis necessárias estão definidas
if [ -z "$DB_CONNECTION_STRING" ]; then
  if [ -z "$DB_HOST" ] || [ -z "$DB_USER" ] || [ -z "$DB_PASSWORD" ] || [ -z "$DB_NAME" ]; then
    echo -e "${RED}❌ Variáveis de ambiente necessárias não definidas.${NC}"
    echo -e "${YELLOW}Defina DB_CONNECTION_STRING ou todas as variáveis: DB_HOST, DB_USER, DB_PASSWORD, DB_NAME${NC}"
    exit 1
  else
    # Construir string de conexão
    DB_CONNECTION_STRING="postgresql://$DB_USER:$DB_PASSWORD@$DB_HOST:${DB_PORT:-5432}/$DB_NAME?sslmode=require"
    echo -e "${YELLOW}⚠️ DB_CONNECTION_STRING não definida, usando string construída a partir de variáveis individuais.${NC}"
  fi
fi

echo -e "${BLUE}🔍 Usando string de conexão: ${DB_CONNECTION_STRING}${NC}"

# Verificar se o psql está instalado
if ! command -v psql &> /dev/null; then
  echo -e "${RED}❌ psql não está instalado. Por favor, instale o cliente PostgreSQL.${NC}"
  exit 1
fi

# Testar conexão com o banco de dados
echo -e "${BLUE}🔌 Testando conexão com o banco de dados Neon...${NC}"
if ! psql "$DB_CONNECTION_STRING" -c '\conninfo'; then
  echo -e "${RED}❌ Não foi possível conectar ao banco de dados Neon.${NC}"
  echo -e "${YELLOW}Verifique suas credenciais e se o banco está acessível.${NC}"
  exit 1
fi

echo -e "${GREEN}✅ Conexão com o banco de dados Neon estabelecida com sucesso!${NC}"

# Listar schemas
echo -e "${BLUE}📋 Listando schemas no banco de dados...${NC}"
psql "$DB_CONNECTION_STRING" -c '\dn'

# Verificar se o schema pixapi existe
echo -e "${BLUE}🔍 Verificando se o schema pixapi existe...${NC}"
SCHEMA_EXISTS=$(psql "$DB_CONNECTION_STRING" -t -c "SELECT EXISTS(SELECT 1 FROM information_schema.schemata WHERE schema_name = 'pixapi');" | tr -d '[:space:]')
if [ "$SCHEMA_EXISTS" = "t" ]; then
  echo -e "${GREEN}✅ Schema pixapi existe!${NC}"
else
  echo -e "${RED}❌ Schema pixapi não existe!${NC}"
  exit 1
fi

# Listar tabelas no schema pixapi
echo -e "${BLUE}📋 Listando tabelas no schema pixapi...${NC}"
psql "$DB_CONNECTION_STRING" -c '\dt pixapi.*'

# Verificar se a tabela schema_migrations existe
echo -e "${BLUE}🔍 Verificando se a tabela schema_migrations existe...${NC}"
TABLE_EXISTS=$(psql "$DB_CONNECTION_STRING" -t -c "SELECT EXISTS(SELECT 1 FROM information_schema.tables WHERE table_schema = 'pixapi' AND table_name = 'schema_migrations');" | tr -d '[:space:]')
if [ "$TABLE_EXISTS" = "t" ]; then
  echo -e "${GREEN}✅ Tabela schema_migrations existe!${NC}"
  
  # Listar migrações aplicadas
  echo -e "${BLUE}📋 Listando migrações aplicadas...${NC}"
  psql "$DB_CONNECTION_STRING" -c "SELECT * FROM pixapi.schema_migrations ORDER BY version;"
else
  echo -e "${RED}❌ Tabela schema_migrations não existe!${NC}"
fi

# Verificar o search_path atual
echo -e "${BLUE}🔍 Verificando o search_path atual...${NC}"
psql "$DB_CONNECTION_STRING" -c 'SHOW search_path;'

echo -e "${GREEN}✅ Verificação do banco de dados concluída!${NC}"
