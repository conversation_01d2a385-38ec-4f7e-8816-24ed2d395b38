# Guia de Implantação no Coolify para o PIX API

Este guia contém instruções para implantar corretamente o PIX API no Coolify, incluindo a configuração do banco de dados PostgreSQL e acesso ao NSQAdmin através do domínio personalizado.

## Passos para Implantação

### 1. Preparar Arquivos

Certifique-se de que você tem os seguintes arquivos no seu repositório:

- `docker-compose.yaml` - Configuração dos serviços
- `init-postgres.sh` - Script de inicialização do banco de dados (com permissão de execução)
- `infra.config.json` - Configuração da infraestrutura para o Encore

### 2. Limpar Volumes do PostgreSQL (⚠️ CRÍTICO ⚠️)

**ESTE PASSO É ABSOLUTAMENTE ESSENCIAL! SE VOCÊ NÃO LIMPAR O VOLUME, O BANCO NÃO SERÁ INICIALIZADO CORRETAMENTE!**

Antes de fazer o deploy, você DEVE limpar o volume do PostgreSQL para garantir que o banco seja inicializado corretamente:

1. Acesse o Coolify Dashboard
2. Vá para o serviço PostgreSQL (`postgres`)
3. Clique na seção "Volumes"
4. Encontre o volume `postgres_data`
5. Clique em "Delete" ou "Reset" para limpar completamente o volume
6. Confirme a operação

**Verificação:** Após iniciar os serviços, verifique nos logs do PostgreSQL se aparece a mensagem: "Inicialização do banco de dados PIX API concluída com sucesso!". Se você ver a mensagem "PostgreSQL Database directory appears to contain a database; Skipping initialization", isso significa que o volume NÃO foi limpo e o script de inicialização não foi executado.

### 3. Configurar o Domínio para NSQAdmin

Para acessar o NSQAdmin através de `https://nsqadmin.cloud.pluggou.io/`:

1. No dashboard do Coolify, vá para o serviço `nsqadmin`
2. Na seção "Networking" ou "Domains", adicione o domínio `nsqadmin.cloud.pluggou.io`
3. Certifique-se de que o domínio está configurado para usar HTTPS
4. Verifique se as labels para Traefik ou Caddy no `docker-compose.yaml` estão corretas para o seu ambiente Coolify

### 4. Garantir Variáveis de Ambiente Corretas

Certifique-se de que:

- `POSTGRES_PASSWORD=encore` está definido como variável de ambiente no serviço `pixapi`
- `DB_PASSWORD=encore` também está definido no serviço `pixapi`
- Todas as variáveis do Flow2Pay estão definidas, mesmo que com valores de placeholder

### 5. Implantar os Serviços

1. No Coolify, clique em "Deploy" para implantar a stack completa
2. Aguarde até que todos os serviços estejam em funcionamento
3. Verifique os logs do PostgreSQL para confirmar que as tabelas foram criadas
4. Verifique os logs do PIX API para confirmar que a conexão com o banco de dados foi estabelecida

### 6. Verificar o Acesso

- API PIX: `https://apipix.cloud.pluggou.io/`
- NSQAdmin: `https://nsqadmin.cloud.pluggou.io/`

## Resolução de Problemas

### Problema: Erro "environment variable not found"

Se você ver nos logs do PIX API o erro:
```
Error: failed to initialize runtime: unable to initialize sqldb proxy
Caused by:
    0: failed to parse SQL clusters
    1: failed to resolve password
    2: environment variable not found
```

**Solução:**
- Verifique se você definiu explicitamente `POSTGRES_PASSWORD=encore` nas variáveis de ambiente do serviço `pixapi`
- Certifique-se de que está usando exatamente o mesmo nome de variável de ambiente que está referenciado no arquivo `infra.config.json` (através da sintaxe `{"$env": "POSTGRES_PASSWORD"}`)

### Problema: Database "encore" not found

Se você ver nos logs do PostgreSQL `FATAL: database "encore" does not exist`, isso significa que:

1. O banco de dados `pix_db` não foi criado corretamente, ou
2. A aplicação Encore está tentando se conectar a um banco chamado `encore` em vez de `pix_db`

**Solução:**
- Verifique se o serviço PostgreSQL está configurado com `POSTGRES_DB: pix_db`
- Verifique se o script `init-postgres.sh` foi executado (procure nos logs do PostgreSQL)
- Confirme que o arquivo `infra.config.json` tem a configuração correta para `pix_db`
- Confirme que `pixapi/db.ts` usa `new SQLDatabase("pixdb", ...)`

### Problema: Relation "users" does not exist

Este erro significa que a tabela `users` não foi criada no schema correto.

**Solução:**
- Verifique os logs do PostgreSQL para confirmar que o script `init-postgres.sh` foi executado
- Verifique se o schema `pixapi` foi criado e as tabelas foram criadas neste schema
- Se necessário, conecte-se ao banco diretamente usando:
  ```
  docker exec -it pix-api-postgres psql -U encore -d pix_db -c "SELECT * FROM pixapi.users LIMIT 5;"
  ```

### Problema: NSQAdmin mostra "Bad Gateway"

Se você não conseguir acessar o NSQAdmin através do domínio configurado:

**Solução:**
- Verifique se o serviço `nsqadmin` está rodando (verificando os logs)
- Confirme que o domínio foi configurado corretamente no Coolify para o serviço `nsqadmin`
- Verifique as labels no `docker-compose.yaml` para garantir que estão corretas para o tipo de proxy que o Coolify usa (Traefik ou Caddy)
- Tente acessar diretamente pela porta 4171 (se estiver exposta no host): `http://IP_DO_SERVIDOR:4171`

## Variáveis de Ambiente Críticas

Estas variáveis são FUNDAMENTAIS para o funcionamento correto:

- `POSTGRES_PASSWORD`: Deve ser `encore` para corresponder à senha definida para o usuário PostgreSQL
- `DB_PASSWORD`: Deve também ser `encore`
- `API_BASE_URL`: URL base da API (exemplo: `https://apipix.cloud.pluggou.io`)
- `FLOW2PAY_CLIENT_ID`: ID do cliente Flow2Pay
- `FLOW2PAY_CLIENT_SECRET`: Secret do cliente Flow2Pay
- `FLOW2PAY_EVENT_TOKEN`: Token de eventos da Flow2Pay

## Manutenção

Para verificar os logs dos serviços:
1. Acesse o dashboard do Coolify
2. Selecione o serviço desejado
3. Clique em "Logs" para ver os logs em tempo real

Para reiniciar um serviço:
1. Acesse o dashboard do Coolify
2. Selecione o serviço desejado
3. Clique em "Restart" ou "Redeploy"

## Comando Para Verificar Estado do Banco de Dados

Você pode executar este comando no terminal do Coolify para verificar se as tabelas foram criadas corretamente:

```bash
docker exec -it pix-api-postgres psql -U encore -d pix_db -c "
SELECT table_name FROM information_schema.tables
WHERE table_schema = 'pixapi'
ORDER BY table_name;
"
```

Se as tabelas existirem, você verá uma lista como:
```
       table_name
------------------------
 event_logs
 pix_qrcodes
 pix_webhooks
 users
(4 rows)
```
