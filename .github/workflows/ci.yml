name: Continuous Integration

on:
  push:
    branches:
      - main # Or your default branch (e.g., master)
  pull_request:
    branches:
      - main # Or your default branch

jobs:
  lint-test-check:
    name: Lint, Test, and Check
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20' # Specify your project's Node.js version
          cache: 'npm' # Or 'yarn' if you use Yarn

      - name: Install dependencies
        run: npm ci # Or 'yarn install --frozen-lockfile' if you use Yarn
        # This assumes you have a package.json and package-lock.json (or yarn.lock)

      - name: Set up Encore CLI
        uses: encoredev/setup-encore@v1
        # You can specify a version if needed:
        # with:
        #   encore-version: '1.26.0'

      - name: Run Linter
        run: npm run lint
        # IMPORTANT: This step assumes you have a 'lint' script in your package.json.
        # If not, you'll need to create one (e.g., "lint": "eslint . --ext .ts")
        # or replace this command with your actual linting command.
        # If you don't have a linter configured, you can remove this step or set one up.

      - name: Run Tests
        run: npm run test
        # IMPORTANT: This step assumes you have a 'test' script in your package.json.
        # If not, you'll need to create one (e.g., "test": "jest")
        # or replace this command with your actual testing command.
        # If you don't have tests, you can remove this step or set them up.

      - name: Encore Application Check
        run: encore check
        # This command checks your Encore application for common issues and validity.
        # If your Encore app requires specific environment variables to pass 'encore check',
        # you might need to set them using the 'env' keyword for this step. For example:
        # env:
        #   SOME_REQUIRED_ENV_VAR: ${{ secrets.SOME_CI_SECRET }}
