name: CI/CD Pipeline

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Install Encore CLI
        run: curl -L https://encore.dev/install.sh | bash

      - name: Add Encore to PATH
        run: echo "$HOME/.encore/bin" >> $GITHUB_PATH

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push Docker image with Encore
        env:
          DB_PASSWORD: ${{ secrets.DB_PASSWORD }}
          FLOW2PAY_CLIENT_ID: ${{ secrets.FLOW2PAY_CLIENT_ID }}
          FLOW2PAY_CLIENT_SECRET: ${{ secrets.FLOW2PAY_CLIENT_SECRET }}
          FLOW2PAY_EVENT_TOKEN: ${{ secrets.FLOW2PAY_EVENT_TOKEN }}
          DB_HOST: localhost
          DB_PORT: 5432
          DB_NAME: postgres
          DB_USER: postgres
        run: |
          encore build docker --services=pixapi-simplified --config pixapi-simplified/encore.config.json pix-api

          # Criar Dockerfile temporário para adicionar script de inicialização
          cat > Dockerfile.temp << EOF
          FROM pix-api
          COPY pixapi-simplified/init-db.js /app/init-db.js
          COPY pixapi-simplified/entrypoint.sh /app/entrypoint.sh
          COPY pixapi-simplified/migrations /app/migrations
          RUN chmod +x /app/entrypoint.sh
          RUN npm install pg
          ENTRYPOINT ["/app/entrypoint.sh"]
          EOF

          # Construir imagem final
          docker build -t pix-api-custom -f Dockerfile.temp .

          # Taguear e enviar para o Docker Hub
          docker tag pix-api-custom ${{ secrets.DOCKERHUB_USERNAME }}/pix-api:latest
          docker push ${{ secrets.DOCKERHUB_USERNAME }}/pix-api:latest

  deploy:
    needs: build-and-push
    runs-on: ubuntu-latest
    steps:
      - name: Trigger Coolify Deployment
        uses: fjogeleit/http-request-action@v1
        with:
          url: ${{ secrets.COOLIFY_WEBHOOK_URL }}
          method: 'POST'
          bearerToken: ${{ secrets.COOLIFY_API_TOKEN }}
          preventFailureOnNoResponse: true
