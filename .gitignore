# Docs directory (contains example code that shouldn't be part of the build)
/_docs/

# <PERSON>ripts directory (contains utility scripts that shouldn't be part of the build)
/scripts/

# Node.js
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
package-lock.json
.npm

# Encore
.encore/

# Environment
.env
.env.local
# .env.*.local
.secrets.local.cue

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Editor directories and files
.idea/
.vscode/
*.sublime-workspace
*.sublime-project
.editorconfig
encore.gen.go
encore.gen.cue
/.encore
/encore.gen
