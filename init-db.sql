-- <PERSON><PERSON>r schema para aplicação PIX
CREATE SCHEMA IF NOT EXISTS pixapi;

-- <PERSON><PERSON><PERSON> de usuários
CREATE TABLE IF NOT EXISTS pixapi.users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de QR codes PIX
CREATE TABLE IF NOT EXISTS pixapi.pix_qrcodes (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES pixapi.users(id),
    qrcode_data TEXT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    expiration_time TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'PENDING',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON><PERSON><PERSON> para webhooks de PIX
CREATE TABLE IF NOT EXISTS pixapi.pix_webhooks (
    id SERIAL PRIMARY KEY,
    transaction_id VARCHAR(255) UNIQUE NOT NULL,
    qrcode_id INTEGER REFERENCES pixapi.pix_qrcodes(id),
    payload JSONB NOT NULL,
    status VARCHAR(20) NOT NULL,
    processed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Tabela para logs de eventos
CREATE TABLE IF NOT EXISTS pixapi.event_logs (
    id SERIAL PRIMARY KEY,
    event_type VARCHAR(50) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id VARCHAR(255) NOT NULL,
    data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Criar extensões úteis
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Inserir usuário de teste (opcional)
INSERT INTO pixapi.users (email, name)
VALUES ('<EMAIL>', 'Usuário de Teste')
ON CONFLICT (email) DO NOTHING;
